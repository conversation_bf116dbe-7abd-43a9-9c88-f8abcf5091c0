[MODE: PLAN]

基于我的深入研究，我将设计一个完整的统一订单处理控制台方案，按照实际操作流程重新组织所有功能。

## 统一订单处理控制台设计方案

### 第一部分：功能整合分析

**需要整合的功能模块：**
1. **Excel映射配置管理** - 从配置管理标签页迁移
2. **分类规则管理** - 从分散位置集中管理
3. **文件扫描功能** - 优化现有扫描逻辑
4. **Excel解析处理** - 整合现有解析功能
5. **批量处理功能** - 统一处理流程
6. **实时进度追踪** - 增强现有进度显示
7. **详细解析日志** - 新增日志展示功能

**实际操作流程排序：**
1. **步骤1：配置准备** - Excel映射配置 + 分类规则配置
2. **步骤2：文件扫描** - 扫描可处理文件
3. **步骤3：解析处理** - 执行Excel解析和分类
4. **步骤4：进度监控** - 实时进度 + 详细日志
5. **步骤5：结果查看** - 处理结果和汇总

### 第二部分：页面重构计划

**文件修改列表：**
- `app/templates/orders/orders_semi_auto.html` - 主要重构文件
- `app/static/js/custom/order_processing_console.js` - 新增专用JS文件
- `app/static/css/custom/order_processing_console.css` - 新增专用样式文件

**重构内容：**

#### 1. 移除冗余板块
- 删除原有分散的Excel解析配置区域
- 删除配置管理标签页中的Excel映射部分
- 整合所有功能到单一控制台

#### 2. 重新设计控制台布局
```
订单处理控制台
├── 第一步：配置管理区
│   ├── Excel映射配置（从配置管理迁移）
│   ├── 分类规则管理（集中管理）
│   └── 处理参数设置
├── 第二步：文件操作区
│   ├── 文件扫描
│   ├── 文件预览
│   └── 批量选择
├── 第三步：处理执行区
│   ├── 一键智能处理
│   ├── 分步执行选项
│   └── 处理控制按钮
├── 第四步：进度监控区
│   ├── 实时进度条
│   ├── 处理状态统计
│   └── 详细解析日志（新增）
└── 第五步：结果展示区
    ├── 分类汇总结果
    ├── 文件下载链接
    └── 错误报告
```

#### 3. 新增解析日志展示
- 实时显示解析过程的详细信息
- 包括文件处理状态、字段映射结果、分类决策过程
- 支持日志过滤和搜索功能

### 第三部分：具体实施计划

#### 阶段1：重构主页面结构（orders_semi_auto.html）
1. 清理现有冗余的Excel解析区域
2. 重新设计订单处理控制台布局
3. 按操作流程排序所有功能模块
4. 添加解析日志展示区域

#### 阶段2：迁移Excel映射配置功能
1. 从配置管理标签页提取Excel映射相关代码
2. 集成到第一步配置管理区
3. 优化配置界面的用户体验

#### 阶段3：集中分类规则管理
1. 整合分散的分类规则配置代码
2. 创建统一的分类规则管理界面
3. 支持可视化的规则配置

#### 阶段4：增强进度监控和日志
1. 扩展现有WebSocket进度推送功能
2. 新增详细解析日志的数据结构
3. 实现实时日志显示和过滤功能

#### 阶段5：优化JavaScript逻辑
1. 创建专用的order_processing_console.js文件
2. 重构现有的分散JavaScript代码
3. 实现统一的事件处理和状态管理

#### 阶段6：样式优化
1. 创建专用的CSS文件
2. 优化控制台的视觉设计
3. 确保响应式布局

### 第四部分：详细技术规范

#### 新增解析日志数据结构
```javascript
{
  timestamp: "2025-01-25 19:30:45",
  level: "INFO|WARN|ERROR",
  stage: "scan|parse|classify|save",
  file: "filename.xlsx",
  message: "详细消息",
  details: {
    // 具体处理细节
  }
}
```

#### 统一控制台状态管理
```javascript
const ProcessingState = {
  IDLE: 'idle',
  CONFIGURING: 'configuring', 
  SCANNING: 'scanning',
  PARSING: 'parsing',
  COMPLETED: 'completed',
  ERROR: 'error'
}
```

#### WebSocket事件扩展
- `parsing_log` - 解析日志事件
- `file_processing_start` - 文件开始处理
- `file_processing_complete` - 文件处理完成
- `classification_decision` - 分类决策事件

### 第五部分：实施清单

**实施清单：**

1. **备份现有文件**
   - 备份 `app/templates/orders/orders_semi_auto.html`
   - 备份相关JavaScript代码

2. **重构主页面结构**
   - 删除冗余的Excel解析配置区域（第572-769行）
   - 重新设计统一控制台布局
   - 按步骤排序功能模块

3. **迁移Excel映射配置**
   - 从配置管理标签页提取映射配置代码（第1454-1492行）
   - 集成到第一步配置管理区

4. **整合分类规则管理**
   - 集中现有分散的分类规则代码
   - 创建可视化配置界面

5. **新增解析日志展示**
   - 在进度监控区下方添加日志展示区域
   - 实现实时日志更新和过滤功能

6. **创建专用JavaScript文件**
   - `app/static/js/custom/order_processing_console.js`
   - 重构现有JavaScript逻辑

7. **创建专用样式文件**
   - `app/static/css/custom/order_processing_console.css`
   - 优化控制台视觉设计

8. **扩展WebSocket事件处理**
   - 在 `app/api/websocket.py` 中添加日志事件
   - 在 `app/services/order_processing_service.py` 中发送日志事件

9. **优化后端日志推送**
   - 在解析过程中推送详细日志
   - 确保日志的实时性和完整性

10. **测试和优化**
    - 测试所有功能的完整性
    - 优化用户体验和性能

**预期成果：**
- 统一、直观的订单处理控制台
- 按实际操作流程排序的功能布局
- 实时的解析进度和详细日志展示
- 更好的用户体验和操作效率

这个方案将彻底重构现有的分散功能，创建一个真正统一的订单处理控制台，大幅提升用户的操作体验。