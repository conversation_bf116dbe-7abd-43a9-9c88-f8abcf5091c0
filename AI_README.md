# AEC-FT AI Commander AI助手功能

**车规芯片终测智能调度平台AI功能模块**

车规芯片终测智能调度平台的AI助手功能基于火山引擎的deepseek-r1-distill-qwen-32b模型，为用户提供智能问答服务，帮助解决生产排程、订单管理、资源分配等方面的问题。

## 主要功能

- 智能问答：基于自然语言处理的智能对话
- 生产排程辅助：针对车规芯片终测调度的专业问题解答

## 功能介绍

APS系统的AI助手功能基于火山引擎的deepseek-r1-distill-qwen-32b模型，为用户提供智能问答服务，帮助解决生产排程、订单管理、资源分配等方面的问题。

主要功能包括：
- 标准对话：基本的问答功能
- 深度思考(R1)：处理更复杂的问题，提供更详细的分析
- 联网搜索：可以搜索网络获取最新信息（需配置搜索API）
- 生产排程辅助：针对APS系统的专业问题解答

## 配置说明

### 1. 安装依赖

确保已安装所需的Python库：

```bash
pip install -r requirements.txt
```

### 2. 配置API密钥

1. 复制`.env.example`文件为`.env`：

```bash
cp .env.example .env
```

2. 编辑`.env`文件，填入您的火山引擎API密钥：

```
ARK_API_KEY=your_volc_api_key_here
```

如需使用联网搜索功能，还需配置必应搜索API密钥：

```
BING_API_KEY=your_bing_api_key_here
```

### 3. 添加AI助手菜单项

首次使用前，管理员需要将AI助手添加到系统菜单中：

1. 登录系统，使用管理员账号
2. 访问以下URL：`http://您的系统域名/add-ai-menu`
3. 系统将显示添加成功的消息

## 使用说明

### 访问AI助手

1. 登录系统
2. 在左侧导航菜单中，找到"系统管理"选项并点击展开
3. 在展开的子菜单中，找到并点击"AI助手"选项

### 使用AI助手

在AI助手页面，您可以：

1. **选择对话模式**：
   - 点击页面上的模式按钮切换不同的对话模式

2. **发送问题**：
   - 在底部输入框中输入您的问题
   - 点击发送按钮或按Enter键发送

3. **查看历史对话**：
   - 左侧面板显示您的历史对话
   - 点击任何历史对话可以继续之前的对话

4. **创建新对话**：
   - 点击右上角的"新对话"按钮开始一个全新的对话

5. **删除对话历史**：
   - 点击左上角的垃圾桶图标可以删除所有对话历史
   - 每个对话右侧也有删除按钮，可以删除单个对话

## 本地模拟模式

如果未配置API密钥，系统将自动使用本地模拟模式，提供基本的预设回复。在AI助手信息卡片中会显示"模拟模式"标识。

## 故障排除

如果遇到问题，请检查：

1. API密钥是否正确配置
2. 网络连接是否正常
3. 系统日志中是否有相关错误信息

如需进一步帮助，请联系系统管理员。

## 2025.5.29 更新 - 自动排产功能优化

### 完成的优化工作

1. **修复了手动排产页面的自动排产功能跳转**
   - 添加了缺失的 `autoSchedule()` 函数
   - 实现了排产规则的传递功能
   - 支持从手动排产页面选择规则后跳转到自动排产页面

2. **优化了自动排产页面的JavaScript功能**
   - 修复了按钮事件绑定问题
   - 改进了错误处理机制
   - 添加了详细的控制台日志输出
   - 确保 `addLog` 函数在页面加载前定义

3. **支持MySQL数据库**
   - 更新了 `fix_auto_db.py` 脚本，支持MySQL和SQLite两种数据库
   - 根据系统配置自动选择合适的数据库类型
   - 创建了必要的数据库表结构

### 使用说明

1. **初始化数据库**
   ```bash
   python fix_auto_db.py
   ```

2. **访问自动排产功能**
   - 直接访问：http://localhost:5000/production/auto
   - 从手动排产页面跳转：选择排产规则后点击"确定切换排产规则"

### 注意事项

1. 确保MySQL服务正在运行（如果使用MySQL数据库）
2. 检查 `instance/db_config.json` 文件中的数据库配置
3. 如果页面按钮仍然无法使用，请查看浏览器控制台的错误信息
4. XLSX导出功能需要网络连接来加载必要的JavaScript库

### 已知问题

1. 某些数据库表可能已存在但字段名不匹配，需要手动调整
2. 如果使用CDN加载SweetAlert2失败，会回退到原生alert
3. Excel导出功能依赖于网络连接加载XLSX库 