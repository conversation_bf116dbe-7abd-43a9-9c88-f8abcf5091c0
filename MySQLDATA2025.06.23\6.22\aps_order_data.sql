-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `order_data`
--

DROP TABLE IF EXISTS `order_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `document_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单据类型 (如: 封测外包加工单)',
  `document_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单据编号',
  `processing_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '加工属性 (如: 测试-编带)',
  `order_date` date DEFAULT NULL COMMENT '下单日期',
  `contractor_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '加工承揽商 (如: 无锡市宜欣科技有限公司)',
  `contractor_contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '承揽商联系人',
  `contractor_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '承揽商地址',
  `contractor_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '承揽商电话',
  `contractor_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '承揽商Email',
  `client_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '加工委托方',
  `order_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号 (如: JHT2506190001)',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签名称/产品名称 (如: JW5116FESOP#TRPBF)',
  `circuit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电路名称 (如: JW5116F)',
  `chip_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '芯片名称',
  `wafer_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '圆片尺寸 (如: 8)',
  `package_qty` int DEFAULT NULL COMMENT '送包只数',
  `package_pieces` int DEFAULT NULL COMMENT '送包片数',
  `diffusion_batch` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩散批号',
  `wafer_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '片号',
  `assembly_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '装片方式',
  `drawing_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图号',
  `package_form` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '封装形式',
  `stamp_line1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '印章第一行',
  `stamp_line2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '印章第二行',
  `stamp_line3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '印章第三行',
  `other_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '其他说明',
  `delivery_date` date DEFAULT NULL COMMENT '交期',
  `env_requirement` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品环保要求',
  `msl_requirement` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MSL要求',
  `reliability_requirement` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '可靠性要求',
  `print_pin_dot` tinyint(1) DEFAULT '0' COMMENT '是否打印pin点',
  `pin_dot_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'pin点位置',
  `item_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Item Code（ITEM编码）',
  `shipping_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '出货地址',
  `wafer_lot` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签 wafer lot',
  `order_attribute` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单属性',
  `lot_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Lot Type原始值 (如: 量产-P, 试验-E, 小批量-PE)',
  `classification` enum('engineering','production','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'unknown' COMMENT '用户选择的分类(工程/量产)',
  `wafer_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Wafer ID',
  `customer` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户名称 (从承揽商或委托方提取)',
  `product_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品编码 (从电路名称或产品名称提取)',
  `quantity` int DEFAULT NULL COMMENT '数量 (从送包片数或其他数量字段提取)',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '总价',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'new' COMMENT '订单状态',
  `urgent` tinyint(1) DEFAULT '0' COMMENT '是否紧急',
  `owner` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '负责人',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `source_file` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据来源文件 (Excel文件名)',
  `raw_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '原始数据JSON (包含所有扫描信息)',
  `horizontal_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '横向信息JSON',
  `vertical_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '纵向表格数据JSON',
  `data_row_number` int DEFAULT NULL COMMENT '在Excel中的数据行号',
  `extraction_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'enhanced_parser' COMMENT '提取方法',
  `extraction_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '提取过程信息',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `imported_at` timestamp NULL DEFAULT NULL COMMENT '导入时间',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_lot_type` (`lot_type`),
  KEY `idx_classification` (`classification`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_order_date` (`order_date`),
  KEY `idx_source_file` (`source_file`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_circuit_name` (`circuit_name`),
  KEY `idx_wafer_id` (`wafer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='完整的订单数据表-包含横向和纵向所有信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_data`
--

LOCK TABLES `order_data` WRITE;
/*!40000 ALTER TABLE `order_data` DISABLE KEYS */;
INSERT INTO `order_data` VALUES (58,'封测外包加工单','JW25060529','测试-编带',NULL,'无锡市宜欣科技有限公司','陈九嘉','宜兴市经济开发区杏里路10号光电产业园5幢102室','15705101359','<EMAIL>','','JHT2506190001','JW5116FESOP#TRPBF','JW5116F','JP14901F','8',363222,NULL,'FA54-6628','','','','ESOP8',NULL,NULL,NULL,NULL,'2025-07-03',NULL,NULL,NULL,0,NULL,'','',NULL,'','量产-P','production','FA54-6628','无锡市宜欣科技有限公司','JW5116F',363222,NULL,NULL,'imported',0,NULL,NULL,'宜欣  生产订单模板(新封装-测试-编带)2025061901  JW5116F_f9a4e01e.xls','{\"送包只数\": \"363222\", \"送包片数\": \"\", \"订单号\": \"JHT2506190001\", \"Lot Type\": \"量产-P\", \"产品名称\": \"JW5116FESOP#TRPBF\", \"电路名称\": \"JW5116F\", \"芯片名称\": \"JP14901F\", \"Wafer ID\": \"FA54-6628\", \"圆片尺寸\": \"8\", \"扩散批号\": \"FA54-6628\", \"片号\": \"\", \"装片方式\": \"\", \"封装形式\": \"ESOP8\", \"图号\": \"\", \"交期\": \"2025-07-03\", \"Item Code\": \"\", \"出货地址\": \"\", \"wafer lot\": \"FA54-6628\", \"订单属性\": \"\", \"源文件\": \"\", \"导入时间\": \"2025-06-22 17:03:18\", \"数据行号\": 15, \"分类结果\": \"量产\", \"单据类型\": \"封测外包加工单\", \"单据编号\": \"JW25060529\", \"加工属性\": \"测试-编带\", \"下单日期\": \"2025-06-19\", \"加工承揽商\": \"无锡市宜欣科技有限公司\", \"承揽商联系人\": \"陈九嘉\", \"承揽商地址\": \"宜兴市经济开发区杏里路10号光电产业园5幢102室\", \"承揽商电话\": \"15705101359\", \"承揽商Email\": \"<EMAIL>\", \"提取时间\": \"2025-06-22 17:03:18\", \"信息来源\": \"横向布局区域\", \"提取方法\": \"horizontal_extractor\", \"加工委托方\": \"\"}',NULL,NULL,15,'horizontal_extractor',NULL,'test_system','2025-06-22 01:03:19','2025-06-22 01:03:19','2025-06-22 09:03:19','2025-06-22 09:03:19'),(59,'封测外包加工单','JW25060529','测试-编带',NULL,'无锡市宜欣科技有限公司','陈九嘉','宜兴市经济开发区杏里路10号光电产业园5幢102室','15705101359','<EMAIL>','','JHT2506190002','JW5116FESOP#TRPBF','JW5116F','JP14901F','8',363107,NULL,'FA54-6708','','','','ESOP8',NULL,NULL,NULL,NULL,'2025-07-03',NULL,NULL,NULL,0,NULL,'','',NULL,'','量产-P','production','FA54-6708','无锡市宜欣科技有限公司','JW5116F',363107,NULL,NULL,'imported',0,NULL,NULL,'宜欣  生产订单模板(新封装-测试-编带)2025061901  JW5116F_f9a4e01e.xls','{\"送包只数\": \"363107\", \"送包片数\": \"\", \"订单号\": \"JHT2506190002\", \"Lot Type\": \"量产-P\", \"产品名称\": \"JW5116FESOP#TRPBF\", \"电路名称\": \"JW5116F\", \"芯片名称\": \"JP14901F\", \"Wafer ID\": \"FA54-6708\", \"圆片尺寸\": \"8\", \"扩散批号\": \"FA54-6708\", \"片号\": \"\", \"装片方式\": \"\", \"封装形式\": \"ESOP8\", \"图号\": \"\", \"交期\": \"2025-07-03\", \"Item Code\": \"\", \"出货地址\": \"\", \"wafer lot\": \"FA54-6708\", \"订单属性\": \"\", \"源文件\": \"\", \"导入时间\": \"2025-06-22 17:03:18\", \"数据行号\": 16, \"分类结果\": \"量产\", \"单据类型\": \"封测外包加工单\", \"单据编号\": \"JW25060529\", \"加工属性\": \"测试-编带\", \"下单日期\": \"2025-06-19\", \"加工承揽商\": \"无锡市宜欣科技有限公司\", \"承揽商联系人\": \"陈九嘉\", \"承揽商地址\": \"宜兴市经济开发区杏里路10号光电产业园5幢102室\", \"承揽商电话\": \"15705101359\", \"承揽商Email\": \"<EMAIL>\", \"提取时间\": \"2025-06-22 17:03:18\", \"信息来源\": \"横向布局区域\", \"提取方法\": \"horizontal_extractor\", \"加工委托方\": \"\"}',NULL,NULL,16,'horizontal_extractor',NULL,'test_system','2025-06-22 01:03:19','2025-06-22 01:03:19','2025-06-22 09:03:19','2025-06-22 09:03:19'),(60,'封测外包加工单','JW25060529','测试-编带',NULL,'无锡市宜欣科技有限公司','陈九嘉','宜兴市经济开发区杏里路10号光电产业园5幢102室','15705101359','<EMAIL>','','JHT2506190003','JW5116FESOP#TRPBF','JW5116F','JP14901F','8',363093,NULL,'FA54-6709','','','','ESOP8',NULL,NULL,NULL,NULL,'2025-07-03',NULL,NULL,NULL,0,NULL,'','',NULL,'','量产-P','production','FA54-6709','无锡市宜欣科技有限公司','JW5116F',363093,NULL,NULL,'imported',0,NULL,NULL,'宜欣  生产订单模板(新封装-测试-编带)2025061901  JW5116F_f9a4e01e.xls','{\"送包只数\": \"363093\", \"送包片数\": \"\", \"订单号\": \"JHT2506190003\", \"Lot Type\": \"量产-P\", \"产品名称\": \"JW5116FESOP#TRPBF\", \"电路名称\": \"JW5116F\", \"芯片名称\": \"JP14901F\", \"Wafer ID\": \"FA54-6709\", \"圆片尺寸\": \"8\", \"扩散批号\": \"FA54-6709\", \"片号\": \"\", \"装片方式\": \"\", \"封装形式\": \"ESOP8\", \"图号\": \"\", \"交期\": \"2025-07-03\", \"Item Code\": \"\", \"出货地址\": \"\", \"wafer lot\": \"FA54-6709\", \"订单属性\": \"\", \"源文件\": \"\", \"导入时间\": \"2025-06-22 17:03:18\", \"数据行号\": 17, \"分类结果\": \"量产\", \"单据类型\": \"封测外包加工单\", \"单据编号\": \"JW25060529\", \"加工属性\": \"测试-编带\", \"下单日期\": \"2025-06-19\", \"加工承揽商\": \"无锡市宜欣科技有限公司\", \"承揽商联系人\": \"陈九嘉\", \"承揽商地址\": \"宜兴市经济开发区杏里路10号光电产业园5幢102室\", \"承揽商电话\": \"15705101359\", \"承揽商Email\": \"<EMAIL>\", \"提取时间\": \"2025-06-22 17:03:18\", \"信息来源\": \"横向布局区域\", \"提取方法\": \"horizontal_extractor\", \"加工委托方\": \"\"}',NULL,NULL,17,'horizontal_extractor',NULL,'test_system','2025-06-22 01:03:19','2025-06-22 01:03:19','2025-06-22 09:03:19','2025-06-22 09:03:19');
/*!40000 ALTER TABLE `order_data` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-23 21:45:45
