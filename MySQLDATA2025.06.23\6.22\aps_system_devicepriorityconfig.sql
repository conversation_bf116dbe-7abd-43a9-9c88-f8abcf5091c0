-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `devicepriorityconfig`
--

DROP TABLE IF EXISTS `devicepriorityconfig`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `devicepriorityconfig` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `priority` int NOT NULL DEFAULT '5' COMMENT '优先级(1-10，数字越小优先级越高)',
  `from_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `refresh_time` datetime DEFAULT NULL COMMENT '刷新时间',
  `user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device` (`device`),
  KEY `idx_priority` (`priority`),
  KEY `idx_from_time` (`from_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品优先级配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devicepriorityconfig`
--

LOCK TABLES `devicepriorityconfig` WRITE;
/*!40000 ALTER TABLE `devicepriorityconfig` DISABLE KEYS */;
INSERT INTO `devicepriorityconfig` VALUES (7,'JW3130-EWDGBB#TR',1,NULL,NULL,NULL,'admin','2025-06-16 23:25:44','2025-06-16 23:25:44'),(8,'JWQ5217DFND-M001_TR1',2,NULL,NULL,NULL,'admin','2025-06-16 23:25:44','2025-06-16 23:25:44'),(9,'JWQ5217DFND-M001_TR1',0,NULL,NULL,NULL,'admin','2025-06-17 00:22:06','2025-06-17 00:22:06'),(10,'JW3130-EWDGBB#TR',1,NULL,NULL,NULL,'admin','2025-06-17 00:22:06','2025-06-17 00:22:06'),(11,'JWQ5217DFND-M001_TR1',2,NULL,NULL,NULL,'admin','2025-06-17 00:22:06','2025-06-17 00:22:06'),(17,'JWQ5217DFND-M001_TR1',0,NULL,NULL,NULL,'admin','2025-06-17 05:31:23','2025-06-17 05:31:23'),(18,'JW3130-EWDGBB#TR',1,NULL,NULL,NULL,'admin','2025-06-17 05:31:23','2025-06-17 05:31:23'),(19,'JWQ5217DFND-M001_TR1',2,NULL,NULL,NULL,'admin','2025-06-17 05:31:23','2025-06-17 05:31:23'),(20,'JWQ5217DFND-M001_TR1',0,NULL,NULL,NULL,'admin','2025-06-22 18:02:23','2025-06-22 18:02:23'),(21,'JW3130-EWDGBB#TR',1,NULL,NULL,NULL,'admin','2025-06-22 18:02:23','2025-06-22 18:02:23'),(22,'JWQ5217DFND-M001_TR1',2,NULL,NULL,NULL,'admin','2025-06-22 18:02:23','2025-06-22 18:02:23'),(23,'JWQ5217DFND-M001_TR1',0,NULL,NULL,NULL,'admin','2025-06-23 04:10:02','2025-06-23 04:10:02'),(24,'JW3130-EWDGBB#TR',1,NULL,NULL,NULL,'admin','2025-06-23 04:10:02','2025-06-23 04:10:02'),(25,'JWQ5217DFND-M001_TR1',2,NULL,NULL,NULL,'admin','2025-06-23 04:10:02','2025-06-23 04:10:02');
/*!40000 ALTER TABLE `devicepriorityconfig` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-23 21:45:50
