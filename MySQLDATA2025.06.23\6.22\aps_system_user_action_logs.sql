-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `user_action_logs`
--

DROP TABLE IF EXISTS `user_action_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_action_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_action_logs`
--

LOCK TABLES `user_action_logs` WRITE;
/*!40000 ALTER TABLE `user_action_logs` DISABLE KEYS */;
INSERT INTO `user_action_logs` VALUES (1,'system','test','database_fix','fix_script','数据库修复脚本测试','127.0.0.1',NULL,'2025-06-20 05:59:05'),(2,'system','test','database_fix','quick_fix','快速修复脚本测试','127.0.0.1',NULL,NULL),(3,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-06-20 06:00:41'),(4,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 你是谁？..., 模式: database','127.0.0.1',NULL,'2025-06-20 06:00:59'),(5,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-20T07:29:17.156762\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-20 07:29:17'),(6,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-06-20 07:29:21'),(7,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们的封装项目如何？限定50个字之内回答我..., 模式: database','127.0.0.1',NULL,'2025-06-20 07:30:09'),(8,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T02:55:54.844998\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 02:55:55'),(9,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:02:12'),(10,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:15:50'),(11,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:15:54'),(12,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:16:04'),(13,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:22:30'),(14,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T03:57:52.971470\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 03:57:53'),(15,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:12:17'),(16,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T04:44:25.671807\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:44:26'),(17,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T04:45:02.110122\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:45:02'),(18,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T04:57:10.554617\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 04:57:11'),(19,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T05:11:39.917309\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 05:11:40'),(20,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T05:46:45.607300\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 05:46:46'),(21,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T08:47:11.168374\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 08:47:11'),(22,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T08:53:38.629626\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 08:53:39'),(23,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T09:04:20.068508\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 09:04:20'),(24,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T09:13:59.798082\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 09:14:00'),(25,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T10:23:15.256864\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 10:23:15'),(26,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T11:20:15.829421\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 11:20:16'),(27,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T11:32:35.398743\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 11:32:35'),(28,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 11:40:58'),(29,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:00:41.320718\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:00:41'),(30,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:07:24.667569\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:07:25'),(31,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','python-requests/2.31.0','2025-06-22 12:07:25'),(32,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','python-requests/2.31.0','2025-06-22 12:07:25'),(33,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:10:08'),(34,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:10:35'),(35,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:11:02'),(36,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:28:00.673566\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 12:28:01'),(37,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:29:33.531636\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:29:34'),(38,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:29:55.526528\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:29:56'),(39,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T12:30:11.433972\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 12:30:11'),(40,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:02:37.463708\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:02:37'),(41,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:27:49.194527\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:27:49'),(42,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:45:30.756087\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:45:31'),(43,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T13:54:58.664880\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 13:54:59'),(44,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 14:03:40'),(45,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T14:11:50.645286\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 14:11:51'),(46,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 14:39:33'),(47,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:07:07'),(48,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T15:25:30.927911\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:25:31'),(49,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:27:03'),(50,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:30:37'),(51,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 15:31:09'),(52,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T15:42:27.890611\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 15:42:28'),(53,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T15:43:31.023408\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 15:43:31'),(54,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:06:20.772339\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 16:06:21'),(55,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:11:53.960768\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:11:54'),(56,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:17:31.799489\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:17:32'),(57,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:19:38.424903\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 16:19:38'),(58,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:19:45.341038\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-22 16:19:45'),(59,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:20:33.751276\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:20:34'),(60,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:24:58.742760\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:24:59'),(61,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:25:30.325344\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:25:30'),(62,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:25:52.333122\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:25:52'),(63,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:28:31.587932\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:28:32'),(64,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T16:50:45.128748\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 16:50:45'),(65,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-22T17:00:28.920183\"}','127.0.0.1','python-requests/2.31.0','2025-06-22 17:00:29'),(66,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T02:32:52.027529\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 02:32:52'),(67,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T02:32:57.391815\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 02:32:57'),(68,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T02:33:47.806436\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 02:33:48'),(69,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T03:04:39.602611\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 03:04:40'),(70,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T05:08:56.202184\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 05:08:56'),(71,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T08:20:26.447056\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 08:20:26'),(72,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:11:33.025122\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:11:33'),(73,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:12:02.147448\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:12:02'),(74,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:15:16.471532\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:15:16'),(75,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:16:04.065219\"}','127.0.0.1','python-requests/2.32.3','2025-06-23 09:16:04'),(76,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T09:45:46.633356\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-23 09:45:47'),(77,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T12:01:51.115960\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-23 12:01:51'),(78,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-23T12:57:09.946598\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-23 12:57:10');
/*!40000 ALTER TABLE `user_action_logs` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-23 21:45:50
