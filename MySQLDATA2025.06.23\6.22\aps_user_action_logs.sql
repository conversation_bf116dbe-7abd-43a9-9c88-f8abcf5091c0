-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `user_action_logs`
--

DROP TABLE IF EXISTS `user_action_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_action_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_action_logs_username` (`username`),
  KEY `idx_user_action_logs_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=523 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_action_logs`
--

LOCK TABLES `user_action_logs` WRITE;
/*!40000 ALTER TABLE `user_action_logs` DISABLE KEYS */;
INSERT INTO `user_action_logs` VALUES (1,'boss','login','user','boss','{\"success\": true, \"timestamp\": \"2025-03-14T10:18:59.720895\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-14 10:19:00'),(2,'boss','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 连上了吗？今天几号？..., 模式: standard','127.0.0.1',NULL,'2025-03-14 10:19:10'),(3,'boss','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 封测流程是什么..., 模式: standard','127.0.0.1',NULL,'2025-03-14 10:19:25'),(4,'boss','login','user','boss','{\"success\": true, \"timestamp\": \"2025-03-14T10:20:13.071034\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-14 10:20:13'),(5,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-16T07:49:32.717169\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','2025-03-16 07:49:33'),(6,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-16T09:15:00.066242\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','2025-03-16 09:15:00'),(7,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-16T12:44:45.050610\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','2025-03-16 12:44:45'),(8,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-16T14:23:52.475345\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','2025-03-16 14:23:52'),(9,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-17T13:33:16.300894\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','2025-03-17 13:33:16'),(10,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-18T13:25:51.277557\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','2025-03-18 13:25:51'),(11,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-18T14:51:35.290948\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-18 14:51:35'),(12,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-19T01:17:03.674572\"}','10.16.48.21','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-19 01:17:04'),(13,'admin','view','User',NULL,'查看用户列表，共3条记录',NULL,NULL,'2025-03-19 01:17:11'),(14,'admin','create','User','bonnie','创建用户成功，用户名: bonnie, 角色: op',NULL,NULL,'2025-03-19 01:17:46'),(15,'admin','view','User',NULL,'查看用户列表，共4条记录',NULL,NULL,'2025-03-19 01:17:46'),(16,'admin','view','User','bonnie','查看用户 bonnie 的权限',NULL,NULL,'2025-03-19 01:17:51'),(17,'admin','update','User','bonnie','更新用户 bonnie 的权限',NULL,NULL,'2025-03-19 01:18:22'),(18,'admin','view','User',NULL,'查看用户列表，共4条记录',NULL,NULL,'2025-03-19 01:18:22'),(19,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-03-19T01:18:29.183664\"}','10.16.48.21','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-19 01:18:29'),(20,'bonnie','login','user','bonnie','{\"success\": true, \"timestamp\": \"2025-03-19T01:18:34.604210\"}','10.16.48.21','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-19 01:18:35'),(21,'planer1','login','user','planer1','{\"success\": true, \"timestamp\": \"2025-03-19T03:02:01.310369\"}','10.16.48.211','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-19 03:02:01'),(22,'bonnie','login','user','bonnie','{\"success\": true, \"timestamp\": \"2025-03-19T07:01:15.296640\"}','10.16.48.21','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-19 07:01:15'),(23,'bonnie','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 你好  关于订单管理到生产排产的逻辑是什么？..., 模式: standard','10.16.48.21',NULL,'2025-03-19 07:02:16'),(24,'planer1','login','user','planer1','{\"success\": true, \"timestamp\": \"2025-03-19T10:11:04.678589\"}','10.16.10.41','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0','2025-03-19 10:11:05'),(25,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-20T07:01:46.912915\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:01:47'),(26,'admin','view','User',NULL,'查看用户列表，共4条记录',NULL,NULL,'2025-03-20 07:01:55'),(27,'admin','create','User','River','创建用户成功，用户名: River, 角色: op',NULL,NULL,'2025-03-20 07:02:23'),(28,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-20 07:02:23'),(29,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-03-20T07:02:29.356010\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:02:29'),(30,'River','login','user','River','{\"success\": true, \"timestamp\": \"2025-03-20T07:02:42.072223\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:02:42'),(31,'River','logout','user','River','{\"success\": true, \"timestamp\": \"2025-03-20T07:02:54.933459\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:02:55'),(32,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-20T07:03:05.230759\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:03:05'),(33,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-20 07:03:09'),(34,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-20 07:03:11'),(35,'admin','update','User','River','更新用户 River 的权限',NULL,NULL,'2025-03-20 07:03:25'),(36,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-20 07:03:25'),(37,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-03-20T07:03:31.872620\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:03:32'),(38,'River','login','user','River','{\"success\": true, \"timestamp\": \"2025-03-20T07:03:35.276557\"}','10.16.48.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','2025-03-20 07:03:35'),(39,'planer1','login','user','planer1','{\"success\": true, \"timestamp\": \"2025-03-20T07:16:51.802273\"}','10.16.10.41','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0','2025-03-20 07:16:52'),(40,'planer1','login','user','planer1','{\"success\": true, \"timestamp\": \"2025-03-26T07:42:51.118588\"}','10.16.48.211','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-03-26 07:42:51'),(41,'planer1','login','user','planer1','{\"success\": true, \"timestamp\": \"2025-03-31T01:37:26.249277\"}','10.16.10.41','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0','2025-03-31 01:37:26'),(42,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:05:08'),(43,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: ？..., 模式: standard','127.0.0.1',NULL,'2025-03-31 09:05:17'),(44,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-03-31 09:05:46'),(45,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 你是谁？》..., 模式: r1','127.0.0.1',NULL,'2025-03-31 09:06:58'),(46,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:13:10.483843\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:13:10'),(47,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:24:48.270584\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:24:48'),(48,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:27:42.692329\"}','192.168.3.122','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:27:43'),(49,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:29:55.758627\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:29:56'),(50,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:30:38.742535\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:30:39'),(51,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:32:37'),(52,'admin','view','User','boss','查看用户 boss 的权限',NULL,NULL,'2025-03-31 09:32:52'),(53,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:37:46.026569\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:37:46'),(54,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:38:06'),(55,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 09:38:07'),(56,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:40:56'),(57,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 09:40:57'),(58,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 09:41:03'),(59,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:45:48'),(60,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:45:49'),(61,'admin','view','User','bonnie','查看用户 bonnie 的权限',NULL,NULL,'2025-03-31 09:45:55'),(62,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-31 09:45:58'),(63,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:51:25.700432\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:51:26'),(64,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:51:36'),(65,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:51:40'),(66,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 09:51:41'),(67,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:51:46'),(68,'admin','view','User','boss','查看用户 boss 的权限',NULL,NULL,'2025-03-31 09:51:51'),(69,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-31 09:52:02'),(70,'admin','view','User','bonnie','查看用户 bonnie 的权限',NULL,NULL,'2025-03-31 09:52:15'),(71,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 09:52:22'),(72,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:52:25'),(73,'admin','update','User','planer1','更新用户权限失败，缺少权限数据',NULL,NULL,'2025-03-31 09:52:38'),(74,'admin','update','User','planer1','更新用户权限失败，缺少权限数据',NULL,NULL,'2025-03-31 09:52:42'),(75,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:56:37'),(76,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:56:41'),(77,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T09:56:54.406091\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 09:56:54'),(78,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:57:00'),(79,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:57:02'),(80,'admin','update','User','planer1','更新用户权限失败，缺少权限数据',NULL,NULL,'2025-03-31 09:57:07'),(81,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:59:04'),(82,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:59:05'),(83,'admin','update','User','planer1','更新用户 planer1 的权限',NULL,NULL,'2025-03-31 09:59:08'),(84,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:59:10'),(85,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:59:11'),(86,'admin','update','User','planer1','更新用户 planer1 的权限',NULL,NULL,'2025-03-31 09:59:15'),(87,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:59:17'),(88,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 09:59:18'),(89,'admin','view','User','boss','查看用户 boss 的权限',NULL,NULL,'2025-03-31 09:59:20'),(90,'admin','update','User','boss','更新用户 boss 的权限',NULL,NULL,'2025-03-31 09:59:39'),(91,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 09:59:41'),(92,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:00:04'),(93,'admin','view','User','bonnie','查看用户 bonnie 的权限',NULL,NULL,'2025-03-31 10:00:06'),(94,'admin','update','User','bonnie','更新用户 bonnie 的权限',NULL,NULL,'2025-03-31 10:00:22'),(95,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:00:23'),(96,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-31 10:00:25'),(97,'admin','update','User','River','更新用户 River 的权限',NULL,NULL,'2025-03-31 10:00:33'),(98,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:00:34'),(99,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:00:36'),(100,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-31 10:00:38'),(101,'admin','create','User','Da','创建用户成功，用户名: Da, 角色: op',NULL,NULL,'2025-03-31 10:01:01'),(102,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-03-31 10:01:02'),(103,'admin','view','User','Da','查看用户 Da 的权限',NULL,NULL,'2025-03-31 10:01:04'),(104,'admin','update','User','Da','更新用户 Da 的权限',NULL,NULL,'2025-03-31 10:01:15'),(105,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-03-31 10:01:16'),(106,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-03-31 10:01:18'),(107,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T10:01:19.189115\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 10:01:19'),(108,NULL,'login','user','Da','{\"success\": true, \"timestamp\": \"2025-03-31T10:01:22.843157\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 10:01:23'),(109,NULL,'AI_CHAT','AI_Assistant',NULL,'用户发送消息: ?..., 模式: standard','127.0.0.1',NULL,'2025-03-31 10:01:27'),(110,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T10:02:27.528835\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 10:02:28'),(111,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-03-31 10:05:00'),(112,'admin','delete','User','Da','删除用户成功，用户名: Da, 角色: op',NULL,NULL,'2025-03-31 10:05:04'),(113,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:05:04'),(114,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-31 10:05:05'),(115,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:09:06'),(116,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 10:09:07'),(117,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 10:09:15'),(118,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-03-31 10:09:30'),(119,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 10:10:23'),(120,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-03-31T10:16:27.727553\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-03-31 10:16:28'),(121,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-03-31 10:16:31'),(122,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-03-31 10:16:32'),(123,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-03-31 10:16:37'),(124,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-02T05:28:18.716627\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-04-02 05:28:19'),(125,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-04-02 05:28:58'),(126,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 作为一名计划部的负责人，我应该如何做好这个工作？..., 模式: r1','127.0.0.1',NULL,'2025-04-02 05:29:50'),(127,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 深入讲一下数据资产化管理的方面..., 模式: r1','127.0.0.1',NULL,'2025-04-02 05:32:22'),(128,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-02T08:50:25.466814\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-04-02 08:50:25'),(129,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-03T00:39:32.154709\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-04-03 00:39:32'),(130,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-14T05:44:32.409335\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-14 05:44:32'),(131,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-14T07:14:51.132587\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-14 07:14:51'),(132,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-14T07:26:01.909533\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-14 07:26:02'),(133,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-14T12:10:57.600218\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-14 12:10:58'),(134,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-14T13:08:12.565933\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-14 13:08:13'),(135,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-14T13:13:09.550995\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-14 13:13:10'),(136,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-15T00:36:59.226811\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-15 00:36:59'),(137,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-15T01:29:04.006118\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-15 01:29:04'),(138,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-15T01:52:17.416396\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-15 01:52:17'),(139,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-22T01:26:25.662977\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-22 01:26:26'),(140,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-04-22 01:26:32'),(141,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 封装的流程是什么？..., 模式: r1','127.0.0.1',NULL,'2025-04-22 01:26:44'),(142,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-04-22 01:31:18'),(143,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-04-22 01:31:22'),(144,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-04-22 01:31:31'),(145,'admin','update','User','planer1','更新用户 planer1 的权限',NULL,NULL,'2025-04-22 01:31:38'),(146,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-04-22 01:31:40'),(147,'admin','view','User','boss','查看用户 boss 的权限',NULL,NULL,'2025-04-22 01:31:41'),(148,'admin','view','User','bonnie','查看用户 bonnie 的权限',NULL,NULL,'2025-04-22 01:31:47'),(149,'admin','update','User','bonnie','更新用户 bonnie 的权限',NULL,NULL,'2025-04-22 01:31:49'),(150,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-04-22 01:31:51'),(151,'admin','view','User','bonnie','查看用户 bonnie 的权限',NULL,NULL,'2025-04-22 01:31:53'),(152,'admin','view','User','River','查看用户 River 的权限',NULL,NULL,'2025-04-22 01:31:59'),(153,'admin','update','User','River','更新用户 River 的权限',NULL,NULL,'2025-04-22 01:32:09'),(154,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-04-22 01:32:10'),(155,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-04-22 12:14:14'),(156,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-23T02:37:46.761727\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-23 02:37:47'),(157,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-04-23 09:14:14'),(158,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-04-28T03:20:12.415352\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0','2025-04-28 03:20:12'),(159,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-06T03:12:46.283606\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-06 03:12:46'),(160,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-05-06 03:13:01'),(161,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-07T06:54:47.086635\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-07 06:54:47'),(162,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-13T05:39:09.179438\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-13 05:39:09'),(163,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-13 05:56:04'),(164,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-05-13 05:56:17'),(165,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 这是一个测试R1模型可用性的请求，请回复\'可用\'一词。..., 模式: r1','127.0.0.1',NULL,'2025-05-13 05:57:22'),(166,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-15T01:44:11.344158\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-15 01:44:11'),(167,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-15 06:58:55'),(168,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-05-15 06:58:57'),(169,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-15 07:11:49'),(170,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-05-15 07:11:54'),(171,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-16T00:47:51.923283\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-16 00:47:52'),(172,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-19T02:27:04.347250\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-19 02:27:04'),(173,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-05-19T08:27:14.355920\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-19 08:27:14'),(174,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-19T08:27:29.260681\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-19 08:27:29'),(175,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-20T00:48:45.767993\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-20 00:48:46'),(176,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-20 05:34:11'),(177,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-20 05:36:10'),(178,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:01:43'),(179,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们有多少台Handler?..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:02:12'),(180,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-20 07:26:21'),(181,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-20 07:26:42'),(182,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 测试数据库连接..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:26:44'),(183,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:28:29'),(184,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们有多少个JWQ510X系列的产品？..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:29:10'),(185,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-20 07:31:58'),(186,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-20 07:32:12'),(187,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:32:15'),(188,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:33:23'),(189,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-20 07:34:12'),(190,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 测试数据库连接..., 模式: database','127.0.0.1',NULL,'2025-05-23 04:13:35'),(191,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-23 04:13:43'),(192,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-24T08:18:42.967894\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 08:18:43'),(193,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-24 08:18:52'),(194,'admin','update','ImportPath',NULL,'保存导入路径: D:\\1-My Work\\0-On-Going\\5-Q3 report\\8-APS\\1-APS Rev 2.0\\Input Excel list\\2025.05.23',NULL,NULL,'2025-05-24 08:20:44'),(195,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-24T14:34:12.856627\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 14:34:13'),(196,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-05-24T15:05:01.880327\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 15:05:02'),(197,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-24T15:05:04.021494\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 15:05:04'),(198,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-24T16:45:48.623810\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 16:45:49'),(199,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 测试数据库连接..., 模式: database','127.0.0.1',NULL,'2025-05-24 16:59:48'),(200,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-05-24 17:02:05'),(201,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 17:02:40'),(202,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:05:42'),(203,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 17:18:47'),(204,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-24 17:18:48'),(205,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 17:18:57'),(206,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-24 17:18:57'),(207,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-24 17:19:04'),(208,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-24 17:19:05'),(209,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:19:27'),(210,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:20:04'),(211,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:32:39'),(212,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:33:08'),(213,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:33:22'),(214,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:36:40'),(215,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们有多少台handler?..., 模式: database','127.0.0.1',NULL,'2025-05-24 17:37:00'),(216,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 18:02:17'),(217,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 18:02:34'),(218,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们一共有多少台handler?编号是哪些？..., 模式: database','127.0.0.1',NULL,'2025-05-24 18:03:05'),(219,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 我们一共有多少台分选机？每种分选机多少台？..., 模式: database','127.0.0.1',NULL,'2025-05-24 18:03:48'),(220,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-05-24 18:13:36'),(221,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-25T15:39:29.455907\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-25 15:39:29'),(222,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 01:51:56'),(223,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 01:51:56'),(224,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 02:24:48'),(225,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 02:24:48'),(226,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 03:38:10'),(227,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 03:38:11'),(228,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 03:38:30'),(229,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 03:38:30'),(230,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 03:54:01'),(231,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 03:54:02'),(232,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 03:54:09'),(233,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 03:54:09'),(234,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 05:33:30'),(235,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 05:33:31'),(236,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-29T06:52:17.816125\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 06:52:18'),(237,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-29 06:53:28'),(238,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-05-29 06:53:29'),(239,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-05-30T05:30:17.213005\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','2025-05-30 05:30:17'),(240,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-01 12:18:37'),(241,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-06-01 12:18:37'),(242,'admin','update','ImportPath',NULL,'保存导入路径: D:\\Users\\pc\\Documents\\APS inputExcel List\\1-excellist\\2025.05.25\\2025.05.23',NULL,NULL,'2025-06-01 12:56:13'),(243,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-03T00:56:19.826614\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-03 00:56:20'),(244,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-03 07:18:04'),(245,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-06-03 07:18:04'),(246,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-03 07:18:17'),(247,'admin','update','DatabaseConfig',NULL,'更新数据库配置: 类型=mysql',NULL,NULL,'2025-06-03 07:18:17'),(248,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-03 08:22:45'),(249,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-03T09:04:49.103280\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-03 09:04:49'),(250,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-06-03 09:04:59'),(251,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-04T03:06:15.485777\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-04 03:06:15'),(252,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-04T13:42:33.411975\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-04 13:42:33'),(253,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-04 13:46:42'),(254,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T02:26:32.218123\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-05 02:26:32'),(255,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-05 02:29:22'),(256,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-05 03:25:22'),(257,'admin','AI_CHAT','AI_Assistant',NULL,'用户发送消息: 请检查数据库连接状态..., 模式: database','127.0.0.1',NULL,'2025-06-05 03:25:33'),(258,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-05 06:36:04'),(259,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T08:44:00.584365\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-05 08:44:01'),(260,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T08:44:31.070356\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-05 08:44:31'),(261,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T08:49:33.336557\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-05 08:49:33'),(262,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T08:51:44.334884\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-05 08:51:44'),(263,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T13:43:01.328473\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 13:43:01'),(264,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T13:44:39.547613\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 13:44:40'),(265,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T13:53:32.305198\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 13:53:32'),(266,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:29:24.098742\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:29:24'),(267,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:29:34.495760\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:29:34'),(268,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-06-05 14:29:38'),(269,'admin','create','User','op','创建用户成功，用户名: op, 角色: op',NULL,NULL,'2025-06-05 14:30:01'),(270,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-05 14:30:02'),(271,'admin','view','User','op','查看用户 op 的权限',NULL,NULL,'2025-06-05 14:30:07'),(272,'admin','update','User','op','更新用户 op 的权限',NULL,NULL,'2025-06-05 14:30:22'),(273,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-05 14:30:24'),(274,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:30:26.051687\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:30:26'),(275,'op','login','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:30:28.584891\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:30:29'),(276,'op','logout','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:30:45.253274\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:30:45'),(277,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:30:48.338894\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:30:48'),(278,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-05 14:30:52'),(279,'admin','view','User','op','查看用户 op 的权限',NULL,NULL,'2025-06-05 14:30:55'),(280,'admin','update','User','op','更新用户 op 的权限',NULL,NULL,'2025-06-05 14:30:58'),(281,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-05 14:31:00'),(282,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:31:01.123130\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:31:01'),(283,'op','login','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:31:04.077437\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:31:04'),(284,'op','logout','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:31:34.090651\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:31:34'),(285,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:31:38.229814\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:31:38'),(286,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:35:46.308705\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:35:46'),(287,'op','login','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:35:49.446313\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:35:49'),(288,'op','logout','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:36:15.418807\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:36:15'),(289,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:36:17.898633\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:36:18'),(290,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T14:36:25.075113\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:36:25'),(291,'op','login','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T14:36:28.547468\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 14:36:29'),(292,'op','logout','user','op','{\"success\": true, \"timestamp\": \"2025-06-05T16:12:16.419778\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 16:12:16'),(293,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T16:12:20.117550\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 16:12:20'),(294,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-05T16:24:59.299177\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36','2025-06-05 16:24:59'),(295,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-05 16:27:19'),(296,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-06-05 16:27:23'),(297,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-05 17:05:42'),(298,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-06-05 17:05:44'),(299,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T02:27:42.909618\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 02:27:43'),(300,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-06 05:58:32'),(301,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 06:57:52'),(302,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 07:31:55'),(303,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 08:15:14'),(304,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-06 08:16:26'),(305,'admin','view','User','op','查看用户 op 的权限',NULL,NULL,'2025-06-06 08:16:38'),(306,'admin','update','User','op','更新用户 op 的权限',NULL,NULL,'2025-06-06 08:16:42'),(307,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-06 08:16:44'),(308,'admin','view','User','op','查看用户 op 的权限',NULL,NULL,'2025-06-06 08:16:45'),(309,'admin','update','User','op','更新用户 op 的权限',NULL,NULL,'2025-06-06 08:16:49'),(310,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-06 08:16:51'),(311,'admin','create','User','123','创建用户成功，用户名: 123, 角色: op',NULL,NULL,'2025-06-06 08:17:12'),(312,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 08:17:12'),(313,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 08:17:18'),(314,'admin','update','User','123','更新用户 123 的权限',NULL,NULL,'2025-06-06 08:17:20'),(315,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 08:17:22'),(316,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 启用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 08:35:53'),(317,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:25:14'),(318,'admin','delete','User','123','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:25:16'),(319,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 09:25:21'),(320,'admin','update','User','123','更新用户 123 的权限',NULL,NULL,'2025-06-06 09:25:24'),(321,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:25:26'),(322,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 09:25:29'),(323,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:25:34.628274\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:25:35'),(324,'123','login','user','123','{\"success\": true, \"timestamp\": \"2025-06-06T09:25:40.488114\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:25:40'),(325,'123','logout','user','123','{\"success\": true, \"timestamp\": \"2025-06-06T09:26:28.529764\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:26:29'),(326,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:26:31.658775\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:26:32'),(327,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:26:36'),(328,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 09:26:38'),(329,'admin','update','User','123','更新用户 123 的权限',NULL,NULL,'2025-06-06 09:26:47'),(330,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:26:50'),(331,'admin','delete','User','op','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:26:55'),(332,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:35:23'),(333,'admin','delete','User','test_user','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:35:28'),(334,'admin','view','User','test_user','查看用户 test_user 的权限',NULL,NULL,'2025-06-06 09:35:31'),(335,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 09:35:37'),(336,'admin','update','User','123','更新用户 123 的权限',NULL,NULL,'2025-06-06 09:35:43'),(337,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:35:45'),(338,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:35:49.140408\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:35:49'),(339,'123','login','user','123','{\"success\": true, \"timestamp\": \"2025-06-06T09:35:55.009068\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:35:55'),(340,'123','logout','user','123','{\"success\": true, \"timestamp\": \"2025-06-06T09:41:31.999474\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:41:32'),(341,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:41:34.246394\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:41:34'),(342,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:41:48'),(343,'admin','view','User','admin','查看用户 admin 的权限',NULL,NULL,'2025-06-06 09:41:52'),(344,'admin','update','User','admin','更新用户 admin 的权限',NULL,NULL,'2025-06-06 09:41:55'),(345,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:41:57'),(346,'admin','view','User','planer1','查看用户 planer1 的权限',NULL,NULL,'2025-06-06 09:41:58'),(347,'admin','update','User','planer1','更新用户 planer1 的权限',NULL,NULL,'2025-06-06 09:42:08'),(348,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:42:09'),(349,'admin','delete','User','test_user','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:42:13'),(350,'admin','delete','User','123','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:42:18'),(351,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:46:38.818724\"}','127.0.0.1','python-requests/2.32.3','2025-06-06 09:46:39'),(352,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:46:45.251704\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:46:45'),(353,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:46:47.709391\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:46:48'),(354,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:46:53'),(355,'admin','delete','User','test_user','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:46:57'),(356,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:48:40'),(357,'admin','delete','User','test_user','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:48:46'),(358,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 09:48:59'),(359,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:50:31.179783\"}','127.0.0.1','python-requests/2.32.3','2025-06-06 09:50:31'),(360,'admin','view','User',NULL,'查看用户列表，共8条记录',NULL,NULL,'2025-06-06 09:50:31'),(361,'admin','delete','User','planer1','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:50:31'),(362,'admin','delete','User','planer1','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:50:31'),(363,'admin','delete','User','planer1','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:50:33'),(364,'admin','delete','User','planer1','删除用户失败，错误: Textual SQL expression \'PRAGMA foreign_keys = OFF\' should be explicitly declared as text(\'PRAGMA foreign_keys = OFF\')',NULL,NULL,'2025-06-06 09:50:37'),(365,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:53:49'),(366,'admin','delete','User','test_user','删除用户失败，错误: name \'UserPermission\' is not defined',NULL,NULL,'2025-06-06 09:53:58'),(367,'admin','delete','User','123','删除用户失败，错误: name \'UserPermission\' is not defined',NULL,NULL,'2025-06-06 09:54:03'),(368,'admin','delete','User','op','删除用户失败，错误: name \'UserPermission\' is not defined',NULL,NULL,'2025-06-06 09:54:14'),(369,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:54:48.138017\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:54:48'),(370,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:55:06.434587\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:55:06'),(371,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:55:14'),(372,'admin','delete','User','test_user','删除用户失败，错误: name \'UserPermission\' is not defined',NULL,NULL,'2025-06-06 09:55:18'),(373,'admin','view','User','boss','查看用户 boss 的权限',NULL,NULL,'2025-06-06 09:55:23'),(374,'admin','view','User','test_user','查看用户 test_user 的权限',NULL,NULL,'2025-06-06 09:55:29'),(375,'admin','update','User','test_user','更新用户 test_user 的权限',NULL,NULL,'2025-06-06 09:55:31'),(376,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:55:33'),(377,'admin','view','User','123','查看用户 123 的权限',NULL,NULL,'2025-06-06 09:55:33'),(378,'admin','update','User','123','更新用户 123 的权限',NULL,NULL,'2025-06-06 09:55:38'),(379,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:55:40'),(380,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:55:42.096580\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:55:42'),(381,'123','login','user','123','{\"success\": true, \"timestamp\": \"2025-06-06T09:55:48.634077\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:55:49'),(382,'123','logout','user','123','{\"success\": true, \"timestamp\": \"2025-06-06T09:56:10.134043\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:56:10'),(383,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T09:56:12.462117\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-06 09:56:12'),(384,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 09:56:31'),(385,'admin','delete','User','test_user','删除用户失败，错误: name \'UserPermission\' is not defined',NULL,NULL,'2025-06-06 09:56:34'),(386,'admin','view','User',NULL,'查看用户列表，共7条记录',NULL,NULL,'2025-06-06 10:00:42'),(387,'admin','delete','User','123','删除用户成功，用户名: 123, 角色: op',NULL,NULL,'2025-06-06 10:00:45'),(388,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-06 10:00:46'),(389,'admin','delete','User','test_user','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'test_user\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:00:49'),(390,'admin','delete','User','test_user','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'test_user\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:00:58'),(391,'admin','delete','User','op','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'op\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:01:02'),(392,'admin','view','User',NULL,'查看用户列表，共6条记录',NULL,NULL,'2025-06-06 10:01:18'),(393,'admin','delete','User','op','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'op\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:01:20'),(394,'admin','delete','User','test_user','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'test_user\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:01:28'),(395,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T10:03:18.025740\"}','127.0.0.1','python-requests/2.32.3','2025-06-06 10:03:18'),(396,'admin','create','User',NULL,'创建用户失败，角色无效: Operator',NULL,NULL,'2025-06-06 10:03:18'),(397,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T10:03:18.052738\"}','127.0.0.1','python-requests/2.32.3','2025-06-06 10:03:18'),(398,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-06-06 10:04:20'),(399,'admin','delete','User','test_user','删除用户成功，用户名: test_user, 角色: Operator',NULL,NULL,'2025-06-06 10:04:23'),(400,'admin','view','User',NULL,'查看用户列表，共4条记录',NULL,NULL,'2025-06-06 10:04:24'),(401,'admin','delete','User','op','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'op\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:04:26'),(402,'admin','create','User','boss','创建用户成功，用户名: boss, 角色: tech',NULL,NULL,'2025-06-06 10:05:02'),(403,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-06-06 10:05:02'),(404,'admin','view','User','boss','查看用户 boss 的权限',NULL,NULL,'2025-06-06 10:05:07'),(405,'admin','delete','User','boss','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'boss\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:05:30'),(406,'admin','update','User','boss','更新用户 boss: 角色从 tech 更改为 op',NULL,NULL,'2025-06-06 10:05:56'),(407,'admin','view','User',NULL,'查看用户列表，共5条记录',NULL,NULL,'2025-06-06 10:05:57'),(408,'admin','delete','User','boss','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'boss\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:06:00'),(409,'admin','delete','User','boss','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'boss\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:06:06'),(410,'admin','delete','User','op','删除用户失败，错误: (sqlite3.OperationalError) no such table: main.menu_settings\n[SQL: DELETE FROM user_permissions WHERE user_permissions.username = ?]\n[parameters: (\'op\',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)',NULL,NULL,'2025-06-06 10:06:09'),(411,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T10:13:57.469590\"}','127.0.0.1','python-requests/2.32.3','2025-06-06 10:13:57'),(412,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-06T10:18:06.888074\"}','127.0.0.1','python-requests/2.32.3','2025-06-06 10:18:07'),(413,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-09T01:27:48.897569\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-09 01:27:49'),(414,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-09T06:33:52.936085\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-09 06:33:53'),(415,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-10T06:04:17.172672\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-10 06:04:17'),(416,'admin','update','GlobalScheduler',NULL,'设置全局定时任务状态为: 禁用','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-10 06:04:29'),(417,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-10T08:54:01.364043\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-10 08:54:01'),(418,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-10T14:40:05.027671\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36','2025-06-10 14:40:05'),(419,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-10T14:59:59.780816\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36','2025-06-10 15:00:00'),(420,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-10T21:52:15.777871\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36','2025-06-10 21:52:16'),(421,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-12T05:35:19.700842\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-12 05:35:20'),(422,'admin','update','AISettings',NULL,'更新AI数据库查询设置','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-12 07:52:45'),(423,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-12T10:11:35.954167\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-12 10:11:36'),(424,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-12T10:15:59.195119\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-12 10:15:59'),(425,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-12T11:38:41.830460\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-12 11:38:42'),(426,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-13T05:50:25.306679\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-13 05:50:25'),(427,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T02:03:32.546299\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 02:03:33'),(428,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:07:24.762434\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 03:07:25'),(429,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:14:27.056888\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 03:14:27'),(430,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:34:35.641636\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 03:34:36'),(431,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:44:54.827830\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 03:44:55'),(432,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:45:53.800615\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 03:45:54'),(433,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:46:31.878339\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 03:46:32'),(434,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:47:38.835697\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 03:47:39'),(435,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:51:03.396497\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 03:51:03'),(436,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:52:50.409858\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 03:52:50'),(437,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:53:11.008497\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 03:53:11'),(438,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T03:57:59.988996\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 03:58:00'),(439,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T04:01:47.294217\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 04:01:47'),(440,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T04:10:18.381421\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 04:10:18'),(441,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T04:20:42.170876\"}','127.0.0.1','python-requests/2.32.3','2025-06-15 04:20:42'),(442,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T04:21:42.652273\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 04:21:43'),(443,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:20:49.614764\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 06:20:50'),(444,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:34:28.220759\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-15 06:34:28'),(445,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:35:14.098584\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-15 06:35:14'),(446,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:36:02.122895\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-15 06:36:02'),(447,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:43:30.673514\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-15 06:43:31'),(448,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:48:29.070695\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-15 06:48:29'),(449,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:49:22.929713\"}','127.0.0.1','Werkzeug/2.3.7','2025-06-15 06:49:23'),(450,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T06:54:53.656908\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 06:54:54'),(451,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T07:01:06.327265\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 07:01:06'),(452,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T08:58:50.577051\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 08:58:51'),(453,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T12:06:39.702588\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 12:06:40'),(454,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T13:26:17.439818\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 13:26:17'),(455,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T13:35:16.211690\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 13:35:16'),(456,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T13:49:37.655254\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 13:49:38'),(457,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T14:19:25.564438\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 14:19:26'),(458,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-15T14:53:25.699018\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-15 14:53:26'),(459,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T02:42:33.568781\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 02:42:34'),(460,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T03:28:20.239678\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 03:28:20'),(461,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T05:57:09.964566\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 05:57:10'),(462,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T06:12:28.146110\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 06:12:28'),(463,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T06:24:28.171226\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 06:24:28'),(464,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T06:25:14.679729\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 06:25:15'),(465,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T06:42:43.044972\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 06:42:43'),(466,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T06:50:25.350087\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 06:50:25'),(467,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T06:53:47.877532\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 06:53:48'),(468,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T07:27:41.801566\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 07:27:42'),(469,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T08:04:08.221244\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 08:04:08'),(470,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T08:04:23.312293\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 08:04:23'),(471,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T08:15:40.711291\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 08:15:41'),(472,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T08:15:43.489414\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 08:15:43'),(473,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T08:25:47.067569\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 08:25:47'),(474,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T08:36:18.168066\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 08:36:18'),(475,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T09:00:13.527708\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 09:00:14'),(476,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T09:04:48.509000\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 09:04:49'),(477,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T09:29:14.774175\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 09:29:15'),(478,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T09:33:08.838462\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 09:33:09'),(479,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T09:34:23.406321\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 09:34:23'),(480,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T09:36:05.787045\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 09:36:06'),(481,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:05:46.240207\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:05:46'),(482,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:09:33.878654\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:09:34'),(483,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:10:18.711360\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:10:19'),(484,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:11:59.624061\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:12:00'),(485,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:13:47.895881\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:13:48'),(486,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:15:09.096789\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:15:09'),(487,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:15:53.872756\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:15:54'),(488,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:22:14.943590\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:22:15'),(489,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:23:21.700158\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:23:22'),(490,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:23:28.043226\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:23:28'),(491,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:26:32.626388\"}','127.0.0.1','python-requests/2.32.3','2025-06-16 10:26:33'),(492,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:36:15.534580\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 10:36:16'),(493,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T10:42:27.442181\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 10:42:27'),(494,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T12:04:30.357926\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 12:04:30'),(495,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T13:37:36.616916\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 13:37:37'),(496,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T13:37:39.299620\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 13:37:39'),(497,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T13:47:56.286628\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 13:47:56'),(498,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T13:55:04.297670\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 13:55:04'),(499,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-16T14:08:58.368257\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-16 14:08:58'),(500,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T00:26:51.060784\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 00:26:51'),(501,'admin','logout','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T00:54:24.702289\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 00:54:25'),(502,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T00:54:26.784787\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 00:54:27'),(503,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:10:44.843118\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:10:45'),(504,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:18:01.862321\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:18:02'),(505,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:27:51.047001\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:27:51'),(506,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:31:46.383932\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:31:46'),(507,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:33:05.101278\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:33:05'),(508,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:33:54.594867\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:33:55'),(509,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:34:38.265461\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:34:38'),(510,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:42:40.378323\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:42:40'),(511,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T01:43:06.351585\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 01:43:06'),(512,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T02:10:25.525918\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 02:10:26'),(513,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T03:21:49.475868\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 03:21:49'),(514,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T04:00:48.775670\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 04:00:49'),(515,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T04:46:13.309619\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 04:46:13'),(516,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T05:08:27.978474\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 05:08:28'),(517,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T05:10:30.400222\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 05:10:30'),(518,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T05:48:42.517560\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 05:48:43'),(519,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T06:53:29.559108\"}','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0','2025-06-17 06:53:30'),(520,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T07:03:25.659230\"}','127.0.0.1','python-requests/2.32.3','2025-06-17 07:03:26'),(521,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T07:23:04.561276\"}','127.0.0.1','python-requests/2.32.3','2025-06-17 07:23:05'),(522,'admin','login','user','admin','{\"success\": true, \"timestamp\": \"2025-06-17T07:30:59.963668\"}','127.0.0.1','Priority-Pages-Test/1.0','2025-06-17 07:31:00');
/*!40000 ALTER TABLE `user_action_logs` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-23 21:45:47
