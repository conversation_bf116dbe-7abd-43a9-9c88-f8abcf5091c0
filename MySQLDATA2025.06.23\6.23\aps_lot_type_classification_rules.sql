-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `lot_type_classification_rules`
--

DROP TABLE IF EXISTS `lot_type_classification_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lot_type_classification_rules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `lot_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Lot Type原始值',
  `classification` enum('engineering','production','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类结果',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规则描述',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `lot_type` (`lot_type`),
  KEY `idx_lot_type` (`lot_type`),
  KEY `idx_classification` (`classification`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lot Type分类规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lot_type_classification_rules`
--

LOCK TABLES `lot_type_classification_rules` WRITE;
/*!40000 ALTER TABLE `lot_type_classification_rules` DISABLE KEYS */;
INSERT INTO `lot_type_classification_rules` VALUES (1,'试验-E','engineering','默认规则: 试验-E -> engineering','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(2,'新品-E','engineering','默认规则: 新品-E -> engineering','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(3,'工程批','engineering','默认规则: 工程批 -> engineering','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(4,'工程','engineering','默认规则: 工程 -> engineering','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(5,'DOE-Q','engineering','默认规则: DOE-Q -> engineering','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(6,'qual-Q','engineering','默认规则: qual-Q -> engineering','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(7,'量产-P','production','默认规则: 量产-P -> production','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(8,'小批量-PE','production','默认规则: 小批量-PE -> production','system','2025-06-22 06:12:48','2025-06-22 06:12:48'),(9,'量产批','production','默认规则: 量产批 -> production','system','2025-06-22 06:12:48','2025-06-22 06:12:48');
/*!40000 ALTER TABLE `lot_type_classification_rules` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-24 18:53:24
