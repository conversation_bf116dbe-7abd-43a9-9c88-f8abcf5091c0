-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `lotprioritydone`
--

DROP TABLE IF EXISTS `lotprioritydone`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lotprioritydone` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `PRIORITY` varchar(50) DEFAULT NULL COMMENT '优先级',
  `HANDLER_ID` varchar(100) DEFAULT NULL COMMENT '操作员ID',
  `LOT_ID` varchar(100) DEFAULT NULL COMMENT '批次ID',
  `LOT_TYPE` varchar(50) DEFAULT NULL COMMENT '批次类型',
  `GOOD_QTY` int DEFAULT NULL COMMENT '良品数量',
  `PROD_ID` varchar(100) DEFAULT NULL COMMENT '产品ID',
  `DEVICE` varchar(100) DEFAULT NULL COMMENT '设备',
  `CHIP_ID` varchar(100) DEFAULT NULL COMMENT '芯片ID',
  `PKG_PN` varchar(100) DEFAULT NULL COMMENT '封装料号',
  `PO_ID` varchar(100) DEFAULT NULL COMMENT 'PO_ID',
  `STAGE` varchar(50) DEFAULT NULL COMMENT '阶段',
  `WIP_STATE` varchar(50) DEFAULT NULL COMMENT 'WIP_STATE',
  `PROC_STATE` varchar(50) DEFAULT NULL COMMENT 'PROC_STATE',
  `HOLD_STATE` varchar(50) DEFAULT NULL COMMENT 'HOLD_STATE',
  `FLOW_ID` varchar(100) DEFAULT NULL COMMENT 'FLOW_ID',
  `FLOW_VER` varchar(50) DEFAULT NULL COMMENT '流程版本',
  `RELEASE_TIME` datetime DEFAULT NULL COMMENT '发布时间',
  `FAC_ID` varchar(50) DEFAULT NULL COMMENT '工厂ID',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `SCHDULED_TIME` datetime DEFAULT NULL COMMENT '计划时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_lot_id` (`LOT_ID`),
  KEY `idx_device` (`DEVICE`),
  KEY `idx_stage` (`STAGE`),
  KEY `idx_priority` (`PRIORITY`),
  KEY `idx_release_time` (`RELEASE_TIME`),
  KEY `idx_create_time` (`CREATE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批次优先级完成表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lotprioritydone`
--

LOCK TABLES `lotprioritydone` WRITE;
/*!40000 ALTER TABLE `lotprioritydone` DISABLE KEYS */;
/*!40000 ALTER TABLE `lotprioritydone` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-24 18:53:26
