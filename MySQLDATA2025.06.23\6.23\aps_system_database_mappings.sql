-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `database_mappings`
--

DROP TABLE IF EXISTS `database_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `database_mappings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mapping_type` varchar(20) NOT NULL COMMENT '映射类型: table, module, schema',
  `target_name` varchar(100) NOT NULL COMMENT '目标名称（表名/模块名/架构名）',
  `database_config_id` int NOT NULL COMMENT '数据库配置ID',
  `priority` int DEFAULT '100' COMMENT '优先级，数值越小优先级越高',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `description` text COMMENT '描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mapping` (`mapping_type`,`target_name`),
  KEY `idx_mapping_type` (`mapping_type`),
  KEY `idx_target_name` (`target_name`),
  KEY `idx_database_config_id` (`database_config_id`),
  KEY `idx_priority` (`priority`),
  CONSTRAINT `fk_mapping_config` FOREIGN KEY (`database_config_id`) REFERENCES `database_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据库映射表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `database_mappings`
--

LOCK TABLES `database_mappings` WRITE;
/*!40000 ALTER TABLE `database_mappings` DISABLE KEYS */;
INSERT INTO `database_mappings` VALUES (1,'table','users',2,10,1,'用户表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(2,'table','user_permissions',2,10,1,'用户权限表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(3,'table','user_action_logs',2,10,1,'用户操作日志表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(4,'table','ai_settings',2,10,1,'AI设置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(5,'table','settings',2,10,1,'系统设置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(6,'table','database_info',2,10,1,'数据库信息表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(7,'table','migration_log',2,10,1,'迁移日志表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(8,'table','system_settings',2,10,1,'系统配置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(9,'table','scheduling_tasks',2,10,1,'调度任务表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(10,'table','user_filter_presets',2,10,1,'用户筛选预设表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(11,'table','order_data',1,20,1,'订单数据表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(12,'table','production_orders',1,20,1,'生产订单表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(13,'table','production_schedules',1,20,1,'生产计划表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(14,'table','customer_orders',1,20,1,'客户订单表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(15,'table','order_items',1,20,1,'订单项表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(16,'table','resources',1,20,1,'资源表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(17,'table','test_specs',1,20,1,'测试规格表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(18,'table','maintenance_records',1,20,1,'维护记录表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(19,'table','resource_usage_logs',1,20,1,'资源使用日志表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(20,'table','wip_records',1,20,1,'WIP记录表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(21,'table','et_wait_lot',1,30,1,'等待批次表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(22,'table','wip_lot',1,30,1,'WIP批次表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(23,'table','lot_wip',1,30,1,'批次WIP表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(24,'table','ct',1,30,1,'CT数据表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(25,'table','eqp_status',1,30,1,'设备状态表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(26,'table','et_ft_test_spec',1,30,1,'FT测试规格表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(27,'table','et_uph_eqp',1,30,1,'设备UPH表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(28,'table','tcc_inv',1,30,1,'TCC库存表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(29,'table','et_recipe_file',1,30,1,'配方文件表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(30,'table','lot_priority_config',1,40,1,'批次优先级配置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(31,'table','device_priority_config',1,40,1,'设备优先级配置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(32,'table','product_priority_config',1,40,1,'产品优先级配置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(33,'table','email_configs',1,50,1,'邮件配置表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(34,'table','email_attachments',1,50,1,'邮件附件表','2025-06-18 18:13:58','2025-06-18 18:13:58'),(35,'table','excel_mappings',1,60,1,'Excel映射表','2025-06-18 18:13:58','2025-06-18 18:13:58');
/*!40000 ALTER TABLE `database_mappings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-24 18:53:21
