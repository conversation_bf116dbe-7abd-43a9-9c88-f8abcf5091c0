-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `lotpriorityconfig`
--

DROP TABLE IF EXISTS `lotpriorityconfig`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lotpriorityconfig` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `stage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工艺阶段',
  `priority` int NOT NULL DEFAULT '5' COMMENT '优先级(1-10，数字越小优先级越高)',
  `refresh_time` datetime DEFAULT NULL COMMENT '刷新时间',
  `user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作用户',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device` (`device`),
  KEY `idx_stage` (`stage`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次优先级配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lotpriorityconfig`
--

LOCK TABLES `lotpriorityconfig` WRITE;
/*!40000 ALTER TABLE `lotpriorityconfig` DISABLE KEYS */;
INSERT INTO `lotpriorityconfig` VALUES (9,'YX2500001330','BAKING2',0,NULL,'admin','2025-06-16 23:25:44','2025-06-16 23:25:44'),(10,'YX2500001430','BAKING2',1,NULL,'admin','2025-06-16 23:25:44','2025-06-16 23:25:44'),(11,'YX2500001582','BAKING2',2,NULL,'admin','2025-06-16 23:25:44','2025-06-16 23:25:44'),(12,'MT8768','FT',1,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(13,'MT8768','CP',2,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(14,'MT6765','FT',2,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(15,'MT6765','CP',3,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(16,'MT6762','FT',3,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(17,'MT6762','CP',4,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(18,'MT6761','FT',4,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(19,'MT6761','CP',5,'2025-06-17 17:44:05','admin','2025-06-17 09:44:05','2025-06-17 09:44:05'),(20,'TEST_DEVICE_1750167296','FT',2,NULL,'test_user','2025-06-17 13:34:58','2025-06-17 13:34:58'),(25,'YX2500001330','BAKING2',0,NULL,'admin','2025-06-22 18:02:34','2025-06-22 18:02:34'),(26,'YX2500001430','BAKING2',1,NULL,'admin','2025-06-22 18:02:34','2025-06-22 18:02:34'),(27,'YX2500001582','BAKING2',2,NULL,'admin','2025-06-22 18:02:34','2025-06-22 18:02:34');
/*!40000 ALTER TABLE `lotpriorityconfig` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-24 18:53:22
