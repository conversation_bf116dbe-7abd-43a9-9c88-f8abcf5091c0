-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: aps_system
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `scheduler_config`
--

DROP TABLE IF EXISTS `scheduler_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scheduler_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key` varchar(255) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_by` varchar(100) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='APScheduler调度器配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scheduler_config`
--

LOCK TABLES `scheduler_config` WRITE;
/*!40000 ALTER TABLE `scheduler_config` DISABLE KEYS */;
INSERT INTO `scheduler_config` VALUES (1,'scheduler_enabled','true','APScheduler调度器是否启用','2025-06-22 15:31:04',NULL),(2,'max_workers','10','最大工作线程数','2025-06-13 15:49:39','system'),(3,'coalesce','true','是否合并相同的任务','2025-06-13 15:49:39','system'),(4,'max_instances','3','同一任务最大并发实例数','2025-06-13 15:49:39','system'),(5,'log_level','INFO','日志级别','2025-06-13 15:49:39','system'),(6,'job_defaults_misfire_grace_time','30','任务错过执行的宽限时间(秒)','2025-06-13 15:49:39','system'),(7,'executors_thread_max_workers','20','线程池执行器最大工作线程数','2025-06-13 15:49:39','system'),(8,'timezone','Asia/Shanghai','时区设置','2025-06-13 15:49:39','system'),(9,'global_scheduler_enabled','true','全局定时任务开关','2025-06-22 15:31:14',NULL),(10,'email_processing_lock','false','邮件处理执行锁，防止重复执行','2025-06-23 03:42:30',NULL),(11,'email_auto_mode','smart','邮件处理模式：smart(智能)、manual(仅手动)、auto(仅自动)','2025-06-23 03:42:30',NULL),(12,'manual_priority','true','手动处理优先级高于定时任务','2025-06-23 03:42:30',NULL);
/*!40000 ALTER TABLE `scheduler_config` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-24 18:53:23
