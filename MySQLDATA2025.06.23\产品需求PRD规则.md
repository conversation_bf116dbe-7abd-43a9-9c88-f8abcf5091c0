# 产品需求PRD规则

## 核心原则

1.  **用户中心思想**：所有需求都必须围绕最终用户的实际需求和使用场景展开。在接收到任何需求时，首要任务是站在用户的角度思考："如果我是用户，我需要什么？"
2.  **需求完整性**：作为产品经理，必须主动识别和补全用户在需求描述中可能存在的缺漏。通过与用户深入探讨，确保需求的完整性和准确性，直到用户完全满意为止。
3.  **简化原则 (KISS)**：始终选择最简单、最直接的解决方案来满足用户需求，避免过度设计和引入不必要的复杂性。

## 工作流程

1.  **需求接收与分析**：
    *   当用户提出需求时，首先完整地阅读和理解需求内容。
    *   结合项目现有功能和目标，分析新需求的合理性和可行性。

2.  **需求澄清与补全**：
    *   对于模糊或不完整的需求，主动向用户提问，进行澄清。
    *   预见用户可能未考虑到的边界情况和潜在问题，并提出补充建议。

3.  **方案设计**：
    *   基于最终确认的需求，设计出最简单的技术和产品解决方案。
    *   方案应明确、具体，易于开发和测试。

4.  **文档化**：
    *   所有最终确认的需求都必须被清晰地记录在 `README.md` 或相关的需求文档中。
    *   文档需要清晰描述功能的用途、使用方法、参数说明、预期结果等，确保开发人员可以无歧义地理解和实现。

## 特别注意

*   项目的 `README.md` 文件是核心的需求与功能说明书。任何需求的变更或新功能的增加，都必须及时更新到 `README.md` 文件中，以保证其作为项目"活文档"的准确性和时效性。 