# 前端设计规则

## 核心原则

1.  **简洁高效**：界面设计应遵循紧凑、干净、美观的原则。功能操作应直观、易于理解，减少用户的学习成本。
2.  **统一体验**：保持整个应用的设计语言和操作体验一致。所有页面和组件都应遵循统一的视觉风格和交互模式。
3.  **高性能**：优先考虑页面加载速度和响应性能。所有资源（特别是SVG图标和图片）应采用内部引用，避免因外部请求导致页面卡顿。
4.  **响应式设计**：确保应用在不同尺寸的设备上都能提供良好的视觉和使用体验。

## 技术栈与规范

*   **核心框架**：HTML5, CSS3, JavaScript, Bootstrap 5。
*   **UI/UX 标准**：
    *   **布局**：使用 Bootstrap 的网格系统构建响应式布局。界面元素排列紧凑，信息密度高。
    *   **表格**：数据表格要求显示美观，并提供便捷的行内编辑功能、完整的CRUD（创建、读取、更新、删除）操作支持。
    *   **视觉风格**：遵循苹果（Apple）的设计美学，追求简洁、优雅和高质感。
    *   **图标**：统一使用SVG图标，并以内部引用的方式嵌入HTML，以优化加载性能。
*   **代码架构**：
    *   **关注点分离**：严格分离 HTML (结构), CSS (表现), 和 JavaScript (行为)。三者应分别存放在独立的文件中。
    *   **模块化**：复杂的UI组件或功能应拆分为独立的模块，确保代码的清晰和可维护性。
    *   **命名规范**：遵循BEM（Block, Element, Modifier）或类似的CSS命名规范，避免样式冲突。

## 关键功能实现

*   **生产计划可视化**：通过图表或甘特图等形式，直观展示生产计划。
*   **设备负载分析**：以图表形式展示设备负载状态，便于快速分析。
*   **数据交互**：所有与后端的数据交互必须通过 `api-client-v2.js` 或更高版本的API客户端进行。
*   **用户反馈**：操作结果（如成功、失败、警告）应通过统一的 `Toast` 通知系统给予用户清晰的反馈。

## 开发流程

1.  **新增页面/菜单**：
    *   在 `app/templates/` 目录下创建新的HTML模板。
    *   开发完成后，必须更新 `app/config/menu_config.py` 文件以添加新的菜单项。
    *   同时，需在 `aps` 数据库的权限相关表中为相应角色（如 `admin`）添加新页面的访问权限。
    *   最后，通过 `/clear_cache` 路由清理缓存，确保菜单更新生效。 