# 后端设计规则

## 核心原则

1.  **稳定可靠**：后端服务是整个系统的基石，必须保证其稳定性和可靠性。
2.  **清晰可维**：代码结构必须清晰，遵循SOLID原则，保证高内聚、低耦合，便于长期维护和扩展。
3.  **安全第一**：所有API接口和数据处理流程都必须考虑安全性，防止SQL注入、跨站脚本（XSS）等常见安全漏洞。
4.  **性能优先**：在保证功能正确的前提下，应持续优化核心算法和数据库查询，确保系统高性能响应。

## 技术栈与规范

*   **核心框架**：Flask。
*   **数据库**：
    *   **主数据库**：MySQL。所有核心业务数据（用户、订单、生产数据等）必须存储在MySQL中。
    *   **禁止使用**：除特定日志或缓存场景外，严禁使用SQLite作为业务数据库。
    *   **表名规范**：数据库表名严格区分大小写。在编写模型（Models）和执行原生SQL查询时，必须与数据库中已存在的表名大小写完全一致，以避免创建重复的表或查询失败。
*   **代码架构**：
    *   **蓝图（Blueprints）**：按照业务领域（如 `auth`, `orders`, `resources`, `production`）划分蓝图，管理不同的路由和功能模块。
    *   **服务层（Services）**：将复杂的业务逻辑封装在 `app/services/` 目录下的服务层中，保持路由（Views）层的轻量和清晰。
    *   **模型层（Models）**：数据库表结构定义在 `app/models/` 目录下，使用 SQLAlchemy 或类似的ORM进行管理。
*   **编码原则**：
    *   **DRY (Don't Repeat Yourself)**：避免重复代码。在编写新功能前，检查是否存在可复用的现有函数或服务。
    *   **KISS (Keep It Simple, Stupid)**：保持解决方案的简单性。
    *   **YAGNI (You Ain't Gonna Need It)**：不要过度设计，只实现当前需要的功能。
    *   **注释**：只对非显而易见的、复杂的业务逻辑或算法进行注释。代码本身应具备良好的自解释性。

## API 设计

*   **版本优先**：新开发的API优先考虑在 `v2` (`/api/v2/...`) 或更高版本下进行构建，以支持系统迭代。
*   **统一格式**：所有API响应都应采用统一的JSON格式，包含状态码、消息和数据负载。例如：`{"status": "success", "message": "操作成功", "data": [...]}`。
*   **错误处理**：提供详细、明确的错误处理信息。当请求失败时，应返回具体的错误原因，便于前端调试和向用户展示。

## 核心功能领域

*   **用户认证系统**：实现基于Session或Token的用户登录、登出及权限验证。
*   **订单管理系统**：处理CP、FT等不同类型订单的导入、解析、存储和状态管理。
*   **数据管理系统**：提供对测试规范、设备状态、硬件库等核心资源的CRUD管理接口。
*   **智能排产算法**：开发和实现自定义排产算法与启发式优化算法，作为后端的核心竞争力。 