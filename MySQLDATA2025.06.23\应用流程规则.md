# 应用流程规则

## 核心理念

本应用遵循从需求到交付的完整、闭环的开发流程。每个环节都有明确的输入和输出，旨在确保高质量、高效率的交付。流程强调 **沟通**、**文档** 和 **测试** 的重要性。

## 标准开发工作流

1.  **第一步：需求分析与确认**
    *   **输入**：用户提出的初步需求。
    *   **过程**：遵循《产品需求PRD规则.md》，与用户充分沟通，明确、细化并完善需求。
    *   **输出**：更新到 `README.md` 或相关文档中的、已获双方确认的最终需求。

2.  **第二步：系统架构设计**
    *   **输入**：最终需求文档。
    *   **过程**：根据需求，进行前后端及数据库的整体设计。
        *   **后端**：遵循《后端设计规则.md》，设计新的数据模型、服务逻辑和API接口。
        *   **前端**：遵循《前端设计规则.md》，设计新的页面布局、组件和交互流程。
    *   **输出**：清晰的系统架构设计方案。

3.  **第三步：编码实现**
    *   **输入**：架构设计方案。
    *   **过程**：
        *   **后端开发**：实现用户认证、订单管理、数据管理、排产算法等功能。
        *   **前端开发**：构建生产计划可视化、设备负载分析等响应式界面。
        *   **数据库集成**：实现数据模型的创建和同步，确保数据一致性。
    *   **输出**：符合设计规范、注释良好、可运行的功能代码。

4.  **第四步：测试与验证**
    *   **输入**：可运行的功能代码。
    *   **过程**：
        *   **单元测试**：对核心模块和函数进行测试。
        *   **集成测试**：确保前后端API调用、数据流转正常。
        *   **功能验收**：提供测试方案或工具，由用户或测试人员验证功能是否符合需求。
    *   **输出**：测试报告和已修复缺陷的代码。

5.  **第五步：总结与复盘**
    *   **输入**：完成测试的功能。
    *   **过程**：在功能上线或交付后，对整个开发过程进行复盘，总结经验教训。
    *   **输出**：更新到 `README.md` 中的项目改进建议。

## 特殊流程规则

*   **Bug修复流程**：
    1.  **初步分析**：完整阅读相关代码，理解功能逻辑，提出最可能的解决方案并实施。
    2.  **二次尝试**：如果第一次修复失败，进行更深入的分析，调整方案后再次尝试。
    3.  **系统性诊断（System 2 Thinking）**：若两次尝试均失败，必须启动系统性诊断模式：
        *   列出所有可能导致Bug的假设。
        *   为每个假设设计验证方法。
        *   提供至少三种不同的解决方案，并详细说明优缺点，供用户选择。

*   **新增页面/菜单的强制流程**：
    1.  开发前端模板 (`/app/templates/...`)。
    2.  开发后端路由 (`/app/routes/...` 或 `/app/api_v2/...`)。
    3.  在 `app/config/menu_config.py` 中注册新菜单。
    4.  在数据库 `aps.roles_permissions` 和 `aps.permissions` 表中为角色配置新页面的访问权限。
    5.  重启应用并调用 `/clear_cache` 接口清理缓存。 