# 技术栈规则

本文档明确定义了"车规芯片终测智能调度平台"项目所使用的核心技术栈。所有开发活动都必须基于此技术栈进行，任何引入新技术或更改现有技术栈的提议都需经过充分讨论和批准。

## 1. 后端 (Backend)

*   **编程语言**: Python 3.x
*   **Web 框架**: Flask
    *   利用其蓝图（Blueprint）功能进行模块化组织。
    *   使用 `render_template` 进行服务端模板渲染。
    *   使用 `jsonify` 提供 RESTful API。
*   **核心库**:
    *   `Flask-SQLAlchemy`: ORM，用于与数据库进行对象关系映射。
    *   `Flask-Login`: 处理用户会话管理和认证。
    *   `Flask-Migrate`: 管理数据库结构变更。
    *   `pandas`: 用于复杂的数据处理，尤其是在解析Excel订单和数据分析时。
    *   `openpyxl`: 作为 `pandas` 的引擎，用于读写 `.xlsx` 格式的Excel文件。
    *   `apscheduler`: 用于处理后台定时任务和调度。

## 2. 前端 (Frontend)

*   **基础技术**:
    *   HTML5
    *   CSS3
    *   JavaScript (ES6+)
*   **UI 框架**: Bootstrap 5
    *   用于构建响应式布局、栅格系统、基础组件（按钮、表单、导航等）。
    *   鼓励使用其 `Utility API` 进行快速样式调整。
*   **核心库**:
    *   `jQuery`: 用于简化DOM操作和事件处理（在现有代码中维护，新代码可考虑使用原生JS替代）。
    *   `DataTables`: 功能强大的表格插件，支持排序、搜索、分页等。
    *   `ECharts`: 数据可视化库，用于生成各种图表。
    *   `SweetAlert2`: 用于生成美观的、交互式的弹窗提示。
    *   `xlsx.js`: 纯前端实现的Excel文件读写库，用于数据导入导出。
    *   `Font Awesome`: 提供丰富的图标集。

## 3. 数据库 (Database)

*   **主数据库**: MySQL
    *   用于存储所有核心业务数据。
*   **数据库交互**:
    *   优先使用 `Flask-SQLAlchemy` ORM。
    *   对于复杂查询，可使用原生SQL语句，但需注意防止SQL注入。

## 4. 开发与部署环境

*   **命令行工具**: PowerShell
    *   所有在开发说明中提供的命令都将使用PowerShell语法。
*   **版本控制**: Git
*   **Web 服务器 (生产环境建议)**: Nginx (作为反向代理)
*   **应用服务器 (生产环境建议)**: Gunicorn 或 uWSGI
*   **进程管理 (生产环境建议)**: Supervisor 