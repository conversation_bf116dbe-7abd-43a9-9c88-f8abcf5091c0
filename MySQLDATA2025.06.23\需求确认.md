# 需求确认清单 (Requirement Checklist)

## 文档目的

本文件旨在作为启动任何新功能或重要修改前的 **最终需求确认清单**。在开发工作开始前，必须与用户逐项核对并获得确认，确保双方对需求的理解完全一致。

---

### **项目/功能名称**: `在此填写项目或功能的简要名称`

### **需求提出日期**: `YYYY-MM-DD`

### **版本/迭代周期**: `vX.X / 迭代编号`

---

## 1. 核心目标 (Core Objective)

*   [ ] **一句话描述**：这个功能/修改最核心的目的是什么？解决了用户的什么痛点？
    *   *描述...*
*   [ ] **成功标准**：如何衡量这个功能是成功的？(例如：处理时间减少X%，用户操作步骤少于N步，实现了XX功能等)
    *   *描述...*

## 2. 功能性需求 (Functional Requirements)

*   [ ] **用户故事/用例 1**:
    *   **角色**: (作为一名...)
    *   **期望**: (我希望能够...)
    *   **目的**: (以便于...)
    *   **验收标准**: (当...时，即视为完成)
*   [ ] **用户故事/用例 2**:
    *   *... (可添加更多)*
*   [ ] **数据需求**:
    *   需要哪些输入数据？来源是哪里？(例如：来自 `ET_WAIT_LOT` 表的 `LOT_ID`)
    *   会产生或修改哪些数据？存储到哪里？
*   [ ] **权限要求**:
    *   哪些用户角色可以访问此功能？(例如：管理员、生产主管)
    *   不同角色是否有不同的操作权限？(例如：只读、读写)

## 3. 非功能性需求 (Non-Functional Requirements)

*   [ ] **性能要求**: 页面加载时间是否需要在X秒以内？API响应时间是否需要低于Y毫秒？
    *   *描述...*
*   [ ] **安全要求**: 是否涉及敏感数据？需要哪些特殊的安全措施？
    *   *描述...*
*   [ ] **兼容性要求**: 是否需要在特定的浏览器或设备上运行？
    *   *描述...*
*   [ ] **UI/UX 要求**: 界面设计是否有特殊要求？是否遵循《前端设计规则.md》即可？
    *   *描述...*

## 4. 范围边界 (Scope & Boundaries)

*   [ ] **明确要做 (In Scope)**: 清晰列出本次开发 **包含** 的所有工作。
    *   *列表...*
*   [ ] **明确不做 (Out of Scope)**: 清晰列出本次开发 **不包含** 的相关但暂不处理的工作，以避免范围蔓延。
    *   *列表...*

## 5. 依赖项 (Dependencies)

*   [ ] **技术依赖**: 是否依赖特定的第三方库或API？
    *   *描述...*
*   [ ] **任务依赖**: 是否依赖其他正在进行的开发任务？
    *   *描述...*

---

### **最终确认**

*   [ ] **用户确认**: 我已阅读并确认以上所有需求描述准确无误。
    *   **签字/日期**:
*   [ ] **开发确认**: 我已理解以上所有需求，并确认技术上是可行的。
    *   **签字/日期**: 