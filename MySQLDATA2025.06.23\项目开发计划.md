# 项目开发计划 (Project Development Plan)

## 1. 项目愿景与目标 (Vision & Goals)

*   **愿景**: 打造一个高效、智能、可靠的车规芯片终测智能调度平台。
*   **核心目标**:
    1.  **提升效率**: 通过智能排产算法，显著缩短订单等待时间，提高设备利用率。
    2.  **数据驱动**: 实现生产全流程数据化，为良率提升、成本控制和工艺优化提供决策支持。
    3.  **操作便捷**: 提供现代化、直观易用的Web界面，简化生产管理人员的日常操作。
    4.  **系统集成**: 无缝对接现有数据源，打通信息孤岛，确保数据一致性与完整性。

## 2. 总体技术架构

*   **后端**: 基于 Python Flask 的微服务化架构。
*   **前端**: 基于 Bootstrap 5 的现代化响应式单页面应用 (SPA) 风格。
*   **数据库**: 以 MySQL 为核心的关系型数据库。
*   **交互**: 前后端通过版本化的 RESTful API (`/api/v2/...`) 进行数据交互。

## 3. 开发阶段与里程碑 (Phases & Milestones)

本计划将项目开发分解为四个主要阶段，每个阶段都有明确的核心任务和交付成果。

---

### **阶段一：基础平台搭建 (Foundation Setup)**

*   **目标**: 搭建稳定可靠的系统骨架和基础管理功能。
*   **核心任务**:
    *   [ ] **1.1 环境与项目初始化**:
        *   [ ] 配置好Python、Flask、MySQL等基础开发环境。
        *   [ ] 初始化Git仓库，创建Flask项目基本结构 (`app`, `config`, `run.py`等)。
    *   [ ] **1.2 数据库与模型设计**:
        *   [ ] 根据需求文档，设计所有核心表的表结构。
        *   [ ] 创建对应的SQLAlchemy模型 (`app/models/...`)。
        *   [ ] 编写 `init_db.py` 脚本，用于初始化数据库表和基础数据。
    *   [ ] **1.3 用户认证与权限系统**:
        *   [ ] 实现用户登录、登出功能。
        *   [ ] 建立基于角色的权限控制体系 (RBAC)，控制不同用户对菜单和API的访问。
    *   [ ] **1.4 基础UI框架**:
        *   [ ] 搭建基于 `base.html` 的通用页面布局，包含顶部导航、侧边栏菜单和主内容区。
        *   [ ] 实现动态菜单加载，并完成与后端权限系统的联动。

---

### **阶段二：核心数据管理 (Core Data Management)**

*   **目标**: 实现对生产核心资源数据的增删改查（CRUD）管理。
*   **核心任务**:
    *   [ ] **2.1 数据源管理模块**:
        *   [ ] 开发统一的数据管理页面模板 (`base_resource_v3.html`)。
        *   [ ] 开发通用的后端API，支持对任意表的通用查询、分页、排序功能。
    *   [ ] **2.2 具体资源页面实现**:
        *   [ ] 逐一实现 `ET_FT_TEST_SPEC` (测试规范)、`EQP_STATUS` (设备状态)、`ET_UPH_EQP` (UPH)、`CT` (生产周期) 等核心资源的前后端管理功能。
    *   [ ] **2.3 数据导入导出**:
        *   [ ] 实现通用的Excel/JSON数据导入导出功能。

---

### **阶段三：订单处理与排产 (Order & Scheduling)**

*   **目标**: 打通订单处理流程，并实现核心的智能排产功能。
*   **核心任务**:
    *   [ ] **3.1 订单导入与解析**:
        *   [ ] 开发CP、FT等不同类型订单的Excel文件自动解析功能。
        *   [ ] 实现订单数据的自动清洗、验证和入库。
    *   [ ] **3.2 待测产品管理**:
        *   [ ] 开发 `ET_WAIT_LOT` 和 `WIP_LOT` 的管理界面，展示待排产的工单。
    *   [ ] **3.3 智能排产算法**:
        *   [ ] **(V1.0 - 基础算法)**: 开发基于优先级规则的调度算法。
        *   [ ] **(V2.0 - 优化算法)**: 引入启发式优化算法，综合考虑设备、物料、时间等多重约束，生成最优排产计划。
    *   [ ] **3.4 生产计划可视化**:
        *   [ ] 在前端通过甘特图等形式，直观展示排产结果。

---

### **阶段四：系统监控与优化 (Monitoring & Optimization)**

*   **目标**: 提升系统透明度和性能，确保长期稳定运行。
*   **核心任务**:
    *   [ ] **4.1 仪表盘 (Dashboard)**:
        *   [ ] 开发系统主页仪表盘，汇总展示关键KPI，如设备OEE、在制品数量、平均生产周期等。
    *   [ ] **4.2 日志与监控**:
        *   [ ] 建立完善的日志系统，记录关键操作和系统异常。
        *   [ ] 开发日志查看界面。
    *   [ ] **4.3 性能分析与优化**:
        *   [ ] 对慢查询和性能瓶颈API进行分析和优化。
        *   [ ] 引入缓存机制（如Redis），提升高频访问接口的性能。 