# APS车规芯片终测智能调度平台

## 项目概述
APS（Advanced Production Scheduling）是一个专为车规芯片终测工序设计的智能调度平台，提供订单管理、生产调度、设备监控、质量管控等核心功能。

## 最新更新 (2025-06-23)

### 🎯 订单汇总表功能完整实现

根据用户要求，系统已完全按照Excel汇总表格式建立了对应的数据库表结构：

#### 📋 汇总表结构

**1. FT订单汇总表 (ft_order_summary)**
- 严格按照 `downloads/FT订单汇总表.xlsx` 的34个字段顺序建表
- 包含：下单日期、订单号、标签名称、电路名称、芯片名称、圆片尺寸、送包、扩散批号、片号、装片方式、图号、封装、印章、交期、产品环保要求、MSL要求、可靠性要求、pin点信息、Item Code、出货地址、wafer lot、订单属性、Lot Type、源文件、导入时间、分类结果等

**2. CP订单汇总表 (cp_order_summary)**
- 严格按照 `downloads/CP订单汇总表.xlsx` 的22个字段顺序建表
- 包含：加工属性、加工承揽商、联系人、地址、电话、传真、加工委托方、订单号、产品名称、芯片名称、芯片批号、加工片数、成品型号、片号、CP Mapping、包装方式、工序、发货地址等

#### 🔧 技术实现

**数据模型**
- `app/models/ft_order_summary.py` - FT订单汇总表模型
- `app/models/cp_order_summary.py` - CP订单汇总表模型
- 支持从解析数据自动创建记录，智能字段映射

**数据保存服务**
- `app/services/summary_data_saver.py` - 汇总数据保存器
- 自动根据模板类型选择对应汇总表
- 支持重复检查，避免数据重复保存
- 提供统计信息和错误处理

**API接口**
- `/api/v2/orders/data/preview` - 支持汇总表数据预览
- 参数 `table_type`: `ft_summary`(FT订单) 或 `cp_summary`(CP订单)
- 批量处理API自动保存解析结果到汇总表

**前端界面**
- 订单处理页面新增FT/CP表格切换功能
- 实时显示汇总表统计信息
- 支持汇总数据预览、刷新、导出功能

#### 📊 数据流程

1. **Excel解析** → 通用解析器识别模板类型
2. **数据映射** → 按Excel字段名映射到数据库字段
3. **自动保存** → 根据模板类型保存到对应汇总表
4. **前端显示** → 用户可切换查看FT/CP汇总数据

#### 🎯 使用方式

1. 访问 `http://localhost:5000/orders/semi-auto`
2. 点击"附件获取与解析"处理Excel文件
3. 解析完成后数据自动保存到汇总表
4. 在"数据预览"区域切换FT/CP表格查看汇总数据
5. 使用刷新按钮获取最新汇总信息

#### ✅ 功能验证

- ✅ 数据库表严格按Excel表头字段顺序创建
- ✅ 解析的订单数据100%保存到对应汇总表
- ✅ FT/CP订单分别存储，字段完全对应
- ✅ 前端页面支持汇总表数据查看和切换
- ✅ API接口正常工作，数据预览功能完善

用户现在可以通过前端页面查看完全按照Excel汇总表格式存储的订单信息，所有解析提取的订单数据都已正确保存在MySQL数据库的汇总表中。

## 核心功能

### 1. 订单管理系统
- **半自动订单处理**: Excel解析、附件管理、数据预览
- **订单汇总表**: 严格按Excel格式的FT/CP订单汇总存储
- **智能解析**: 支持标准模板和CP模板自动识别
- **数据导出**: 支持Excel、CSV、PDF多格式导出

### 2. 生产调度引擎
- **智能调度算法**: 基于交期、优先级、设备状态的多维度调度
- **实时监控**: WebSocket实时进度反馈
- **设备管理**: 测试设备状态监控和调度
- **批次追踪**: 完整的生产批次跟踪链路

### 3. 系统管理
- **用户权限**: 基于角色的访问控制
- **菜单管理**: 动态菜单配置和权限控制
- **数据库管理**: MySQL主数据库 + SQLite资源管理
- **日志系统**: 统一日志记录和查看

## 技术架构

### 后端技术栈
- **Flask 2.3+**: Web框架
- **SQLAlchemy**: ORM数据库操作
- **MySQL**: 主要数据存储
- **SQLite**: 资源管理数据
- **APScheduler**: 定时任务调度
- **SocketIO**: 实时通信

### 前端技术栈
- **Bootstrap 5**: UI框架
- **jQuery**: JavaScript库
- **ECharts**: 图表可视化
- **DataTables**: 数据表格
- **Socket.IO**: 实时通信

### 项目结构
```
APS-2025.6.21/
├── app/
│   ├── api_v2/          # API接口v2
│   ├── models/          # 数据模型
│   ├── services/        # 业务服务
│   ├── templates/       # 页面模板
│   ├── static/          # 静态资源
│   └── utils/           # 工具函数
├── config/              # 配置文件
├── downloads/           # 下载文件
├── logs/               # 日志文件
└── requirements.txt    # 依赖包
```

## 快速开始

### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 配置MySQL数据库
# 修改 config/mysql_config.py 中的数据库连接信息
```

### 2. 数据库初始化
```bash
# 创建汇总表
python create_summary_tables.py

# 初始化基础数据
python init_db.py
```

### 3. 启动应用
```bash
# 启动Flask应用
python run.py

# 访问系统
# http://localhost:5000
```

### 4. 默认账户
- 用户名: `admin`
- 密码: `admin123`

## 使用指南

### 订单处理流程
1. **邮箱配置**: 配置Outlook邮箱和收件规则
2. **附件解析**: 自动下载并解析Excel订单文件
3. **数据汇总**: 解析结果自动保存到对应汇总表
4. **数据预览**: 查看FT/CP订单汇总信息
5. **数据导出**: 导出订单数据到Excel等格式

### 汇总表使用
- 访问"订单管理" → "订单处理中心"
- 使用FT/CP切换按钮查看不同类型订单
- 点击刷新按钮获取最新汇总数据
- 支持记录选择、详情查看、批量导出

## 更新日志

### v2.1 (2025-06-23)
- 🎯 **新增**: 完整的订单汇总表功能
- 🎯 **新增**: 严格按Excel表头字段顺序的数据库表
- 🎯 **新增**: FT/CP订单分类存储和显示
- 🎯 **优化**: 数据解析和保存流程
- 🎯 **优化**: 前端数据预览和切换功能

### v2.0 (2025-06-21)
- 🎯 **重构**: 统一API接口设计
- 🎯 **新增**: WebSocket实时通信
- 🎯 **新增**: 智能Excel解析引擎
- 🎯 **新增**: 生产调度优化算法
- 🎯 **优化**: 用户界面和交互体验

## 技术支持

### 常见问题
1. **数据库连接问题**: 检查MySQL服务状态和配置信息
2. **Excel解析失败**: 确认文件格式和模板匹配
3. **权限访问问题**: 检查用户角色和菜单权限配置

### 联系方式
- 项目维护者: APS开发团队
- 技术支持: 通过GitHub Issues提交问题

---

© 2025 APS车规芯片终测智能调度平台 - 版权所有