#!/usr/bin/env python3
"""
API v3性能对比测试工具
用于比较API v2和v3的性能差异，为迁移决策提供数据支撑
"""

import requests
import time
import json
import statistics
from datetime import datetime
import sys

class APIPerformanceTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.v2_prefix = "/api"
        self.v3_prefix = "/api/v3"
        self.test_tables = ['eqp_status', 'et_uph_eqp', 'et_ft_test_spec', 'ct']
        
    def test_api_v2_data(self, table_name, iterations=3):
        """测试API v2性能"""
        times = []
        for i in range(iterations):
            start_time = time.time()
            try:
                # 使用原有的API v2接口
                url = f"{self.base_url}/api_v2/resources/data"
                params = {'table': table_name, 'page': 1, 'per_page': 10}
                response = requests.get(url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        elapsed = time.time() - start_time
                        times.append(elapsed * 1000)  # 转换为毫秒
                        print(f"  v2 第{i+1}次: {elapsed*1000:.1f}ms")
                    else:
                        print(f"  v2 第{i+1}次: API错误 - {data.get('error')}")
                        return None
                else:
                    print(f"  v2 第{i+1}次: HTTP错误 {response.status_code}")
                    return None
                    
            except Exception as e:
                print(f"  v2 第{i+1}次: 异常 - {e}")
                return None
                
        return {
            'avg': statistics.mean(times),
            'min': min(times),
            'max': max(times),
            'times': times
        }
    
    def test_api_v3_data(self, table_name, iterations=3):
        """测试API v3性能"""
        times = []
        for i in range(iterations):
            start_time = time.time()
            try:
                url = f"{self.base_url}{self.v3_prefix}/tables/{table_name}/data"
                params = {'page': 1, 'per_page': 10}
                response = requests.get(url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        elapsed = time.time() - start_time
                        times.append(elapsed * 1000)  # 转换为毫秒
                        print(f"  v3 第{i+1}次: {elapsed*1000:.1f}ms")
                    else:
                        print(f"  v3 第{i+1}次: API错误 - {data.get('error')}")
                        return None
                else:
                    print(f"  v3 第{i+1}次: HTTP错误 {response.status_code}")
                    return None
                    
            except Exception as e:
                print(f"  v3 第{i+1}次: 异常 - {e}")
                return None
                
        return {
            'avg': statistics.mean(times),
            'min': min(times),
            'max': max(times),
            'times': times
        }
    
    def test_api_v3_validation(self, table_name):
        """测试API v3字段验证功能"""
        try:
            start_time = time.time()
            url = f"{self.base_url}{self.v3_prefix}/tables/{table_name}/validate"
            response = requests.get(url, timeout=30)
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'time': elapsed * 1000,
                    'match_rate': data.get('match_rate', 0),
                    'status': data.get('status', 'unknown')
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_table_comparison(self, table_name):
        """对比单个表的API v2和v3性能"""
        print(f"\n🔄 测试表: {table_name}")
        print("=" * 50)
        
        # 测试API v2
        print("📊 API v2测试:")
        v2_result = self.test_api_v2_data(table_name)
        
        # 测试API v3
        print("📊 API v3测试:")
        v3_result = self.test_api_v3_data(table_name)
        
        # 测试字段验证
        print("🔍 字段验证测试:")
        validation_result = self.test_api_v3_validation(table_name)
        if validation_result['success']:
            print(f"  验证: {validation_result['time']:.1f}ms, 匹配率: {validation_result['match_rate']}%")
        else:
            print(f"  验证失败: {validation_result['error']}")
        
        return {
            'table': table_name,
            'v2': v2_result,
            'v3': v3_result,
            'validation': validation_result
        }
    
    def run_full_comparison(self):
        """运行完整的性能对比测试"""
        print("🚀 API v3性能对比测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"基础URL: {self.base_url}")
        print(f"测试表: {', '.join(self.test_tables)}")
        
        results = []
        for table_name in self.test_tables:
            result = self.test_table_comparison(table_name)
            results.append(result)
        
        # 生成总结报告
        self.generate_summary_report(results)
        
        # 保存详细结果
        self.save_results(results)
        
        return results
    
    def generate_summary_report(self, results):
        """生成总结报告"""
        print("\n📈 性能对比总结")
        print("=" * 60)
        
        print(f"{'表名':<15} {'v2平均(ms)':<12} {'v3平均(ms)':<12} {'性能对比':<10} {'匹配率':<8}")
        print("-" * 60)
        
        total_v2_time = 0
        total_v3_time = 0
        successful_tests = 0
        
        for result in results:
            table = result['table']
            v2 = result['v2']
            v3 = result['v3']
            validation = result['validation']
            
            if v2 and v3:
                v2_avg = v2['avg']
                v3_avg = v3['avg']
                performance_ratio = v3_avg / v2_avg
                
                if performance_ratio < 1:
                    perf_indicator = f"↑{(1-performance_ratio)*100:.0f}%"
                else:
                    perf_indicator = f"↓{(performance_ratio-1)*100:.0f}%"
                
                match_rate = validation.get('match_rate', 0) if validation['success'] else 0
                
                print(f"{table:<15} {v2_avg:<12.1f} {v3_avg:<12.1f} {perf_indicator:<10} {match_rate}%")
                
                total_v2_time += v2_avg
                total_v3_time += v3_avg
                successful_tests += 1
            else:
                print(f"{table:<15} {'失败':<12} {'失败':<12} {'N/A':<10} {'N/A':<8}")
        
        if successful_tests > 0:
            overall_ratio = total_v3_time / total_v2_time
            print("-" * 60)
            if overall_ratio < 1:
                print(f"🎉 总体性能: API v3比v2快 {(1-overall_ratio)*100:.1f}%")
            else:
                print(f"⚠️  总体性能: API v3比v2慢 {(overall_ratio-1)*100:.1f}%")
            
            print(f"📊 成功测试: {successful_tests}/{len(results)} 个表")
    
    def save_results(self, results):
        """保存测试结果到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'api_v3_performance_test_{timestamp}.json'
        
        test_data = {
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'results': results,
            'summary': {
                'total_tables': len(self.test_tables),
                'successful_tests': len([r for r in results if r['v2'] and r['v3']])
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: {filename}")

def main():
    """主函数"""
    tester = APIPerformanceTester()
    
    if len(sys.argv) > 1:
        # 测试单个表
        table_name = sys.argv[1]
        if table_name in tester.test_tables:
            tester.test_table_comparison(table_name)
        else:
            print(f"❌ 不支持的表名: {table_name}")
            print(f"支持的表: {', '.join(tester.test_tables)}")
    else:
        # 运行完整测试
        tester.run_full_comparison()

if __name__ == "__main__":
    main() 