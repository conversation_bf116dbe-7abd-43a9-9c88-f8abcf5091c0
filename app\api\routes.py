from flask import jsonify, request, send_file, current_app
from app.api import bp
# API v1已废弃 - from app.api.compatibility_routes import register_compatibility_routes
from app import db
from app.models import Resource, ProductionOrder, ProductionSchedule, Product, CustomerOrder, OrderItem, User, MenuSetting, UserActionLog, SystemSetting, WIP_LOT, EQP_STATUS, ET_FT_TEST_SPEC, ET_RECIPE_FILE, AISettings, ProductPriorityConfig, UserFilterPresets, Settings, SchedulingTasks, DatabaseInfo, MigrationLog, UserPermission
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import pandas as pd
import io
import os
import json
from werkzeug.utils import secure_filename
import logging
from app.config.menu_config import MENU_CONFIG, MENU_ID_MAP, get_menu_by_id, get_all_menu_ids
import shutil
import traceback
import subprocess
import sys
import pymysql
from sqlalchemy import text
import time
from sqlalchemy import func

logger = logging.getLogger(__name__)

def get_db_connection(mysql_config=None, data_type='business'):
    """
    获取数据库连接 (100% MySQL)
    
    Args:
        mysql_config: 自定义MySQL配置字典，包含host, port, user, password, database等
        data_type: 'system'=系统配置数据库(aps_system), 'business'=业务数据库(aps)
    """
    
    # 如果提供了自定义MySQL配置，直接使用
    if mysql_config:
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database'],
            charset=mysql_config.get('charset', 'utf8mb4'),
            cursorclass=pymysql.cursors.DictCursor
        )
        return conn
    
    # 使用默认MySQL配置
    try:
        # 尝试从ORM模型读取AI设置中的MySQL配置
        ai_setting = db.session.query(AISettings).filter_by(id=1).first()
        
        mysql_config = None
        if ai_setting and ai_setting.settings:
            import json
            settings = json.loads(ai_setting.settings)
            db_settings = settings.get('database', {})
            if 'mysql' in db_settings:
                mysql_config = db_settings['mysql']
                
        # 如果没有找到AI设置中的配置，使用环境变量或默认配置
        if not mysql_config:
            mysql_config = {
                'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': current_app.config.get('MYSQL_PORT', 3306),
                'user': current_app.config.get('MYSQL_USER', 'root'),
                'password': current_app.config.get('MYSQL_PASSWORD', 'WWWwww123!'),
                'database': current_app.config.get('MYSQL_DATABASE', 'aps'),
                'charset': current_app.config.get('MYSQL_CHARSET', 'utf8mb4')
            }
        
        # 根据数据类型确定数据库名称
        if data_type == 'system':
            # 系统配置数据使用 aps_system 数据库
            mysql_config['database'] = mysql_config.get('system_database', 'aps_system')
        else:
            # 业务数据使用 aps 数据库
            mysql_config['database'] = mysql_config.get('database', 'aps')
        
        # 创建MySQL连接
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database'],
            charset=mysql_config.get('charset', 'utf8mb4'),
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=False  # 手动控制事务
        )
        
        current_app.logger.info(f"成功连接到MySQL数据库: {mysql_config['database']}")
        return conn
        
    except Exception as e:
        current_app.logger.error(f"MySQL数据库连接失败: {str(e)}")
        # 抛出异常，不再提供SQLite备选方案
        raise ConnectionError(f"无法连接到MySQL数据库: {str(e)}。请检查MySQL服务器状态和配置。")
    return jsonify({'status': 'ok'})

@login_required
def import_orders():
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 处理每一行数据
        for _, row in df.iterrows():
            # 创建订单
            order = CustomerOrder(
                order_number=f'ORD-{datetime.now().strftime("%Y%m%d")}-{len(orders)+1:04d}',
                customer_name=row['customer_name'],
                status='new'
            )
            
            # 创建订单项
            item = OrderItem(
                product_id=row['product_id'],
                quantity=row['quantity'],
                unit_price=row['unit_price'],
                total_price=row['quantity'] * row['unit_price']
            )
            order.items.append(item)
            order.total_amount = item.total_price
            
            db.session.add(order)
        
        db.session.commit()
        return jsonify({'status': 'success'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/production/schedules/<int:schedule_id>', methods=['PUT'])
@login_required
def update_schedule(schedule_id):
    schedule = ProductionSchedule.query.get_or_404(schedule_id)
    data = request.get_json()
    
    if 'resourceId' in data:
        schedule.resource_id = data['resourceId']
    if 'startTime' in data:
        schedule.start_time = datetime.fromisoformat(data['startTime'])
    if 'endTime' in data:
        schedule.end_time = datetime.fromisoformat(data['endTime'])
    if 'status' in data:
        schedule.status = data['status']
    
    try:
        db.session.commit()
        return jsonify({
            'id': schedule.id,
            'status': 'success'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/production/schedules/<int:schedule_id>', methods=['DELETE'])
@login_required
def delete_schedule(schedule_id):
    schedule = ProductionSchedule.query.get_or_404(schedule_id)
    
    # 更新订单状态
    order = schedule.production_order
    order.status = 'pending'
    order.scheduled_start = None
    order.scheduled_end = None
    
    # 更新资源状态
    resource = schedule.resource
    resource.status = 'available'
    
    try:
        db.session.delete(schedule)
        db.session.commit()
        return '', 204
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 用户管理API已迁移到 app/api/auth.py
# 为避免路由冲突，此处已删除重复的用户管理函数

# 删除重复的路由定义 - 这些功能已在 app/api/auth.py 中实现

# 批次上传
@bp.route('/production/batch/upload', methods=['POST'])
@login_required
def upload_batch():
    """上传生产批次文件"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if not file.filename:
        return jsonify({'error': 'No file selected'}), 400
    
    # 检查文件类型
    allowed_extensions = {'xlsx', 'xls', 'csv'}
    if not file.filename.rsplit('.', 1)[1].lower() in allowed_extensions:
        return jsonify({'error': 'Invalid file type'}), 400
    
    try:
        # 读取Excel文件
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file)
        else:
            df = pd.read_excel(file)
        
        # 验证必要的列
        required_columns = ['product_code', 'quantity', 'priority']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': 'Missing required columns'}), 400
        
        # 创建生产订单
        orders = []
        for _, row in df.iterrows():
            product = Product.query.filter_by(code=row['product_code']).first()
            if not product:
                continue
            
            order = ProductionOrder(
                order_number=f'PRD-{datetime.now().strftime("%Y%m%d")}-{len(orders)+1:04d}',
                product_id=product.id,
                quantity=row['quantity'],
                priority=row['priority'],
                status='pending',
                created_by=current_user.username
            )
            orders.append(order)
        
        db.session.bulk_save_objects(orders)
        db.session.commit()
        
        # 记录操作日志
        UserActionLog.log_action(
            current_user.username,
            'import',
            'ProductionOrder',
            None,
            f'批量导入生产订单，文件名: {file.filename}, 成功创建订单数: {len(orders)}'
        )
        
        return jsonify({
            'message': f'Successfully created {len(orders)} production orders',
            'order_count': len(orders)
        })
        
    except Exception as e:
        db.session.rollback()
        
        # 记录失败日志
        UserActionLog.log_action(
            current_user.username,
            'import',
            'ProductionOrder',
            None,
            f'批量导入生产订单失败，文件名: {file.filename}, 错误: {str(e)}'
        )
        
        return jsonify({'error': str(e)}), 500

@bp.route('/production/save-order', methods=['POST'])
@login_required
def save_order():
    """保存排序顺序"""
    try:
        data = request.get_json()
        if not data or 'orders' not in data:
            return jsonify({
                'success': False,
                'error': '缺少排序数据'
            }), 400

        # 创建数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # 检查是否是MySQL连接
            is_mysql = 'ping' in dir(conn)

            # 先检查order_index列是否存在，如果不存在则添加
            if is_mysql:
                # MySQL操作
                try:
                    cursor.execute("""
                        ALTER TABLE LOT_WIP
                        ADD COLUMN order_index INT
                    """)
                except Exception as e:
                    # 列已存在，忽略错误
                    pass

                # 先将所有记录的order_index重置为NULL
                cursor.execute("""
                    UPDATE LOT_WIP
                    SET order_index = NULL
                """)

                # 更新排序
                for order in data['orders']:
                    cursor.execute("""
                        UPDATE LOT_WIP
                        SET order_index = %s
                        WHERE lot_id = %s
                    """, (int(order['order_index']), order['lot_id']))
            else:
                # SQLite操作
                try:
                    cursor.execute("""
                        ALTER TABLE LOT_WIP
                        ADD COLUMN order_index INTEGER
                    """)
                except sqlite3.OperationalError:
                    # 列已存在，忽略错误
                    pass

                # 先将所有记录的order_index重置为NULL
                cursor.execute("""
                    UPDATE LOT_WIP
                    SET order_index = NULL
                """)

                # 更新排序
                for order in data['orders']:
                    cursor.execute("""
                        UPDATE LOT_WIP
                        SET order_index = ?
                        WHERE lot_id = ?
                    """, (int(order['order_index']), order['lot_id']))

            conn.commit()
            
            return jsonify({
                'success': True,
                'message': '排序保存成功'
            })

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"保存排序失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/production/save-priority-done', methods=['POST'])
@login_required
def save_priority_done():
    """保存数据到 LotPriorityDone 表"""
    try:
        data = request.get_json()
        if not data or 'records' not in data:
            return jsonify({
                'success': False,
                'error': '缺少数据记录'
            }), 400

        # 创建数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # 检查是否是MySQL连接
            is_mysql = 'ping' in dir(conn)

            # 清空现有数据
            if is_mysql:
                # MySQL操作
                cursor.execute("DELETE FROM LotPriorityDone")
            
            conn.commit()
            
            # 安全地处理字符串
            def safe_strip(value):
                if value is None:
                    return None
                if isinstance(value, str):
                    return value.strip() or None
                return str(value) or None
            
            # 按HANDLER_ID分组
            grouped_records = {}
            for record in data['records']:
                handler_id = safe_strip(record.get('HANDLER_ID')) or 'unknown'
                if handler_id not in grouped_records:
                    grouped_records[handler_id] = []
                grouped_records[handler_id].append(record)
            
            # 为每组分别编号并插入数据
            total_inserted = 0
            for handler_id, records in grouped_records.items():
                for index, record in enumerate(records, 1):
                    try:
                        # 根据数据库类型使用不同的占位符
                        if is_mysql:
                            # MySQL使用%s占位符
                            cursor.execute("""
                                INSERT INTO LotPriorityDone (
                                    ORDER_INDEX, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                                    PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID,
                                    STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID,
                                    FLOW_VER, RELEASE_TIME, FAC_ID, CREATE_TIME,
                                    UPDATE_TIME
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                            """, (
                                record.get('ORDER', index),  # 排序序号
                                safe_strip(record.get('HANDLER_ID')),
                                safe_strip(record.get('LOT_ID')),  # LOT_ID是必需的
                                safe_strip(record.get('LOT_TYPE')),
                                record.get('GOOD_QTY', 0),
                                safe_strip(record.get('PROD_ID')),
                                safe_strip(record.get('DEVICE')),
                                safe_strip(record.get('CHIP_ID')),
                                safe_strip(record.get('PKG_PN')),
                                safe_strip(record.get('PO_ID')),
                                safe_strip(record.get('STAGE')),
                                safe_strip(record.get('WIP_STATE')),
                                safe_strip(record.get('PROC_STATE')),
                                safe_strip(record.get('HOLD_STATE')),
                                safe_strip(record.get('FLOW_ID')),
                                record.get('FLOW_VER', 0),
                                safe_strip(record.get('RELEASE_TIME')),
                                safe_strip(record.get('FAC_ID')),
                                safe_strip(record.get('CREATE_TIME'))
                            ))
                        else:
                            # SQLite使用?占位符
                            cursor.execute("""
                                INSERT INTO LotPriorityDone (
                                    ORDER_INDEX, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                                    PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID,
                                    STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID,
                                    FLOW_VER, RELEASE_TIME, FAC_ID, CREATE_TIME,
                                    UPDATE_TIME
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                            """, (
                                record.get('ORDER', index),  # 排序序号
                                safe_strip(record.get('HANDLER_ID')),
                                safe_strip(record.get('LOT_ID')),  # LOT_ID是必需的
                                safe_strip(record.get('LOT_TYPE')),
                                record.get('GOOD_QTY', 0),
                                safe_strip(record.get('PROD_ID')),
                                safe_strip(record.get('DEVICE')),
                                safe_strip(record.get('CHIP_ID')),
                                safe_strip(record.get('PKG_PN')),
                                safe_strip(record.get('PO_ID')),
                                safe_strip(record.get('STAGE')),
                                safe_strip(record.get('WIP_STATE')),
                                safe_strip(record.get('PROC_STATE')),
                                safe_strip(record.get('HOLD_STATE')),
                                safe_strip(record.get('FLOW_ID')),
                                record.get('FLOW_VER', 0),
                                safe_strip(record.get('RELEASE_TIME')),
                                safe_strip(record.get('FAC_ID')),
                                safe_strip(record.get('CREATE_TIME'))
                            ))
                        total_inserted += 1
                    except Exception as e:
                        logger.error(f"Error inserting record: {record}, Error: {str(e)}")
                        raise e

            conn.commit()
            logger.info(f"Successfully saved {total_inserted} records to LotPriorityDone")
            
            return jsonify({
                'success': True,
                'message': f"成功保存 {total_inserted} 条记录"
            })

        except Exception as e:
            conn.rollback()
            logger.error(f"Error in save_priority_done: {str(e)}")
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"详细错误信息: {error_details}")
            
            # 提供更友好的错误信息
            error_message = str(e)
            if 'no such table' in error_message.lower():
                error_message = "LotPriorityDone表不存在，请运行数据库初始化"
            elif 'connection' in error_message.lower():
                error_message = "数据库连接失败，请检查数据库配置"
            elif 'permission denied' in error_message.lower():
                error_message = "数据库权限不足，请联系管理员"
            elif 'constraint' in error_message.lower():
                error_message = "数据约束冲突，请检查数据有效性"
            elif 'timeout' in error_message.lower():
                error_message = "数据库操作超时，请稍后重试"
            elif 'locked' in error_message.lower():
                error_message = "数据库被锁定，请稍后重试"
            
            return jsonify({
                'success': False,
                'error': error_message,
                'message': f'保存失败: {error_message}',
                'error_type': 'database_error',
                'can_retry': 'timeout' in error_message.lower() or 'connection' in error_message.lower() or 'locked' in error_message.lower()
            }), 500
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"Error processing request in save_priority_done: {str(e)}")
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"详细错误信息: {error_details}")
        
        # 提供更友好的错误信息
        error_message = str(e)
        if 'json' in error_message.lower():
            error_message = "请求数据格式错误，请检查JSON格式"
        elif 'missing' in error_message.lower():
            error_message = "缺少必要的请求参数"
        elif 'invalid' in error_message.lower():
            error_message = "请求参数无效"
        
        return jsonify({
            'success': False,
            'error': error_message,
            'message': f'请求处理失败: {error_message}',
            'error_type': 'request_error'
        }), 500

@bp.route('/production/import-from-directory', methods=['POST'])
@login_required
def import_from_directory():
    data = request.get_json()
    if 'path' not in data:
        return jsonify({'error': 'Path is required'}), 400
        
    path = data['path']
    
    try:
        # 转换路径格式
        path = os.path.normpath(path)
        path = os.path.abspath(path)  # 转换为绝对路径
        
        # 验证路径是否存在
        if not os.path.exists(path):
            return jsonify({'error': f'目录不存在: {path}'}), 400
            
        if not os.path.isdir(path):
            return jsonify({'error': f'指定路径不是目录: {path}'}), 400
        
        # 获取数据库路径
        db_path = os.path.join(current_app.instance_path, 'aps.db')
        logger.info(f"使用SQLite数据库: {db_path}")
        
        # 导入Excel数据到MySQL
        try:
            # 导入MySQL版本的导入模块
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from import_excel_to_mysql import import_from_directory as import_excel
            
            # 调用导入函数（MySQL模式不需要db_path）
            success, result = import_excel(path)
            
            if success:
                # 导入成功，返回详细结果信息
                # 确保兼容旧格式也能正常工作
                if isinstance(result, dict):
                    return jsonify({
                        'success': True,
                        'message': result.get('message', '导入成功'),
                        'details': result.get('details', ''),
                        'processed_files': result.get('processed_files', []),
                        'failed_files': result.get('failed_files', []),
                        'total_files': result.get('total_files', 0),
                        'failed_count': result.get('failed_count', 0),
                        'total_records': result.get('total_records', 0),
                        'processing_time': result.get('processing_time', 0)
                    })
                else:
                    # 处理旧格式的返回值
                    return jsonify({
                        'success': True,
                        'message': result if isinstance(result, str) else '导入成功'
                    })
            else:
                # 导入失败，返回错误信息
                if isinstance(result, dict):
                    return jsonify({
                        'success': False,
                        'error': result.get('error', '导入失败'),
                        'message': result.get('message', '导入失败')
                    }), 500
                else:
                    return jsonify({
                        'success': False,
                        'error': result if isinstance(result, str) else '导入失败'
                    }), 500
                
        except ImportError as e:
            logger.error(f"导入模块失败: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'导入模块失败: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f"导入过程发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'导入过程发生错误: {str(e)}'
        }), 500

@bp.route('/test-database-connection', methods=['POST'])
@login_required
def test_database_connection():
    """测试数据库连接"""
    try:
        data = request.get_json()
        if not data or 'type' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400
        
        db_type = data.get('type')
        
        if db_type == 'sqlite':
            # 测试SQLite连接
            try:
                # 使用get_db_connection来测试SQLite连接
                data_type = data.get('data_type', 'business')
                conn = get_db_connection(data_type=data_type)
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                table_count = len(tables)
                
                # 关闭连接
                conn.close()
                
                return jsonify({
                    'success': True,
                    'message': f'成功连接到SQLite数据库，发现{table_count}个表。'
                })
            except Exception as e:
                logger.error(f"SQLite连接测试失败: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': f'SQLite连接失败: {str(e)}'
                })
        
        elif db_type == 'mysql':
            # 测试MySQL连接
            mysql_config = data.get('config', {})
            
            if not all(key in mysql_config for key in ['host', 'port', 'user', 'password', 'database']):
                return jsonify({
                    'success': False,
                    'error': 'MySQL配置信息不完整'
                }), 400
            
            try:
                # 尝试连接MySQL数据库
                conn = pymysql.connect(
                    host=mysql_config['host'],
                    port=int(mysql_config['port']),
                    user=mysql_config['user'],
                    password=mysql_config['password'],
                    database=mysql_config['database'],
                    charset='utf8mb4',
                    cursorclass=pymysql.cursors.DictCursor
                )
                
                # 检查表是否存在
                cursor = conn.cursor()
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                table_count = len(tables)
                
                # 关闭连接
                conn.close()
                
                return jsonify({
                    'success': True,
                    'message': f'成功连接到MySQL数据库，发现{table_count}个表。'
                })
            except Exception as e:
                logger.error(f"MySQL连接测试失败: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': f'MySQL连接失败: {str(e)}'
                })
        
        else:
            return jsonify({
                'success': False,
                'error': f'不支持的数据库类型: {db_type}'
            }), 400
    
    except Exception as e:
        logger.error(f"测试数据库连接失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/database-status', methods=['GET', 'POST'])
@login_required
def get_database_status():
    """获取当前数据库连接状态"""
    try:
        # 准备结果对象
        result = {
            'success': True,
            'databases': []
        }
        
        # 1. 检查系统SQLite数据库 (system.db)
        try:
            system_conn = get_db_connection(data_type='system')
            system_cursor = system_conn.cursor()
            
            # 获取版本信息
            system_cursor.execute("SELECT sqlite_version()")
            sqlite_version = system_cursor.fetchone()[0]
            
            # 检查表是否存在
            system_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            sqlite_tables = system_cursor.fetchall()
            sqlite_table_count = len(sqlite_tables)
            
            # 关闭连接
            system_conn.close()
            
            # 添加SQLite数据库信息
            result['databases'].append({
                'name': 'system.db',
                'type': 'SQLite',
                'path': current_app.config.get('SYSTEM_DB_PATH', 'instance/system.db'),
                'status': 'connected',
                'version': sqlite_version,
                'table_count': sqlite_table_count
            })
            
        except Exception as e:
            logger.error(f"系统SQLite数据库连接失败: {str(e)}")
            result['databases'].append({
                'name': 'system.db',
                'type': 'SQLite',
                'status': 'failed',
                'error': str(e)
            })
        
        # 2. 检查MySQL数据库连接
        try:
            from flask import current_app
            import pymysql
            
            # 从请求中获取MySQL配置，如果请求中没有则使用应用配置
            form_data = request.get_json() or {}
            mysql_settings = form_data.get('mysql_settings', {})
            
            mysql_config = {
                'host': mysql_settings.get('host') or current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': int(mysql_settings.get('port', 0)) or current_app.config.get('MYSQL_PORT', 3306),
                'user': mysql_settings.get('user') or current_app.config.get('MYSQL_USER', 'root'),
                'password': mysql_settings.get('password') or current_app.config.get('MYSQL_PASSWORD', 'WWWwww123!'),
                'database': mysql_settings.get('database') or current_app.config.get('MYSQL_DATABASE', 'aps'),
                'charset': mysql_settings.get('charset') or current_app.config.get('MYSQL_CHARSET', 'utf8mb4'),
                'cursorclass': pymysql.cursors.DictCursor
            }
            
            # 使用自定义的MySQL配置连接数据库
            mysql_conn = pymysql.connect(**mysql_config)
            mysql_cursor = mysql_conn.cursor()
            
            # 获取MySQL版本
            mysql_cursor.execute("SELECT VERSION() as version")
            version_info = mysql_cursor.fetchone()
            mysql_version = version_info['version'] if isinstance(version_info, dict) else version_info[0]
            
            # 检查表列表
            mysql_cursor.execute("SHOW TABLES")
            mysql_tables = mysql_cursor.fetchall()
            
            # 提取表名
            if mysql_tables and len(mysql_tables) > 0:
                if isinstance(mysql_tables[0], dict):
                    table_key = list(mysql_tables[0].keys())[0]  # 获取字典的第一个键
                    mysql_table_count = len(mysql_tables)
                else:
                    mysql_table_count = len(mysql_tables)
            else:
                mysql_table_count = 0
            
            # 关闭连接
            mysql_conn.close()
            
            # 添加MySQL数据库信息
            result['databases'].append({
                'name': mysql_config['database'],
                'type': 'MySQL',
                'host': mysql_config['host'],
                'port': mysql_config['port'],
                'status': 'connected',
                'version': mysql_version,
                'table_count': mysql_table_count
            })
            
        except Exception as e:
            logger.error(f"MySQL数据库连接失败: {str(e)}")
            result['databases'].append({
                'name': current_app.config.get('MYSQL_DATABASE', 'aps'),
                'type': 'MySQL',
                'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': current_app.config.get('MYSQL_PORT', 3306),
                'status': 'failed',
                'error': str(e)
            })
        
        # 3. 获取当前配置使用的数据库类型
        db_type = current_app.config.get('AUTO_DB_TYPE', 'sqlite')
        result['current_type'] = db_type
        
        # 如果所有数据库连接都失败，整体状态设为失败
        if all(db['status'] == 'failed' for db in result['databases']):
            result['success'] = False
            result['error'] = '所有数据库连接均失败'
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"获取数据库状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@login_required
def database_config():
    """获取或更新数据库配置"""
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403
    
    if request.method == 'GET':
        # 获取当前配置
        from flask import current_app
        return jsonify({
            'success': True,
            'config': {
                'type': current_app.config.get('AUTO_DB_TYPE', 'sqlite'),
                'sqlite': {
                    'path': current_app.config.get('AUTO_DB_SQLITE_PATH', 'instance/auto.db')
                },
                'mysql': {
                    'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                    'port': current_app.config.get('MYSQL_PORT', 3306),
                    'user': current_app.config.get('MYSQL_USER', 'root'),
                    'password': current_app.config.get('MYSQL_PASSWORD', ''),
                    'database': current_app.config.get('MYSQL_DATABASE', 'aps'),
                    'charset': current_app.config.get('MYSQL_CHARSET', 'utf8mb4')
                }
            }
        })
    else:
        # 更新配置
        try:
            data = request.get_json()
            if not data or 'config' not in data:
                return jsonify({
                    'success': False,
                    'error': '缺少配置数据'
                }), 400
            
            config = data['config']
            
            # 验证配置
            if 'type' not in config:
                return jsonify({
                    'success': False,
                    'error': '缺少数据库类型配置'
                }), 400
            
            # 获取配置文件路径
            import os
            from flask import current_app
            config_file = os.path.join(os.path.dirname(current_app.root_path), '.env')
            
            # 更新配置
            env_vars = []
            env_vars.append(f"AUTO_DB_TYPE={config['type']}")
            
            if config['type'] == 'sqlite':
                if 'sqlite' in config and 'path' in config['sqlite']:
                    env_vars.append(f"AUTO_DB_SQLITE_PATH={config['sqlite']['path']}")
            
            elif config['type'] == 'mysql':
                if 'mysql' in config:
                    mysql_config = config['mysql']
                    if 'host' in mysql_config:
                        env_vars.append(f"MYSQL_HOST={mysql_config['host']}")
                    if 'port' in mysql_config:
                        env_vars.append(f"MYSQL_PORT={mysql_config['port']}")
                    if 'user' in mysql_config:
                        env_vars.append(f"MYSQL_USER={mysql_config['user']}")
                    if 'password' in mysql_config:
                        env_vars.append(f"MYSQL_PASSWORD={mysql_config['password']}")
                    if 'database' in mysql_config:
                        env_vars.append(f"MYSQL_DATABASE={mysql_config['database']}")
                    if 'charset' in mysql_config:
                        env_vars.append(f"MYSQL_CHARSET={mysql_config['charset']}")
            
            # 写入.env文件
            with open(config_file, 'w') as f:
                for var in env_vars:
                    f.write(f"{var}\n")
            
            # 更新当前应用的配置
            from flask import current_app
            current_app.config['AUTO_DB_TYPE'] = config['type']
            
            if config['type'] == 'sqlite':
                if 'sqlite' in config and 'path' in config['sqlite']:
                    current_app.config['AUTO_DB_SQLITE_PATH'] = config['sqlite']['path']
            
            elif config['type'] == 'mysql':
                if 'mysql' in config:
                    mysql_config = config['mysql']
                    if 'host' in mysql_config:
                        current_app.config['MYSQL_HOST'] = mysql_config['host']
                    if 'port' in mysql_config:
                        current_app.config['MYSQL_PORT'] = int(mysql_config['port'])
                    if 'user' in mysql_config:
                        current_app.config['MYSQL_USER'] = mysql_config['user']
                    if 'password' in mysql_config:
                        current_app.config['MYSQL_PASSWORD'] = mysql_config['password']
                    if 'database' in mysql_config:
                        current_app.config['MYSQL_DATABASE'] = mysql_config['database']
                    if 'charset' in mysql_config:
                        current_app.config['MYSQL_CHARSET'] = mysql_config['charset']
            
            # 记录用户操作
            UserActionLog.log_action(
                current_user.username,
                'update',
                'DatabaseConfig',
                None,
                f'更新数据库配置: 类型={config["type"]}'
            )
            
            return jsonify({
                'success': True,
                'message': '数据库配置已更新'
            })
            
        except Exception as e:
            logger.error(f"更新数据库配置失败: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

@bp.route('/production/history-data', methods=['GET'])
def get_history_data():
    """获取指定时间点的历史数据"""
    try:
        timestamp = request.args.get('timestamp')
        if not timestamp:
            return jsonify({
                'success': False,
                'error': '未提供时间戳'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查是否是MySQL连接
        is_mysql = 'ping' in dir(conn)
        
        # 获取指定时间点的所有数据
        if is_mysql:
            # MySQL操作
            cursor.execute("""
                SELECT 
                    SN,
                    HANDLER_ID,
                    TESTER_ID,
                    DEVICE,
                    PKG_PN,
                    CHIP_ID,
                    LOT_ID,
                    STAGE,
                    TB_PN,
                    HB_PN,
                    HANDLER_CONFIG,
                    KIT_PN,
                    SOCKET_PN,
                    Priority,
                    UPDATE_TIME
                FROM LotPriorityDone 
                WHERE UPDATE_TIME = %s
                ORDER BY SN ASC
            """, (timestamp,))
            
            # MySQL的cursor.fetchall()已经是字典列表
            data = cursor.fetchall()
        else:
            # SQLite操作
            cursor.execute("""
                SELECT 
                    SN,
                    HANDLER_ID,
                    TESTER_ID,
                    DEVICE,
                    PKG_PN,
                    CHIP_ID,
                    LOT_ID,
                    STAGE,
                    TB_PN,
                    HB_PN,
                    HANDLER_CONFIG,
                    KIT_PN,
                    SOCKET_PN,
                    Priority,
                    UPDATE_TIME
                FROM LotPriorityDone 
                WHERE UPDATE_TIME = ?
                ORDER BY SN ASC
            """, (timestamp,))
            
            # 获取列名
            columns = [description[0] for description in cursor.description]
            
            # 构建数据列表
            data = []
            for row in cursor.fetchall():
                data.append(dict(zip(columns, row)))
        
        conn.close()
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        logger.error(f"获取历史数据失败: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/test-all-database-connections', methods=['POST'])
@login_required
def test_all_database_connections():
    """测试所有关联数据库连接"""
    try:
        result = {
            'success': True,
            'databases': []
        }
        
        # 1. 测试系统SQLite数据库 (用于系统配置)
        try:
            system_conn = get_db_connection(data_type='system')
            cursor = system_conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            table_count = len(tables)
            
            # 关闭连接
            system_conn.close()
            
            result['databases'].append({
                'name': 'system.db',
                'type': 'SQLite',
                'path': current_app.config.get('SYSTEM_DB_PATH', 'instance/system.db'),
                'status': 'connected',
                'table_count': table_count,
                'tables': table_names
            })
        except Exception as e:
            logger.error(f"system.db连接测试失败: {str(e)}")
            result['databases'].append({
                'name': 'system.db',
                'type': 'SQLite',
                'path': current_app.config.get('SYSTEM_DB_PATH', 'instance/system.db'),
                'status': 'failed',
                'error': str(e)
            })
        
        # 2. 测试业务数据库 (MySQL/SQLite)
        try:
            business_conn = get_db_connection(data_type='business')
            is_mysql = hasattr(business_conn, 'ping')
            
            if is_mysql:
                cursor = business_conn.cursor()
                
                # 获取MySQL版本
                cursor.execute("SELECT VERSION() as version")
                version_info = cursor.fetchone()
                version = version_info['version'] if isinstance(version_info, dict) else version_info[0]
                
                # 检查表列表
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables and len(tables) > 0:
                    if isinstance(tables[0], dict):
                        table_key = list(tables[0].keys())[0]
                        table_names = [table[table_key] for table in tables]
                    else:
                        table_names = [table[0] for table in tables]
                else:
                    table_names = []
                
                result['databases'].append({
                    'name': 'MySQL Business DB',
                    'type': 'MySQL',
                    'status': 'connected',
                    'version': version,
                    'table_count': len(tables),
                    'tables': table_names
                })
            else:
                cursor = business_conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            table_count = len(tables)
            
            result['databases'].append({
                    'name': 'SQLite Business DB',
                'type': 'SQLite',
                'status': 'connected',
                'table_count': table_count,
                'tables': table_names
            })
            
            business_conn.close()
            
        except Exception as e:
            logger.error(f"业务数据库连接测试失败: {str(e)}")
            result['databases'].append({
                'name': 'Business DB',
                'type': 'Unknown',
                'status': 'failed',
                'error': str(e)
            })
        
        # 3. 测试MySQL数据库
        try:
            # 从当前请求中获取用户界面设置的MySQL配置
            form_data = request.get_json() or {}
            mysql_settings = form_data.get('mysql_settings', {})
            
            # 如果请求中包含MySQL配置信息，则使用该配置
            # 否则使用应用配置中的值
            mysql_config = {
                'host': mysql_settings.get('host') or current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': int(mysql_settings.get('port', 0)) or current_app.config.get('MYSQL_PORT', 3306),
                'user': mysql_settings.get('user') or current_app.config.get('MYSQL_USER', 'root'),
                'password': mysql_settings.get('password') or current_app.config.get('MYSQL_PASSWORD', 'WWWwww123!'),
                'database': mysql_settings.get('database') or current_app.config.get('MYSQL_DATABASE', 'aps'),
                'charset': mysql_settings.get('charset') or current_app.config.get('MYSQL_CHARSET', 'utf8mb4'),
                'cursorclass': pymysql.cursors.DictCursor
            }
            
            conn = pymysql.connect(**mysql_config)
            cursor = conn.cursor()
            
            # 获取MySQL版本
            cursor.execute("SELECT VERSION() as version")
            version_info = cursor.fetchone()
            version = version_info['version'] if isinstance(version_info, dict) else version_info[0]
            
            # 检查表列表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            # 提取表名
            if tables and len(tables) > 0:
                if isinstance(tables[0], dict):
                    table_key = list(tables[0].keys())[0]  # 获取字典的第一个键
                    table_names = [table[table_key] for table in tables]
                else:
                    table_names = [table[0] for table in tables]
            else:
                table_names = []
                
            table_count = len(tables)
            
            # 关闭连接
            conn.close()
            
            result['databases'].append({
                'name': mysql_config['database'],
                'type': 'MySQL',
                'host': mysql_config['host'],
                'port': mysql_config['port'],
                'status': 'connected',
                'version': version,
                'table_count': table_count,
                'tables': table_names
            })
        except Exception as e:
            logger.error(f"MySQL连接测试失败: {str(e)}")
            result['databases'].append({
                'name': current_app.config.get('MYSQL_DATABASE', 'aps'),
                'type': 'MySQL',
                'host': current_app.config.get('MYSQL_HOST', '127.0.0.1'),
                'port': current_app.config.get('MYSQL_PORT', 3306),
                'status': 'failed',
                'error': str(e)
            })
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"测试所有数据库连接失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/production/auto-schedule/stop', methods=['POST'])
@login_required
def stop_auto_schedule():
    """停止正在执行的排产进程"""
    try:
        # 在实际环境中，这里应该有代码来停止排产算法
        # 由于我们没有实际的后台排产进程，这里只返回成功响应
        logger.info("收到停止排产请求")
        
        # 记录用户操作
        if 'current_user' in globals() and hasattr(current_user, 'username'):
            logger.info(f"用户 {current_user.username} 请求停止排产")
        
        return jsonify({
            'success': True,
            'message': "排产已停止"
        })
    except Exception as e:
        logger.error(f"停止排产失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"停止排产失败: {str(e)}"
        }), 500

@login_required
def init_database_api():
    """初始化数据库API接口"""
    from init_db import init_db
    import traceback
    
    try:
        success = init_db()
        
        if success:
            return jsonify({
                'success': True,
                'message': '数据库初始化成功！已创建所有排产所需的表结构'
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据库初始化失败',
                'error': '请查看服务器日志以获取详细信息'
            }), 500
            
    except Exception as e:
        error_info = traceback.format_exc()
        logger.error(f"数据库初始化失败: {str(e)}\n{error_info}")
        
        return jsonify({
            'success': False,
            'message': '数据库初始化失败',
            'error': str(e)
        }), 500

@bp.route('/production/save-import-path', methods=['POST'])
@login_required
def save_import_path():
    """保存Excel导入路径"""
    try:
        data = request.get_json()
        
        if not data or 'path' not in data:
            return jsonify({'success': False, 'error': '缺少路径参数'}), 400
            
        path = data['path'].strip()
        
        # 验证路径是否存在
        if not os.path.exists(path):
            return jsonify({'success': False, 'error': f'路径不存在: {path}'}), 400
            
        # 保存路径到用户设置（系统数据库）
        username = current_user.username
        setting = db.session.query(SystemSetting).filter_by(
            key='import_excel_path',
            user_id=username
        ).first()
        
        if setting:
            setting.value = path
        else:
            setting = SystemSetting(
                user_id=username,
                key='import_excel_path',
                value=path,
                description='Excel导入路径',
                setting_type='path'
            )
            db.session.add(setting)
            
        db.session.commit()
        return jsonify({'success': True, 'message': '路径保存成功'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"保存路径错误: {str(e)}")
        return jsonify({'success': False, 'error': f'保存失败: {str(e)}'}), 500

@bp.route('/production/get-import-path', methods=['GET'])
@login_required
def get_import_path():
    """获取Excel导入路径"""
    try:
        username = current_user.username
        setting = db.session.query(SystemSetting).filter_by(
            key='import_excel_path',
            user_id=username
        ).first()
        
        if setting and setting.value:
            return jsonify({'success': True, 'path': setting.value})
        else:
            return jsonify({'success': True, 'path': ''})
            
    except Exception as e:
        logger.error(f"获取路径错误: {str(e)}")
        return jsonify({'success': False, 'error': f'获取失败: {str(e)}'}), 500

@bp.route('/production/imported-files', methods=['GET'])
@login_required
def get_imported_files():
    """获取已导入的Excel文件列表"""
    try:
        # 使用正确的数据库连接
        conn = get_db_connection(data_type='business')
        is_mysql = hasattr(conn, 'ping')
        
        if is_mysql:
            cursor = conn.cursor()
        else:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
        
        # 查询所有表结构，查找可能的已导入表
        if is_mysql:
            # MySQL中表名都是小写
            excel_tables = ['ct', 'et_ft_test_spec', 'et_recipe_file', 'et_uph_eqp', 'tcc_inv', 'wip_lot', 'et_wait_lot', 'eqp_status']
        else:
            excel_tables = ['CT', 'ET_FT_TEST_SPEC', 'ET_RECIPE_FILE', 'ET_UPH_EQP', 'TCC_INV', 'wip_lot', 'ET_WAIT_LOT', 'eqp_status']
        
        files = []
        for table in excel_tables:
            try:
                # 检查表是否存在
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                result = cursor.fetchone()
                if is_mysql:
                    count = result['count']
                else:
                    count = result['count']
                
                # 将表名转换为文件名
                if table == 'eqp_status':
                    filename = "EQP_STATUS.xlsx"  # 为eqp_status表使用EQP_STATUS作为显示名称
                else:
                    filename = f"{table}.xlsx"
                
                files.append({
                    'name': filename,
                    'table': table,
                    'records': count
                })
            except Exception as e:
                logger.error(f"检查表 {table} 时出错: {str(e)}")
                continue
        
        conn.close()
        return jsonify({'success': True, 'files': files})
        
    except Exception as e:
        logger.error(f"获取已导入文件列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/production/file-data/<filename>', methods=['GET'])
@login_required
def get_file_data(filename):
    """获取导入的文件数据"""
    try:
        # 连接数据库
        conn = get_db_connection(data_type='business')
        is_mysql = hasattr(conn, 'ping')
        
        if is_mysql:
            cursor = conn.cursor()
                        # 从文件名解析表名 - MySQL中表名都是小写
            if filename == "EQP_STATUS.xlsx":
                table_name = "eqp_status"
            else:
                table_name = os.path.splitext(filename)[0].lower()
               
        # 获取页码
        page = request.args.get('page', 1, type=int)
        per_page = 20  # 每页显示的记录数
        
        # 获取表的列名
        if is_mysql:
            cursor.execute(f"DESCRIBE {table_name}")
            columns = [row['Field'] for row in cursor.fetchall()]
        else:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row['name'] for row in cursor.fetchall()]
        
        # 排除id、created_at和updated_at列
        display_columns = [col for col in columns if col.lower() not in ('id', 'created_at', 'updated_at')]
        
        # 获取总记录数
        cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        result = cursor.fetchone()
        if is_mysql:
            total_records = result['count']
        else:
            total_records = result['count']
        
        # 计算总页数
        total_pages = (total_records + per_page - 1) // per_page
        
        # 计算OFFSET
        offset = (page - 1) * per_page
        
        # 获取分页数据
        cursor.execute(f"SELECT * FROM {table_name} LIMIT {per_page} OFFSET {offset}")
        rows = cursor.fetchall()
        
        # 转换为字典列表
        data = []
        for row in rows:
            row_dict = {}
            for col in display_columns:
                row_dict[col] = row[col]
            data.append(row_dict)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'columns': display_columns,
            'data': data,
            'page': page,
            'total_pages': total_pages,
            'total_records': total_records
        })
        
    except Exception as e:
        logger.error(f"获取文件 {filename} 数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"获取数据失败: {str(e)}"
        }), 500

@bp.route('/production/export-file/<filename>', methods=['GET'])
@login_required
def export_file(filename):
    """导出数据为Excel文件"""
    try:
        # 连接数据库
        conn = get_db_connection(data_type='business')
        is_mysql = hasattr(conn, 'ping')
        
        if is_mysql:
            cursor = conn.cursor()
            # MySQL中表名都是小写
            if filename == "EQP_STATUS.xlsx":
                table_name = "eqp_status"
            else:
                table_name = os.path.splitext(filename)[0].lower()
               
        # 获取表的列名
        if is_mysql:
            cursor.execute(f"DESCRIBE {table_name}")
            columns = [row['Field'] for row in cursor.fetchall()]
        else:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row['name'] for row in cursor.fetchall()]
        
        # 排除id、created_at和updated_at列
        display_columns = [col for col in columns if col.lower() not in ('id', 'created_at', 'updated_at')]
        
        # 获取所有数据
        cursor.execute(f"SELECT {', '.join(display_columns)} FROM {table_name}")
        rows = cursor.fetchall()
        
        # 转换为字典列表
        data = []
        for row in rows:
            row_dict = {}
            for col in display_columns:
                row_dict[col] = row[col]
            data.append(row_dict)
        
        conn.close()
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Sheet1', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        logger.error(f"导出文件 {filename} 失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"导出失败: {str(e)}"
        }), 500

@login_required
def delete_files():
    """删除已导入的文件（清空数据表）"""
    try:
        data = request.get_json()
        if not data or 'files' not in data:
            return jsonify({'success': False, 'error': '缺少文件列表'}), 400
            
        files = data['files']
        
        conn = get_db_connection(data_type='business')
        is_mysql = hasattr(conn, 'ping')
        cursor = conn.cursor()
        
        deleted_count = 0
        for filename in files:
            # 从文件名解析表名
            if is_mysql:
                # MySQL中表名都是小写
                if filename == "EQP_STATUS.xlsx":
                    table_name = "eqp_status"
                else:
                    table_name = os.path.splitext(filename)[0].lower()
            
            
            try:
                # 清空表
                cursor.execute(f"DELETE FROM {table_name}")
                deleted_count += 1
            except Exception as e:
                logger.error(f"删除表 {table_name} 数据失败: {str(e)}")
                continue
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'成功清空 {deleted_count} 个表的数据'
        })
        
    except Exception as e:
        logger.error(f"删除文件数据失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/production/import-progress', methods=['GET'])
@login_required
def get_import_progress():
    """获取导入进度"""
    try:
        # 检查是否有导入进度文件
        progress_file = os.path.join(current_app.instance_path, 'import_progress.json')
        if os.path.exists(progress_file):
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
                
            # 如果进度是100%，保持completed状态30秒，然后删除文件
            # 如果文件创建时间超过5分钟，直接删除
            file_age = time.time() - os.path.getmtime(progress_file)
            
            if progress_data.get('percent', 0) >= 100:
                # 导入完成，保持completed状态30秒
                if file_age > 30:  # 30秒后删除
                    try:
                        os.remove(progress_file)
                    except:
                        pass
                    # 返回空闲状态
                    return jsonify({
                        'percent': 0,
                        'message': '没有正在进行的导入任务',
                        'status': 'idle',
                        'files_processed': 0,
                        'total_files': 0
                    })
                else:
                    # 返回完成状态
                    return jsonify({
                        'percent': 100,
                        'message': '导入已完成',
                        'status': 'completed',
                        'files_processed': progress_data.get('files_processed', 0),
                        'total_files': progress_data.get('total_files', 0)
                    })
            elif file_age > 300:  # 5分钟 = 300秒，处理异常情况
                try:
                    os.remove(progress_file)
                except:
                    pass
                return jsonify({
                    'percent': 0,
                    'message': '没有正在进行的导入任务',
                    'status': 'idle',
                    'files_processed': 0,
                    'total_files': 0
                })
                
            return jsonify(progress_data)
        else:
            # 默认返回空闲状态
            return jsonify({
                'percent': 0,
                'message': '没有正在进行的导入任务',
                'status': 'idle',
                'files_processed': 0,
                'total_files': 0
            })
            
    except Exception as e:
        logger.error(f"获取导入进度失败: {str(e)}")
        return jsonify({
            'percent': 0,
            'message': f'获取进度失败: {str(e)}',
            'status': 'error',
            'error': True
        }), 500

@login_required
def clear_import_progress():
    """清理导入进度文件"""
    try:
        progress_file = os.path.join(current_app.instance_path, 'import_progress.json')
        if os.path.exists(progress_file):
            os.remove(progress_file)
            return jsonify({'success': True, 'message': '进度文件已清理'})
        else:
            return jsonify({'success': True, 'message': '没有进度文件需要清理'})
    except Exception as e:
        logger.error(f"清理进度文件失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/production/delete-file', methods=['POST'])
@login_required
def delete_file():
    """删除单个导入的Excel数据表"""
    try:
        data = request.get_json()
        if not data or 'file' not in data:
            return jsonify({
                'success': False,
                'error': '请求数据不完整'
            })
        
        filename = data['file']
        
        # 连接数据库
        conn = get_db_connection(data_type='business')
        is_mysql = hasattr(conn, 'ping')
        cursor = conn.cursor()
        
        # 从文件名解析表名
        if is_mysql:
            # MySQL中表名都是小写
            if filename == "EQP_STATUS.xlsx":
                table_name = "eqp_status"
            else:
                table_name = os.path.splitext(filename)[0].lower()
        else:
            # SQLite中表名保持原来的大小写
            if filename == "EQP_STATUS.xlsx":
                table_name = "eqp_status"
            else:
                table_name = os.path.splitext(filename)[0]
        
        # 检查表是否存在
        if is_mysql:
            cursor.execute("SHOW TABLES LIKE %s", (table_name,))
            if not cursor.fetchone():
                return jsonify({
                    'success': False,
                    'error': f'表 {table_name} 不存在'
                })
        else:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                return jsonify({
                    'success': False,
                    'error': f'表 {table_name} 不存在'
                })
        
        # 删除表中的所有数据
        cursor.execute(f"DELETE FROM {table_name}")
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功删除表 {table_name} 中的所有数据'
        })
    except Exception as e:
        current_app.logger.error(f"删除文件数据时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'删除文件时出错: {str(e)}'
        })
    finally:
        if 'conn' in locals():
            conn.close()

@bp.route('/check-mysql-databases', methods=['GET'])
@login_required
def check_mysql_databases():
    """检查MySQL中的数据库列表"""
    try:
        # 使用ORM模型读取AI设置
        ai_setting = db.session.query(AISettings).filter_by(id=1).first()
        
        if not ai_setting or not ai_setting.settings:
            return jsonify({
                'success': False,
                'error': 'AI设置中没有数据库配置'
            }), 400
        
        import json
        settings = json.loads(ai_setting.settings)
        db_settings = settings.get('database', {})
        
        if db_settings.get('type') != 'mysql':
            return jsonify({
                'success': False,
                'error': '当前AI设置中的数据库类型不是MySQL'
            }), 400
        
        mysql_settings = db_settings.get('mysql', {})
        if not mysql_settings:
            return jsonify({
                'success': False,
                'error': 'AI设置中没有MySQL配置'
            }), 400
        
        # 连接MySQL服务器（不指定数据库）
        connection_config = mysql_settings.copy()
        if 'database' in connection_config:
            del connection_config['database']
        
        connection_config['cursorclass'] = pymysql.cursors.DictCursor
        
        conn = pymysql.connect(**connection_config)
        cursor = conn.cursor()
        
        # 获取所有数据库
        cursor.execute("SHOW DATABASES")
        all_databases = [row['Database'] for row in cursor.fetchall()]
        
        # 检查特定数据库的表情况
        database_info = []
        target_databases = ['aps', 'aps_system']
        
        for db_name in target_databases:
            if db_name in all_databases:
                try:
                    cursor.execute(f"USE {db_name}")
                    cursor.execute("SHOW TABLES")
                    tables = cursor.fetchall()
                    table_names = [list(table.values())[0] for table in tables]
                    
                    # 检查关键表
                    key_tables = ['eqp_status', 'wip_lot', 'et_ft_test_spec', 'et_recipe_file']
                    existing_key_tables = [t for t in key_tables if t in table_names]
                    
                    # 获取记录数
                    record_counts = {}
                    for table in existing_key_tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                            count_result = cursor.fetchone()
                            record_counts[table] = count_result['count']
                        except:
                            record_counts[table] = 0
                    
                    database_info.append({
                        'name': db_name,
                        'exists': True,
                        'total_tables': len(table_names),
                        'table_names': table_names,
                        'key_tables': existing_key_tables,
                        'record_counts': record_counts,
                        'is_current': db_name == mysql_settings.get('database', 'aps')
                    })
                    
                except Exception as e:
                    database_info.append({
                        'name': db_name,
                        'exists': True,
                        'error': str(e),
                        'is_current': db_name == mysql_settings.get('database', 'aps')
                    })
            else:
                database_info.append({
                    'name': db_name,
                    'exists': False,
                    'is_current': db_name == mysql_settings.get('database', 'aps')
                })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'all_databases': all_databases,
            'database_info': database_info,
            'current_config': mysql_settings.get('database', 'aps')
        })
        
    except Exception as e:
        logger.error(f"检查MySQL数据库失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@login_required
def switch_mysql_database():
    """切换MySQL数据库"""
    try:
        data = request.get_json()
        target_database = data.get('database')
        
        if not target_database:
            return jsonify({
                'success': False,
                'error': '请指定目标数据库名称'
            }), 400
        
        # 使用ORM模型读取和更新AI设置
        ai_setting = db.session.query(AISettings).filter_by(id=1).first()
        
        if not ai_setting or not ai_setting.settings:
            return jsonify({
                'success': False,
                'error': 'AI设置中没有数据库配置'
            }), 400
        
        import json
        settings = json.loads(ai_setting.settings)
        
        # 更新MySQL数据库名称
        if 'database' not in settings:
            settings['database'] = {}
        if 'mysql' not in settings['database']:
            settings['database']['mysql'] = {}
        
        settings['database']['mysql']['database'] = target_database
        
        # 保存更新后的配置
        ai_setting.settings = json.dumps(settings, ensure_ascii=False)
        ai_setting.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"数据库配置已切换到: {target_database}")
        
        return jsonify({
            'success': True,
            'message': f'数据库配置已切换到: {target_database}'
        })
        
    except Exception as e:
        logger.error(f"切换数据库失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# 资源管理数据预览API
@bp.route('/user-filter-presets', methods=['GET', 'POST'])
@login_required
def user_filter_presets():
    """用户过滤器预设API"""
    try:
        from app.api_v2.production.missing_apis import get_user_filter_presets, save_user_filter_preset
    except ImportError as e:
        logger.warning(f'missing_apis导入失败: {e}')
        return jsonify({'error': '用户过滤器预设服务不可用'}), 503
    
    if request.method == 'GET':
        return get_user_filter_presets()
    elif request.method == 'POST':
        return save_user_filter_preset()

@bp.route('/user-filter-presets/<int:preset_id>', methods=['PUT', 'DELETE'])
@login_required
def user_filter_presets_with_id(preset_id):
    """用户过滤器预设API（带ID）"""
    try:
        from app.api_v2.production.missing_apis import update_user_filter_preset, delete_user_filter_preset
    except ImportError as e:
        logger.warning(f'missing_apis导入失败: {e}')
        return jsonify({'error': '用户过滤器预设服务不可用'}), 503
    
    if request.method == 'PUT':
        return update_user_filter_preset(preset_id)
    elif request.method == 'DELETE':
        return delete_user_filter_preset(preset_id)

@bp.route('/production/auto-schedule', methods=['POST'])
@login_required
def auto_schedule():
    """自动排产API"""
    try:
        from app.api_v2.production.missing_apis import start_auto_schedule
    except ImportError as e:
        logger.warning(f'missing_apis导入失败: {e}')
        return jsonify({'error': '用户过滤器预设服务不可用'}), 503
    return start_auto_schedule()

@bp.route('/production/manual-schedule', methods=['POST'])
@login_required
def manual_schedule():
    """手动排产API"""
    from flask import jsonify
    import time
    return jsonify({
        'success': True,
        'message': '手动排产功能正在开发中',
        'data': {
            'schedule_id': 'MANUAL_' + str(int(time.time())),
            'status': 'pending',
            'message': '手动排产请求已接收'
        }
    })

@bp.route('/production/save-schedule-history', methods=['POST'])
@login_required
def save_schedule_history():
    """保存排产历史记录"""
    try:
        from app.api_v2.production.missing_apis import save_schedule_history
    except ImportError as e:
        logger.warning(f'missing_apis导入失败: {e}')
        return jsonify({'error': '用户过滤器预设服务不可用'}), 503
    return save_schedule_history()

@bp.route('/production/schedule-history', methods=['GET'])
@login_required
def get_schedule_history():
    """获取排产历史记录"""
    try:
        from app.api_v2.production.missing_apis import get_schedule_history
    except ImportError as e:
        logger.warning(f'missing_apis导入失败: {e}')
        return jsonify({'error': '用户过滤器预设服务不可用'}), 503
    return get_schedule_history()

@bp.route('/production/export-schedule', methods=['POST'])
@login_required
def export_schedule():
    """导出排产结果"""
    try:
        from app.api_v2.production.missing_apis import export_schedule
    except ImportError as e:
        logger.warning(f'missing_apis导入失败: {e}')
        return jsonify({'error': '用户过滤器预设服务不可用'}), 503
    return export_schedule()



# 旧的资源管理API已迁移到 API v2 (/api/v2/resources/)

# 历史排产时间API
@bp.route('/api/production/history-times', methods=['GET'])
@login_required
def get_production_history_times():
    """获取历史排产时间统计"""
    try:
        # 模拟历史排产时间数据
        history_times = [
            {
                'date': '2025-06-16',
                'algorithm': 'intelligent',
                'execution_time': 1.25,
                'total_lots': 156,
                'success_rate': 98.5
            },
            {
                'date': '2025-06-15',
                'algorithm': 'deadline',
                'execution_time': 0.85,
                'total_lots': 142,
                'success_rate': 97.2
            },
            {
                'date': '2025-06-14',
                'algorithm': 'intelligent',
                'execution_time': 1.42,
                'total_lots': 168,
                'success_rate': 99.1
            },
            {
                'date': '2025-06-13',
                'algorithm': 'product',
                'execution_time': 0.95,
                'total_lots': 134,
                'success_rate': 96.8
            },
            {
                'date': '2025-06-12',
                'algorithm': 'intelligent',
                'execution_time': 1.18,
                'total_lots': 151,
                'success_rate': 98.7
            }
        ]
        
        return jsonify({
            'success': True,
            'history_times': history_times,
            'total_records': len(history_times)
        })
        
    except Exception as e:
        current_app.logger.error(f"获取历史排产时间失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取历史排产时间失败'
        }), 500

@bp.route('/orders/parse-excel', methods=['POST'])
@login_required
def parse_excel_orders():
    """解析宜欣生产订单Excel文件并分类汇总（使用增强解析器v2.0）"""
    try:
        from app.services.enhanced_excel_parser import EnhancedExcelParser
        from app.services.order_excel_parser import OrderExcelParser
        
        # 获取请求参数
        data = request.get_json() or {}
        auto_append = data.get('auto_append', True)  # 是否自动追加到汇总表
        file_paths = data.get('file_paths')  # 指定文件路径列表，为空时自动扫描
        production_keywords = data.get('production_keywords')  # 自定义量产关键词
        source_dir = data.get('source_dir', 'downloads')  # 源文件目录
        search_subdirs = data.get('search_subdirs', True)  # 是否搜索子目录
        use_enhanced_parser = data.get('use_enhanced_parser', True)  # 是否使用增强解析器
        
        # 优先使用增强解析器
        if use_enhanced_parser:
            parser = EnhancedExcelParser(downloads_dir=source_dir, search_subdirs=search_subdirs)
            logger.info("使用增强Excel解析器v2.0")
        else:
            parser = OrderExcelParser(downloads_dir=source_dir, search_subdirs=search_subdirs)
            logger.info("使用原始Excel解析器")
        
        # 如果提供了自定义分类规则，更新解析器配置
        classification_rules = data.get('classification_rules')
        if classification_rules and isinstance(classification_rules, dict):
            parser.classification_rules = classification_rules
            logger.info(f"使用自定义分类规则: {len(classification_rules)} 个规则")
        
        if auto_append:
            if hasattr(parser, 'auto_parse_and_classify'):
                # 使用原解析器的完整流程
                result = parser.auto_parse_and_classify()
            else:
                # 增强解析器只支持批量解析，需要手动追加到汇总表
                if file_paths:
                    result = parser.batch_parse_files(file_paths)
                else:
                    result = parser.batch_parse_files()
                # TODO: 后续可以添加追加到汇总表的逻辑
        else:
            # 仅执行批量解析，不追加到汇总表
            if file_paths:
                result = parser.batch_parse_files(file_paths)
            else:
                result = parser.batch_parse_files()
        
        return jsonify({
            'success': result['status'] == 'success',
            'data': result,
            'message': result.get('message', '解析完成')
        })
        
    except Exception as e:
        logger.error(f"解析订单Excel文件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '解析订单Excel文件失败'
        }), 500

@bp.route('/orders/scan-files', methods=['GET', 'POST'])
@login_required
def scan_order_files():
    """扫描指定目录下的宜欣订单Excel文件"""
    try:
        from app.services.order_excel_parser import OrderExcelParser
        
        # 支持POST请求传递参数
        if request.method == 'POST':
            data = request.get_json() or {}
            source_dir = data.get('source_dir', 'downloads')
            search_subdirs = data.get('search_subdirs', True)
        else:
            # GET请求从查询参数获取
            source_dir = request.args.get('source_dir', 'downloads')
            search_subdirs = request.args.get('search_subdirs', 'true').lower() == 'true'
        
        parser = OrderExcelParser(downloads_dir=source_dir, search_subdirs=search_subdirs)
        result = parser.scan_order_files()
        
        return jsonify({
            'success': result['status'] == 'success',
            'data': result,
            'message': result.get('message', '扫描完成')
        })
        
    except Exception as e:
        logger.error(f"扫描订单文件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '扫描订单文件失败'
        }), 500

@bp.route('/orders/preview-file', methods=['GET'])
@login_required
def preview_order_file():
    """预览宜欣订单Excel文件内容"""
    try:
        from app.services.order_excel_parser import OrderExcelParser
        
        file_path = request.args.get('file_path')
        if not file_path:
            return jsonify({
                'success': False,
                'message': '文件路径参数缺失'
            }), 400
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404
        
        parser = OrderExcelParser()
        result = parser.parse_order_file(file_path)
        
        # 限制预览数据量
        if result['status'] == 'success' and len(result['data']) > 5:
            result['data'] = result['data'][:5]  # 只显示前5条记录
            result['message'] += " (预览模式：仅显示前5条记录)"
        
        return jsonify({
            'success': result['status'] == 'success',
            'data': result,
            'message': result.get('message', '文件预览获取成功')
        })
        
    except Exception as e:
        logger.error(f"预览订单文件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '预览订单文件失败'
        }), 500

@bp.route('/orders/scan-lot-types', methods=['POST'])
@login_required
def scan_lot_types():
    """扫描所有Excel文件中的Lot Type，统计每种类型的数量"""
    try:
        from app.services.order_excel_parser import OrderExcelParser
        
        # 获取请求参数
        data = request.get_json() or {}
        source_dir = data.get('source_dir', 'downloads')
        search_subdirs = data.get('search_subdirs', True)
        
        parser = OrderExcelParser(downloads_dir=source_dir, search_subdirs=search_subdirs)
        
        # 先扫描文件
        scan_result = parser.scan_order_files()
        if scan_result['status'] != 'success':
            return jsonify({
                'success': False,
                'message': '扫描文件失败: ' + scan_result.get('message', '未知错误')
            })
        
        lot_type_stats = {}
        file_paths = [file_info['path'] for file_info in scan_result['yixin_order_files']]
        
        for file_path in file_paths:
            try:
                # 解析单个文件
                parse_result = parser.parse_order_file(file_path)
                if parse_result['status'] == 'success':
                    for record in parse_result['data']:
                        lot_type = record.get('Lot Type', '').strip()
                        if lot_type == '':
                            lot_type = '<空值>'
                        
                        if lot_type in lot_type_stats:
                            lot_type_stats[lot_type] += 1
                        else:
                            lot_type_stats[lot_type] = 1
                else:
                    logger.warning(f"解析文件失败: {file_path}, 错误: {parse_result.get('message')}")
            except Exception as e:
                logger.warning(f"处理文件时出错: {file_path}, 错误: {str(e)}")
                continue
        
        # 确保所有数据都可以JSON序列化
        serializable_stats = {}
        for k, v in lot_type_stats.items():
            serializable_stats[str(k)] = int(v) if isinstance(v, (int, float)) else str(v)
        
        return jsonify({
            'success': True,
            'data': serializable_stats,
            'message': f'扫描完成，找到 {len(serializable_stats)} 种不同的Lot Type'
        })
        
    except Exception as e:
        logger.error(f"扫描Lot Type失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '扫描Lot Type失败'
        }), 500

# 分类规则管理和进度跟踪API

@bp.route('/orders/classification-rules', methods=['GET', 'POST'])
@login_required
def classification_rules():
    """管理分类规则的持久化"""
    rules_file = 'instance/classification_rules.json'
    
    if request.method == 'GET':
        # 获取保存的分类规则
        try:
            if os.path.exists(rules_file):
                with open(rules_file, 'r', encoding='utf-8') as f:
                    rules = json.load(f)
                return jsonify({
                    'success': True,
                    'data': rules,
                    'message': f'加载了 {len(rules)} 条分类规则'
                })
            else:
                return jsonify({
                    'success': True,
                    'data': {},
                    'message': '未找到保存的分类规则'
                })
        except Exception as e:
            logger.error(f"加载分类规则失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e),
                'message': '加载分类规则失败'
            }), 500
    
    elif request.method == 'POST':
        # 保存分类规则
        try:
            data = request.get_json() or {}
            rules = data.get('rules', {})
            
            # 确保目录存在
            os.makedirs(os.path.dirname(rules_file), exist_ok=True)
            
            # 保存到文件
            with open(rules_file, 'w', encoding='utf-8') as f:
                json.dump(rules, f, ensure_ascii=False, indent=2)
            
            return jsonify({
                'success': True,
                'message': f'成功保存 {len(rules)} 条分类规则'
            })
            
        except Exception as e:
            logger.error(f"保存分类规则失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e),
                'message': '保存分类规则失败'
            }), 500

@bp.route('/orders/parse-progress/<task_id>', methods=['GET'])
@login_required
def get_parse_progress(task_id):
    """获取解析进度"""
    try:
        progress_file = f'instance/parse_progress_{task_id}.json'
        
        if os.path.exists(progress_file):
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress = json.load(f)
            return jsonify({
                'success': True,
                'data': progress
            })
        else:
            return jsonify({
                'success': False,
                'message': '未找到进度信息'
            })
            
    except Exception as e:
        logger.error(f"获取解析进度失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
