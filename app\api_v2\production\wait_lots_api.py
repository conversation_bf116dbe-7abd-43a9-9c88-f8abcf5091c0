"""
待排产批次API接口
"""
from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app import db
from app.models.production.wait_lots import WaitLot
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 创建待排产批次API蓝图
wait_lots_bp = Blueprint('wait_lots_api', __name__, url_prefix='/api/v2/production/wait-lots')

@wait_lots_bp.route('/health')
def health_check():
    """待排产批次模块健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'wait_lots_api',
        'timestamp': datetime.now().isoformat()
    })

@wait_lots_bp.route('/', methods=['GET'])
@login_required
def get_wait_lots():
    """获取待排产批次数据"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # 构建查询
        query = WaitLot.query
        
        # 应用筛选条件
        filters = request.args.get('filters')
        if filters:
            # 这里可以添加筛选逻辑
            pass
        
        # 分页查询
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # 转换数据格式
        data = [item.to_dict() for item in pagination.items]
        
        return jsonify({
            'success': True,
            'data': data,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        logger.error(f"获取待排产批次数据失败: {str(e)}")
        return jsonify({'error': '获取数据失败'}), 500

@wait_lots_bp.route('/<int:lot_id>', methods=['GET'])
@login_required
def get_wait_lot(lot_id):
    """获取单个待排产批次详情"""
    try:
        lot = WaitLot.query.get_or_404(lot_id)
        return jsonify({
            'success': True,
            'data': lot.to_dict()
        })
    except Exception as e:
        logger.error(f"获取待排产批次详情失败: {str(e)}")
        return jsonify({'error': '获取详情失败'}), 500

@wait_lots_bp.route('/', methods=['POST'])
@login_required
def create_wait_lot():
    """创建待排产批次"""
    try:
        data = request.get_json()
        
        # 创建新记录
        lot = WaitLot(
            lot_id=data.get('lot_id'),
            device=data.get('device'),
            stage=data.get('stage'),
            quantity=data.get('quantity'),
            pkg_pn=data.get('pkg_pn'),
            chip_id=data.get('chip_id'),
            priority=data.get('priority'),
            equipment_id=data.get('equipment_id'),
            status=data.get('status', 'WAITING')
        )
        
        db.session.add(lot)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '创建成功',
            'data': lot.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建待排产批次失败: {str(e)}")
        return jsonify({'error': '创建失败'}), 500

@wait_lots_bp.route('/<int:lot_id>', methods=['PUT'])
@login_required
def update_wait_lot(lot_id):
    """更新待排产批次"""
    try:
        lot = WaitLot.query.get_or_404(lot_id)
        data = request.get_json()
        
        # 更新字段
        for field in ['lot_id', 'device', 'stage', 'quantity', 'pkg_pn', 
                      'chip_id', 'priority', 'equipment_id', 'status']:
            if field in data:
                setattr(lot, field, data[field])
        
        lot.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '更新成功',
            'data': lot.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新待排产批次失败: {str(e)}")
        return jsonify({'error': '更新失败'}), 500

@wait_lots_bp.route('/<int:lot_id>', methods=['DELETE'])
@login_required
def delete_wait_lot(lot_id):
    """删除待排产批次"""
    try:
        lot = WaitLot.query.get_or_404(lot_id)
        db.session.delete(lot)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除待排产批次失败: {str(e)}")
        return jsonify({'error': '删除失败'}), 500

@wait_lots_bp.route('/columns', methods=['GET'])
@login_required
def get_wait_lot_columns():
    """获取待排产批次表列信息"""
    try:
        columns = [
            {'name': 'id', 'label': 'ID', 'type': 'integer', 'editable': False},
            {'name': 'lot_id', 'label': '批次号', 'type': 'string', 'editable': True},
            {'name': 'device', 'label': '产品名称', 'type': 'string', 'editable': True},
            {'name': 'stage', 'label': '工序', 'type': 'string', 'editable': True},
            {'name': 'quantity', 'label': '数量', 'type': 'integer', 'editable': True},
            {'name': 'pkg_pn', 'label': '封装料号', 'type': 'string', 'editable': True},
            {'name': 'chip_id', 'label': '芯片ID', 'type': 'string', 'editable': True},
            {'name': 'priority', 'label': '优先级', 'type': 'integer', 'editable': True},
            {'name': 'equipment_id', 'label': '设备ID', 'type': 'string', 'editable': True},
            {'name': 'status', 'label': '状态', 'type': 'string', 'editable': True},
            {'name': 'created_at', 'label': '创建时间', 'type': 'datetime', 'editable': False},
            {'name': 'updated_at', 'label': '更新时间', 'type': 'datetime', 'editable': False}
        ]
        
        return jsonify({
            'success': True,
            'data': columns
        })
        
    except Exception as e:
        logger.error(f"获取待排产批次列信息失败: {str(e)}")
        return jsonify({'error': '获取列信息失败'}), 500

@wait_lots_bp.route('/move-to-scheduled', methods=['POST'])
@login_required
def move_to_scheduled():
    """将待排产批次移至已排产"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])
        
        if not ids:
            return jsonify({'success': False, 'error': '没有选择要移动的批次'}), 400
        
        # 获取要移动的批次
        lots = WaitLot.query.filter(WaitLot.id.in_(ids)).all()
        if not lots:
            return jsonify({'success': False, 'error': '没有找到要移动的批次'}), 404
        
        from app.models.production.done_lots import DoneLot
        
        moved_count = 0
        for lot in lots:
            # 创建已排产记录
            done_lot = DoneLot(
                lot_id=lot.lot_id,
                device=lot.device,
                stage=lot.stage,
                quantity=lot.quantity,
                pkg_pn=lot.pkg_pn,
                chip_id=lot.chip_id,
                priority=lot.priority,
                equipment_id=lot.equipment_id,
                status='SCHEDULED',
                completion_rate=0.0,
                user=current_user.username,
                scheduled_start_time=datetime.utcnow()
            )
            
            db.session.add(done_lot)
            db.session.delete(lot)
            moved_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功移动 {moved_count} 个批次至已排产',
            'moved_count': moved_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"移动批次至已排产失败: {str(e)}")
        return jsonify({'error': '移动失败'}), 500 