{% extends "base.html" %}

{% block title %}手动排产与优先级管理
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
    .preview-area {
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    /* 添加表格列宽控制 */
    .table th {
        white-space: nowrap;
        min-width: 100px;
        position: relative;
        padding-right: 20px;
    }
    .table td {
        white-space: nowrap;
    }
    /* 选择行样式 */
    .selectable-row {
        cursor: pointer;
    }
    .selectable-row.selected {
        background-color: #fff1f0 !important; /* 浅红色背景 */
    }
    .selectable-row:hover {
        background-color: #f8f9fa;
    }
    /* 筛选输入框样式 */
    .filter-input {
        width: 100%;
        padding: 2px 5px;
        margin: 2px 0;
        font-size: 0.875rem;
        border: 1px solid #dee2e6;
        border-radius: 3px;
    }
    /* 排序图标样式 */
    .sort-icon {
        position: absolute;
        right: 5px;
        cursor: pointer;
        color: #999;
    }
    .sort-icon.active {
        color: #b72424; /* 红色主题色 */
    }
    /* 筛选行样式 */
    .filter-row th {
        padding: 4px;
        background-color: #f8f9fa;
    }
    /* 批量操作按钮样式 */
    .batch-actions {
        margin-bottom: 10px;
    }
    .batch-actions .btn {
        margin-right: 5px;
    }
    /* 键盘快捷键提示 */
    .shortcut-hint {
        font-size: 0.8rem;
        color: #6c757d;
        margin-left: 5px;
    }
    /* 修改表格字体大小和行高 */
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    /* 固定第一列（复选框列）*/
    .table-responsive {
        position: relative;
    }
    
    .table th:first-child,
    .table td:first-child {
        position: sticky;
        left: 0;
        z-index: 2;
        background-color: #fff;
        border-right: 1px solid #dee2e6;
    }
    
    /* 当表头固定时，确保第一列表头的背景色 */
    .table th:first-child {
        background-color: #f8f9fa;
        z-index: 3;
    }
    
    /* 悬停时保持背景色 */
    .table tr:hover td:first-child {
        background-color: #f8f9fa;
    }
    
    /* 选中行时的第一列样式 */
    .table tr.selected td:first-child {
        background-color: #fff1f0;
    }
    
    /* 调整单元格内边距 */
    .table td, 
    .table th {
        padding: 0.3rem 0.5rem;
        white-space: nowrap;
    }
    
    /* 调整复选框大小 */
    .form-check-input {
        width: 0.8rem;
        height: 0.8rem;
        margin-top: 0.2rem;
    }

    /* 修改主要按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }

    /* 修改分页激活状态颜色 */
    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 修改徽章颜色 */
    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    /* 修改链接颜色 */
    a {
        color: #b72424;
    }
    a:hover {
        color: #b72424;
    }

    /* 顶部说明区域 */
    .info-box {
        background-color: #f8f9fa;
        border-left: 4px solid #b72424;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 0 4px 4px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">手动批次排序与优先级管理</h5>
                        <div>
                            <button type="button" class="btn btn-primary me-2" onclick="autoSchedule()">
                                <i class="fas fa-robot me-1"></i>自动排产
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i>导出Excel
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="saveOrder()">
                                <i class="fas fa-save me-1"></i>保存排序
                            </button>
                            <button type="button" class="btn btn-primary" onclick="refreshPreviewData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新数据
                            </button>
                        </div>
                    </div>
                    
                    <!-- 添加说明区域 -->
                    <div class="info-box">
                        <h6><i class="fas fa-info-circle me-1"></i>手动排产界面</h6>
                        <p class="mb-0">此界面用于<b>手动调整批次顺序和优先级</b>。您可以通过拖拽、上下移动或直接设置优先级来排序批次。所有在此界面进行的更改都会独立保存，不会影响自动排产的结果。</p>
                    </div>
                    
                    <!-- 预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">手动排产预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="historySelect" onchange="loadHistoryData(this.value)">
                                    <option value="">当前数据</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作按钮 -->
                        <div class="batch-actions mb-3">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveSelectedUp()">
                                <i class="fas fa-arrow-up me-1"></i>上移<span class="shortcut-hint">↑</span>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveSelectedDown()">
                                <i class="fas fa-arrow-down me-1"></i>下移<span class="shortcut-hint">↓</span>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveSelectedTop()">
                                <i class="fas fa-angle-double-up me-1"></i>置顶<span class="shortcut-hint">Ctrl+↑</span>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveSelectedBottom()">
                                <i class="fas fa-angle-double-down me-1"></i>置底<span class="shortcut-hint">Ctrl+↓</span>
                            </button>
                            <span class="ms-3 text-muted">已选择: <span id="selectedCount">0</span> 项</span>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm table-hover table-striped" id="previewTable">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                            </div>
                                        </th>
                                        <th>SN<i class="fas fa-sort sort-icon" data-column="SN"></i></th>
                                                        <th>HANDLER_ID<i class="fas fa-sort sort-icon" data-column="HANDLER_ID"></i></th>
                <th>TESTER_ID<i class="fas fa-sort sort-icon" data-column="TESTER_ID"></i></th>
                <th>DEVICE<i class="fas fa-sort sort-icon" data-column="DEVICE"></i></th>
                <th>PKG_PN<i class="fas fa-sort sort-icon" data-column="PKG_PN"></i></th>
                <th>CHIP_ID<i class="fas fa-sort sort-icon" data-column="CHIP_ID"></i></th>
                <th>LOT_ID<i class="fas fa-sort sort-icon" data-column="LOT_ID"></i></th>
                                        <th>STAGE<i class="fas fa-sort sort-icon" data-column="STAGE"></i></th>
                                        <th>Priority<i class="fas fa-sort sort-icon" data-column="Priority"></i></th>
                                    </tr>
                                    <tr class="filter-row">
                                        <th></th>
                                        <th><input type="text" class="filter-input" data-column="SN" placeholder="筛选..."></th>
                                                        <th><input type="text" class="filter-input" data-column="HANDLER_ID" placeholder="筛选..."></th>
                <th><input type="text" class="filter-input" data-column="TESTER_ID" placeholder="筛选..."></th>
                <th><input type="text" class="filter-input" data-column="DEVICE" placeholder="筛选..."></th>
                <th><input type="text" class="filter-input" data-column="PKG_PN" placeholder="筛选..."></th>
                <th><input type="text" class="filter-input" data-column="CHIP_ID" placeholder="筛选..."></th>
                <th><input type="text" class="filter-input" data-column="LOT_ID" placeholder="筛选..."></th>
                                        <th><input type="text" class="filter-input" data-column="STAGE" placeholder="筛选..."></th>
                                        <th><input type="text" class="filter-input" data-column="Priority" placeholder="筛选..."></th>
                                    </tr>
                                </thead>
                                <tbody id="previewTableBody">
                                    <tr>
                                        <td colspan="14" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页导航 -->
                        <nav class="mt-3">
                            <ul class="pagination justify-content-center" id="previewPagination"></ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<script>
let tableData = []; // 存储原始数据
let filteredData = []; // 存储筛选后的数据
let currentSortColumn = ''; // 当前排序列
let currentSortDirection = ''; // 当前排序方向
let lastSelectedIndex = -1; // 用于Shift+点击多选

document.addEventListener('DOMContentLoaded', function() {
    // 页面加载时先加载历史时间列表
    loadHistoryTimeList(false);
    
    
    // 初始化筛选功能
    initFilters();

    // 初始化排序功能
    initSorting();
    
    // 显示等待提示
    document.getElementById('previewTableBody').innerHTML = 
        '<tr><td colspan="10" class="text-center">请点击"自动排产"按钮开始排产...</td></tr>';
});

// 初始化键盘快捷键
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // 只有在表格区域内才响应快捷键
        if (!document.activeElement || 
            document.activeElement.tagName === 'INPUT' || 
            document.activeElement.tagName === 'TEXTAREA' || 
            document.activeElement.tagName === 'SELECT') {
            return;
        }
        
        // 上移 (↑)
        if (e.key === 'ArrowUp' && !e.ctrlKey) {
            e.preventDefault();
            moveSelectedUp();
        }
        
        // 下移 (↓)
        if (e.key === 'ArrowDown' && !e.ctrlKey) {
            e.preventDefault();
            moveSelectedDown();
        }
        
        // 置顶 (Ctrl+↑)
        if (e.key === 'ArrowUp' && e.ctrlKey) {
            e.preventDefault();
            moveSelectedTop();
        }
        
        // 置底 (Ctrl+↓)
        if (e.key === 'ArrowDown' && e.ctrlKey) {
            e.preventDefault();
            moveSelectedBottom();
        }
    });
}

// 初始化行选择功能
function initRowSelection() {
    const rows = document.querySelectorAll('.selectable-row');
    
    rows.forEach((row, index) => {
        // 点击行选择/取消选择
        row.addEventListener('click', function(e) {
            // 如果点击的是复选框，不处理（复选框有自己的点击事件）
            if (e.target.classList.contains('form-check-input')) {
                return;
            }
            
            const checkbox = this.querySelector('.row-checkbox');
            
            // Shift+点击实现范围选择
            if (e.shiftKey && lastSelectedIndex !== -1) {
                const start = Math.min(lastSelectedIndex, index);
                const end = Math.max(lastSelectedIndex, index);
                
                for (let i = start; i <= end; i++) {
                    const rowInRange = rows[i];
                    const checkboxInRange = rowInRange.querySelector('.row-checkbox');
                    checkboxInRange.checked = true;
                    rowInRange.classList.add('selected');
                }
            } else {
                // 普通点击切换选中状态
                checkbox.checked = !checkbox.checked;
                this.classList.toggle('selected', checkbox.checked);
                
                // Ctrl+点击实现多选
                if (!e.ctrlKey && !e.shiftKey) {
                    // 如果不是Ctrl点击，取消其他所有选择
                    rows.forEach(r => {
                        if (r !== this) {
                            r.classList.remove('selected');
                            r.querySelector('.row-checkbox').checked = false;
                        }
                    });
                }
            }
            
            // 记录最后选择的索引，用于Shift+点击
            lastSelectedIndex = index;
            
            // 更新选中计数
            updateSelectedCount();
        });
    });
}

// 更新选中项计数
function updateSelectedCount() {
    const selectedCount = document.querySelectorAll('.row-checkbox:checked').length;
    document.getElementById('selectedCount').textContent = selectedCount;
    
    // 更新全选框状态
    const totalRows = document.querySelectorAll('.row-checkbox').length;
    const selectAllCheckbox = document.getElementById('selectAll');
    
    if (selectedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (selectedCount === totalRows) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const isChecked = selectAllCheckbox.checked;
    
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.checked = isChecked;
        const row = checkbox.closest('tr');
        if (row) {
            row.classList.toggle('selected', isChecked);
        }
    });
    
    updateSelectedCount();
}

// 获取选中的行
function getSelectedRows() {
    const selectedRows = [];
    document.querySelectorAll('.row-checkbox:checked').forEach(checkbox => {
        const row = checkbox.closest('tr');
        if (row) {
            const lotId = row.dataset.lotId;
            const handlerId = row.dataset.handlerId;
            selectedRows.push({
                element: row,
                lotId: lotId,
                handlerId: handlerId
            });
        }
    });
    return selectedRows;
}

// 上移选中项
function moveSelectedUp() {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) return;
    
    // 按照DOM中的顺序排序选中的行
    selectedRows.sort((a, b) => {
        return Array.from(document.querySelectorAll('.selectable-row')).indexOf(a.element) - 
               Array.from(document.querySelectorAll('.selectable-row')).indexOf(b.element);
    });
    
    // 分组处理，每个HANDLER_ID组单独处理
    const groupedRows = {};
    selectedRows.forEach(row => {
        if (!groupedRows[row.handlerId]) {
            groupedRows[row.handlerId] = [];
        }
        groupedRows[row.handlerId].push(row);
    });
    
    // 对每个组执行上移操作
    let moved = false;
    Object.keys(groupedRows).forEach(handlerId => {
        const rows = groupedRows[handlerId];
        // 获取该组的所有行
        const allGroupRows = Array.from(document.querySelectorAll(`.selectable-row[data-handler-id="${handlerId}"]`));
        
        // 从上到下处理每一行
        rows.forEach(row => {
            const index = allGroupRows.indexOf(row.element);
            if (index > 0) {
                // 如果不是第一行，交换位置
                const prevRow = allGroupRows[index - 1];
                if (!prevRow.querySelector('.row-checkbox').checked) {
                    // 只有前一行未被选中时才交换
                    prevRow.parentNode.insertBefore(row.element, prevRow);
                    allGroupRows.splice(index, 1);
                    allGroupRows.splice(index - 1, 0, row.element);
                    moved = true;
                }
            }
        });
    });
    
    if (moved) {
        // 更新行号
        updateRowNumbers();
        // 更新数据顺序
        updateDataOrder();
    }
}

// 下移选中项
function moveSelectedDown() {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) return;
    
    // 按照DOM中的顺序倒序排序选中的行（从下往上处理）
    selectedRows.sort((a, b) => {
        return Array.from(document.querySelectorAll('.selectable-row')).indexOf(b.element) - 
               Array.from(document.querySelectorAll('.selectable-row')).indexOf(a.element);
    });
    
    // 分组处理，每个HANDLER_ID组单独处理
    const groupedRows = {};
    selectedRows.forEach(row => {
        if (!groupedRows[row.handlerId]) {
            groupedRows[row.handlerId] = [];
        }
        groupedRows[row.handlerId].push(row);
    });
    
    // 对每个组执行下移操作
    let moved = false;
    Object.keys(groupedRows).forEach(handlerId => {
        const rows = groupedRows[handlerId];
        // 获取该组的所有行
        const allGroupRows = Array.from(document.querySelectorAll(`.selectable-row[data-handler-id="${handlerId}"]`));
        
        // 从下到上处理每一行
        rows.forEach(row => {
            const index = allGroupRows.indexOf(row.element);
            if (index < allGroupRows.length - 1) {
                // 如果不是最后一行，交换位置
                const nextRow = allGroupRows[index + 1];
                if (!nextRow.querySelector('.row-checkbox').checked) {
                    // 只有后一行未被选中时才交换
                    nextRow.parentNode.insertBefore(row.element, nextRow.nextSibling);
                    allGroupRows.splice(index, 1);
                    allGroupRows.splice(index + 1, 0, row.element);
                    moved = true;
                }
            }
        });
    });
    
    if (moved) {
        // 更新行号
        updateRowNumbers();
        // 更新数据顺序
        updateDataOrder();
    }
}

// 置顶选中项
function moveSelectedTop() {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) return;
    
    // 分组处理，每个HANDLER_ID组单独处理
    const groupedRows = {};
    selectedRows.forEach(row => {
        if (!groupedRows[row.handlerId]) {
            groupedRows[row.handlerId] = [];
        }
        groupedRows[row.handlerId].push(row);
    });
    
    // 对每个组执行置顶操作
    let moved = false;
    Object.keys(groupedRows).forEach(handlerId => {
        const rows = groupedRows[handlerId];
        // 获取该组的所有行
        const allGroupRows = Array.from(document.querySelectorAll(`.selectable-row[data-handler-id="${handlerId}"]`));
        const firstRow = allGroupRows[0];
        
        // 从上到下处理每一行（按DOM顺序）
        rows.sort((a, b) => {
            return Array.from(document.querySelectorAll('.selectable-row')).indexOf(a.element) - 
                   Array.from(document.querySelectorAll('.selectable-row')).indexOf(b.element);
        }).forEach(row => {
            // 将行移到组的顶部
            if (row.element !== firstRow) {
                firstRow.parentNode.insertBefore(row.element, firstRow);
                moved = true;
            }
        });
    });
    
    if (moved) {
        // 更新行号
        updateRowNumbers();
        // 更新数据顺序
        updateDataOrder();
    }
}

// 置底选中项
function moveSelectedBottom() {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) return;
    
    // 分组处理，每个HANDLER_ID组单独处理
    const groupedRows = {};
    selectedRows.forEach(row => {
        if (!groupedRows[row.handlerId]) {
            groupedRows[row.handlerId] = [];
        }
        groupedRows[row.handlerId].push(row);
    });
    
    // 对每个组执行置底操作
    let moved = false;
    Object.keys(groupedRows).forEach(handlerId => {
        const rows = groupedRows[handlerId];
        // 获取该组的所有行
        const allGroupRows = Array.from(document.querySelectorAll(`.selectable-row[data-handler-id="${handlerId}"]`));
        const lastRow = allGroupRows[allGroupRows.length - 1];
        
        // 从下到上处理每一行（按DOM倒序）
        rows.sort((a, b) => {
            return Array.from(document.querySelectorAll('.selectable-row')).indexOf(b.element) - 
                   Array.from(document.querySelectorAll('.selectable-row')).indexOf(a.element);
        }).forEach(row => {
            // 将行移到组的底部
            if (row.element !== lastRow) {
                lastRow.parentNode.appendChild(row.element);
                moved = true;
            }
        });
    });
    
    if (moved) {
        // 更新行号
        updateRowNumbers();
        // 更新数据顺序
        updateDataOrder();
    }
}

// 更新数据顺序
function updateDataOrder() {
    // 获取当前表格中的数据顺序
    const rows = document.querySelectorAll('#previewTableBody tr');
    if (!rows || rows.length === 0) {
        return;
    }
    
    // 按HANDLER_ID分组
    const groupedByHandler = {};
    Array.from(rows).forEach(row => {
        const lotId = row.dataset.lotId;
        const handlerId = row.dataset.handlerId || '';
        
        if (!lotId) {
            console.error('行缺少lot_id属性:', row.outerHTML);
            return;
        }
        
        if (!groupedByHandler[handlerId]) {
            groupedByHandler[handlerId] = [];
        }
        
        // 使用LOT_ID（大写）或lot_id（小写）进行查找
        const item = tableData.find(d => (d.LOT_ID === lotId || d.lot_id === lotId));
        if (!item) {
            console.error('找不到匹配的数据项:', lotId);
            return;
        }
        
        groupedByHandler[handlerId].push(item);
    });
    
    // 更新tableData以反映当前顺序
    tableData = [];
    Object.keys(groupedByHandler).forEach(handlerId => {
        tableData = tableData.concat(groupedByHandler[handlerId]);
    });
    
    // 更新filteredData
    filteredData = [...tableData];
    
    // 如果有排序或筛选，重新应用
    if (currentSortColumn && currentSortDirection) {
        applySorting(currentSortColumn, currentSortDirection);
    } else {
        applyFilters();
    }
}

// 初始化筛选功能
function initFilters() {
    const filterInputs = document.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
        input.addEventListener('input', function() {
            applyFilters();
        });
    });
}

// 应用筛选
function applyFilters() {
    const filterValues = {};
    document.querySelectorAll('.filter-input').forEach(input => {
        filterValues[input.dataset.column] = input.value.toLowerCase();
    });

    filteredData = tableData.filter(row => {
        return Object.keys(filterValues).every(column => {
            const filterValue = filterValues[column];
            if (!filterValue) return true;
            
            const cellValue = String(row[column] || '').toLowerCase();
            return cellValue.includes(filterValue);
        });
    });

    // 应用当前排序
    if (currentSortColumn && currentSortDirection) {
        applySorting(currentSortColumn, currentSortDirection);
    }

    // 更新显示
    renderTableData(filteredData);
}

// 初始化排序功能
function initSorting() {
    const sortIcons = document.querySelectorAll('.sort-icon');
    sortIcons.forEach(icon => {
        icon.addEventListener('click', function() {
            const column = this.dataset.column;
            const currentDirection = this.classList.contains('active') ? 
                (this.classList.contains('asc') ? 'desc' : 'asc') : 'asc';
            
            // 重置所有图标
            document.querySelectorAll('.sort-icon').forEach(i => {
                i.classList.remove('active', 'asc', 'desc');
            });
            
            // 设置当前图标状态
            this.classList.add('active', currentDirection);
            
            // 应用排序
            currentSortColumn = column;
            currentSortDirection = currentDirection;
            applySorting(column, currentDirection);
        });
    });
}

// 应用排序
function applySorting(column, direction) {
    filteredData.sort((a, b) => {
        let valueA = (a[column] || '').toString().toLowerCase();
        let valueB = (b[column] || '').toString().toLowerCase();
        
        if (column === 'sn' || column === 'order_index') {
            valueA = parseInt(valueA) || 0;
            valueB = parseInt(valueB) || 0;
        }
        
        if (valueA < valueB) return direction === 'asc' ? -1 : 1;
        if (valueA > valueB) return direction === 'asc' ? 1 : -1;
        return 0;
    });
    
    renderTableData(filteredData);
}

// 渲染表格数据
function renderTableData(data, startIndex = 0) {
    const tbody = document.getElementById('previewTableBody');
    if (!data || data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center">暂无数据</td></tr>';
        return;
    }

    // 按HANDLER_ID分组并重新编号
    const groupedByHandler = {};
    data.forEach(row => {
        const handlerId = row.HANDLER_ID || '';
        if (!groupedByHandler[handlerId]) {
            groupedByHandler[handlerId] = [];
        }
        groupedByHandler[handlerId].push(row);
    });

    // 生成表格HTML
    let html = '';
    Object.keys(groupedByHandler).forEach(handlerId => {
        const rows = groupedByHandler[handlerId];
        rows.forEach((row, index) => {
            // 确保使用正确的LOT_ID属性
            const lotId = row.LOT_ID || row.lot_id || '';
            if (!lotId) {
                console.error('渲染行时缺少LOT_ID:', row);
            }
            html += `
                <tr class="selectable-row" data-lot-id="${lotId}" data-handler-id="${handlerId}">
                    <td>
                        <div class="form-check">
                            <input class="form-check-input row-checkbox" type="checkbox" data-lot-id="${lotId}" onclick="event.stopPropagation(); updateSelectedCount();">
                        </div>
                    </td>
                    <td>${index + 1}</td>
                    <td>${row.HANDLER_ID || ''}</td>
                    <td>${row.TESTER_ID || ''}</td>
                    <td>${row.DEVICE || ''}</td>
                    <td>${row.PKG_PN || ''}</td>
                    <td>${row.CHIP_ID || ''}</td>
                    <td>${lotId}</td>
                    <td>${row.STAGE || ''}</td>
                    <td>${row.Priority || 'medium'}</td>
                </tr>
            `;
        });
    });
    
    tbody.innerHTML = html;
    
    // 修改这里：使用 tableData 的总长度，而不是当前页的 data 长度
    document.getElementById('recordCount').textContent = `${tableData.length} 条记录`;
    
    // 初始化行点击事件
    initRowSelection();
}

// 导出Excel功能
function exportToExcel() {
    // 按HANDLER_ID分组
    const groupedByHandler = {};
    tableData.forEach(row => {
        const handlerId = row.HANDLER_ID || '';
        if (!groupedByHandler[handlerId]) {
            groupedByHandler[handlerId] = [];
        }
        groupedByHandler[handlerId].push(row);
    });
    
    // 为每组分别编号并合并数据
    let exportData = [];
    Object.keys(groupedByHandler).forEach(handlerId => {
        const rows = groupedByHandler[handlerId];
        const groupData = rows.map((row, index) => ({
            'SN': index + 1,  // 每组从1开始编号
            'HANDLER_ID': row.HANDLER_ID || '',
            'TESTER_ID': row.TESTER_ID || '',
            'DEVICE': row.DEVICE || '',
            'PKG_PN': row.PKG_PN || '',
            'CHIP_ID': row.CHIP_ID || '',
            'LOT_ID': row.LOT_ID || '',
            'STAGE': row.STAGE || '',
            'Priority': row.Priority || 'medium'
        }));
        exportData = exportData.concat(groupData);
    });

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
        { wch: 5 },  // SN
        { wch: 15 }, // HANDLER_ID
        { wch: 15 }, // TESTER_ID
        { wch: 15 }, // DEVICE
        { wch: 15 }, // PKG_PN
        { wch: 15 }, // CHIP_ID
        { wch: 15 }, // LOT_ID
        { wch: 15 }, // STAGE
        { wch: 15 }  // Priority
    ];
    ws['!cols'] = colWidths;

    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    XLSX.writeFile(wb, `production_data_${new Date().toISOString().slice(0,10)}.xlsx`);
}

// 自动排产功能
function autoSchedule() {
    // 显示加载状态
    document.getElementById('previewTableBody').innerHTML = '<tr><td colspan="10" class="text-center">正在执行自动排产...</td></tr>';
    
    // 调用自动排产API
    fetch('/api/production/auto-schedule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            algorithm: 'priority',  // 排产策略
            optimization_target: 'makespan',  // 优化目标
            time_limit: 30,  // 时间限制（分钟）
            population_size: 100  // 算法参数
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('自动排产请求失败');
        }
        return response.json();
    })
    .then(result => {
        console.log('自动排产结果:', result);
        
        if (result.success && result.schedule && result.schedule.length > 0) {
            // 转换排产结果为表格数据格式 - 使用标准字段格式
            const scheduleData = result.schedule.map((item, index) => ({
                SN: item.SN || index + 1,  // 使用API返回的SN
                HANDLER_ID: item.HANDLER_ID || '',  // 直接使用API字段
                TESTER_ID: item.TESTER_ID || '',    // 直接使用API字段  
                DEVICE: item.DEVICE || '',          // 直接使用API字段
                PKG_PN: item.PKG_PN || '',         // 直接使用API字段
                CHIP_ID: item.CHIP_ID || '',       // 直接使用API字段
                LOT_ID: item.LOT_ID || '',         // 直接使用API字段
                STAGE: item.STAGE || '',           // 直接使用API字段
                Priority: item.Priority || 'medium' // 直接使用API字段
            }));
            
            // 更新表格数据
            tableData = scheduleData;
            filteredData = [...tableData];
            
            // 渲染表格
            renderTableData(scheduleData);
            
            // 更新记录数
            document.getElementById('recordCount').textContent = `${scheduleData.length} 条记录`;
            
            // 显示成功消息
            alert(`自动排产完成！生成了 ${scheduleData.length} 条排产记录。`);
            
        } else {
            throw new Error(result.message || '自动排产没有生成有效的排产记录');
        }
    })
    .catch(error => {
        console.error('自动排产失败:', error);
        
        // 提供更详细的错误处理
        let errorMessage = '自动排产失败';
        let retryButton = '';
        
        if (error.message.includes('网络') || error.message.includes('fetch')) {
            errorMessage = '网络连接失败，请检查网络连接';
            retryButton = '<br><button class="btn btn-sm btn-primary mt-2" onclick="autoSchedule()">重试</button>';
        } else if (error.message.includes('数据库')) {
            errorMessage = '数据库错误，请联系管理员或尝试重新初始化数据库';
        } else if (error.message.includes('权限')) {
            errorMessage = '权限不足，请联系管理员';
        } else {
            errorMessage = `自动排产失败: ${error.message}`;
            retryButton = '<br><button class="btn btn-sm btn-primary mt-2" onclick="autoSchedule()">重试</button>';
        }
        
        document.getElementById('previewTableBody').innerHTML = 
            `<tr><td colspan="10" class="text-center text-danger">${errorMessage}${retryButton}</td></tr>`;
        
        // 显示用户友好的错误提示
        showErrorAlert(errorMessage, error);
    });
}

// 刷新预览数据
function refreshPreviewData(page = 1) {
    // 显示加载状态
    document.getElementById('previewTableBody').innerHTML = '<tr><td colspan="10" class="text-center">加载中...</td></tr>';
    
    // 获取所有数据
    fetch('/api/production/preview-data')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(result => {
            console.log('Preview data result:', result); // 添加调试日志
            
            if (result.success) {
                // 修改这里：直接使用实际数据长度，不使用 result.total
                const actualDataLength = result.data ? result.data.length : 0;
                
                if (actualDataLength === 0) {
                    // 如果没有数据，尝试从LotPriorityDone表获取最新的历史数据
                    return fetch('/api/production/history-times')
                        .then(response => response.json())
                        .then(timesResult => {
                            console.log('History times result:', timesResult);
                            if (timesResult.success && timesResult.times && timesResult.times.length > 0) {
                                // 获取最新的时间戳
                                const latestTimestamp = timesResult.times[0];
                                // 使用最新的时间戳获取历史数据
                                return fetch(`/api/production/history-data?timestamp=${latestTimestamp}`)
                                    .then(response => response.json())
                                    .then(historyResult => {
                                        console.log('History data result:', historyResult);
                                        if (historyResult.success && historyResult.data && historyResult.data.length > 0) {
                                            return {
                                                success: true,
                                                data: historyResult.data,
                                                total: historyResult.data.length // 使用实际数据长度
                                            };
                                        }
                                        return result;
                                    });
                            }
                            return result;
                        });
                }
                
                // 使用实际数据长度
                result.total = actualDataLength;
                return result;
            } else {
                throw new Error(result.error || '未知错误');
            }
        })
        .then(result => {
            // 存储所有原始数据
            // 按HANDLER_ID分组并重新编号SN
            const groupedByHandler = {};
            result.data.forEach(item => {
                const handlerId = item.HANDLER_ID || item.handler_id || '';
                if (!groupedByHandler[handlerId]) {
                    groupedByHandler[handlerId] = [];
                }
                groupedByHandler[handlerId].push(item);
            });
            
            // 为每组分别编号并合并数据
            tableData = [];
            Object.keys(groupedByHandler).forEach(handlerId => {
                const items = groupedByHandler[handlerId];
                const groupData = items.map((item, index) => ({
                    ...item,
                    SN: index + 1,  // 每组从1开始编号 - 统一使用大写
                    HANDLER_ID: item.HANDLER_ID || '',
                    TESTER_ID: item.TESTER_ID || '',
                    DEVICE: item.DEVICE || '',
                    PKG_PN: item.PKG_PN || '',
                    CHIP_ID: item.CHIP_ID || '',
                    LOT_ID: item.LOT_ID || '',
                    STAGE: item.STAGE || '',
                    Priority: item.Priority || 'medium'
                }));
                tableData = tableData.concat(groupData);
            });
            
            filteredData = [...tableData];
            
            // 计算分页
            const per_page = 10;
            const total_pages = Math.ceil(tableData.length / per_page);
            const start = (page - 1) * per_page;
            const end = start + per_page;
            
            // 只渲染当前页的数据
            const currentPageData = filteredData.slice(start, end);
            renderTableData(currentPageData, start);
            
            // 更新分页
            updatePreviewPagination(page, total_pages);
            
            console.log('Table data processed:', {
                totalRecords: result.total || tableData.length,
                processedRecords: tableData.length,
                currentPageRecords: currentPageData.length
            });
        })
        .catch(error => {
            console.error('加载预览数据失败:', error);
            document.getElementById('previewTableBody').innerHTML = 
                '<tr><td colspan="14" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
        });
}

// 更新分页导航
function updatePreviewPagination(currentPage, totalPages) {
    const pagination = document.getElementById('previewPagination');
    let html = '';
    
    if (totalPages > 1) {
        // 上一页
        html += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="return handlePageClick(${currentPage - 1})">上一页</a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="return handlePageClick(${i})">${i}</a>
                    </li>
                `;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // 下一页
        html += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="return handlePageClick(${currentPage + 1})">下一页</a>
            </li>
        `;
    }
    
    pagination.innerHTML = html;
}

// 处理分页点击
function handlePageClick(page) {
    refreshPreviewData(page);
    return false; // 阻止默认事件
}

// 格式化优先级显示
function formatPriority(priority) {
    if (!priority) return '';
    const priorityMap = {
        'high': '<span class="badge bg-danger">高</span>',
        'medium': '<span class="badge bg-warning">中</span>',
        'low': '<span class="badge bg-success">低</span>'
    };
    return priorityMap[priority] || priority;
}

// 显示错误提示
function showErrorAlert(message, error) {
    // 使用Bootstrap的alert组件显示错误信息
    const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
    
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>错误:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    alertContainer.innerHTML = alertHtml;
    
    // 5秒后自动隐藏
    setTimeout(() => {
        const alertElement = alertContainer.querySelector('.alert');
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}

// 创建alert容器
function createAlertContainer() {
    let container = document.getElementById('alertContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'alertContainer';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        container.style.width = '400px';
        document.body.appendChild(container);
    }
    return container;
}

// 加载历史数据时间列表
function loadHistoryTimeList(autoLoadLatest = true) {
    return fetch('/api/production/history-times')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                const historySelect = document.getElementById('historySelect');
                // 清除现有选项，保留"当前数据"选项
                historySelect.innerHTML = '<option value="">当前数据</option>';
                
                // 添加历史时间选项
                result.times.forEach(time => {
                    const option = document.createElement('option');
                    option.value = time;
                    const date = new Date(time);
                    // 格式化时间为 "YYYY-MM-DD HH:mm:ss"
                    const formattedDate = date.getFullYear() + '-' +
                        String(date.getMonth() + 1).padStart(2, '0') + '-' +
                        String(date.getDate()).padStart(2, '0') + ' ' +
                        String(date.getHours()).padStart(2, '0') + ':' +
                        String(date.getMinutes()).padStart(2, '0') + ':' +
                        String(date.getSeconds()).padStart(2, '0');
                    option.textContent = formattedDate;
                    historySelect.appendChild(option);
                });

                // 如果需要自动加载最新数据且有历史数据
                if (autoLoadLatest && result.times.length > 0) {
                    historySelect.value = result.times[0];
                    loadHistoryData(result.times[0]);
                }
                return result;
            } else {
                throw new Error(result.error || '加载历史时间列表失败');
            }
        })
        .catch(error => {
            console.error('加载历史时间列表失败:', error);
            throw error;
        });
}

// 加载历史数据
function loadHistoryData(timestamp) {
    // 显示加载状态
    document.getElementById('previewTableBody').innerHTML = '<tr><td colspan="10" class="text-center">加载中...</td></tr>';

    if (!timestamp) {
        // 如果没有选择时间戳，重新加载当前数据
        refreshPreviewData();
        return;
    }

    fetch(`/api/production/history-data?timestamp=${timestamp}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 更新表格数据
                // 按HANDLER_ID分组并重新编号SN
                const groupedByHandler = {};
                result.data.forEach(item => {
                    const handlerId = item.HANDLER_ID || item.handler_id || '';
                    if (!groupedByHandler[handlerId]) {
                        groupedByHandler[handlerId] = [];
                    }
                    groupedByHandler[handlerId].push(item);
                });
                
                // 为每组分别编号并合并数据
                tableData = [];
                Object.keys(groupedByHandler).forEach(handlerId => {
                    const items = groupedByHandler[handlerId];
                    const groupData = items.map((item, index) => ({
                        ...item,
                    SN: index + 1,  // 每组从1开始编号 - 统一使用大写
                    HANDLER_ID: item.HANDLER_ID || '',
                    TESTER_ID: item.TESTER_ID || '',
                    DEVICE: item.DEVICE || '',
                    PKG_PN: item.PKG_PN || '',
                    CHIP_ID: item.CHIP_ID || '',
                    LOT_ID: item.LOT_ID || '',
                    STAGE: item.STAGE || '',
                    Priority: item.Priority || 'medium'
                    }));
                    tableData = tableData.concat(groupData);
                });
                
                filteredData = [...tableData];
                
                // 更新显示
                const per_page = 10;
                const total_pages = Math.ceil(tableData.length / per_page);
                
                // 渲染第一页数据
                renderTableData(filteredData.slice(0, per_page), 0);
                
                // 更新分页
                updatePreviewPagination(1, total_pages);
                
                // 移除这里的记录数更新，使用 renderTableData 中的更新
                // document.getElementById('recordCount').textContent = 
                //     `${result.data.length} 条历史记录 (${new Date(timestamp).toLocaleString()})`;
                
                // 初始化排序和筛选
                currentSortColumn = '';
                currentSortDirection = '';
                document.querySelectorAll('.sort-icon').forEach(icon => {
                    icon.classList.remove('active', 'asc', 'desc');
                });
                document.querySelectorAll('.filter-input').forEach(input => {
                    input.value = '';
                });
            } else {
                document.getElementById('previewTableBody').innerHTML = 
                    '<tr><td colspan="10" class="text-center text-danger">加载失败: ' + (result.error || '未知错误') + '</td></tr>';
            }
        })
        .catch(error => {
            console.error('加载历史数据失败:', error);
            document.getElementById('previewTableBody').innerHTML = 
                '<tr><td colspan="10" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
        });
}

// 更新行号
function updateRowNumbers() {
    // 按HANDLER_ID分组
    const rows = document.querySelectorAll('#previewTableBody tr');
    const groupedRows = {};
    
    // 第一遍：分组
    Array.from(rows).forEach(row => {
        const handlerId = row.cells[2].textContent.trim(); // HANDLER_ID在第3列
        if (!groupedRows[handlerId]) {
            groupedRows[handlerId] = [];
        }
        groupedRows[handlerId].push(row);
    });
    
    // 第二遍：更新每组的序号
    Object.keys(groupedRows).forEach(handlerId => {
        const groupRows = groupedRows[handlerId];
        groupRows.forEach((row, index) => {
            row.cells[1].textContent = index + 1; // SN在第2列
        });
    });
}

// 保存排序
function saveOrder() {
    // 显示加载状态
    const loadingHtml = '<tr><td colspan="10" class="text-center">正在保存数据...</td></tr>';
    const originalHtml = document.getElementById('previewTableBody').innerHTML;
    
    try {
        // 获取当前表格中的数据顺序
        const rows = document.querySelectorAll('#previewTableBody tr');
        if (!rows || rows.length === 0) {
            alert('没有数据可保存');
            return;
        }
        
        const currentOrder = Array.from(rows).map(row => {
            const lotId = row.dataset.lotId;
            if (!lotId) {
                console.error('行缺少lot_id属性:', row.outerHTML);
                return null;
            }
            
            // 使用LOT_ID（大写）或lot_id（小写）进行查找
            const item = tableData.find(d => (d.LOT_ID === lotId || d.lot_id === lotId));
            if (!item) {
                console.error('找不到匹配的数据项:', lotId);
                return null; // 返回null以便后续过滤掉
            }
            return item;
        }).filter(item => item !== null); // 过滤掉null项
        
        if (currentOrder.length === 0) {
            throw new Error('无法获取有效的排序数据');
        }
        
        // 更新tableData以反映当前顺序
        tableData = currentOrder;
        
        // 按HANDLER_ID分组
        const groupedByHandler = {};
        tableData.forEach(row => {
            const handlerId = row.HANDLER_ID || '';
            if (!groupedByHandler[handlerId]) {
                groupedByHandler[handlerId] = [];
            }
            groupedByHandler[handlerId].push(row);
        });
        
        // 为每组分别编号并合并数据
        let orderData = [];
        Object.keys(groupedByHandler).forEach(handlerId => {
            const rows = groupedByHandler[handlerId];
            const groupData = rows.map((row, index) => {
                // 确保LOT_ID存在
                const lotId = row.LOT_ID || row.lot_id || '';
                if (!lotId) {
                    console.error('缺少LOT_ID:', row);
                    throw new Error(`数据项缺少LOT_ID: ${JSON.stringify(row)}`);
                }
                
                return {
                    sn: index + 1,  // 每组从1开始编号
                    HANDLER_ID: row.HANDLER_ID || '',
                    TESTER_ID: row.TESTER_ID || '',
                    DEVICE: row.DEVICE || '',
                    PKG_PN: row.PKG_PN || '',
                    CHIP_ID: row.CHIP_ID || '',
                    LOT_ID: lotId,  // 确保使用正确的LOT_ID
                    STAGE: row.STAGE || '',
                    Priority: row.Priority || 'medium',
                    order_index: index + 1  // 每组从1开始编号
                };
            });
            orderData = orderData.concat(groupData);
        });

        console.log('保存的数据:', orderData); // 调试日志
        
        if (orderData.length === 0) {
            throw new Error('没有有效数据可保存');
        }

        // 显示保存中状态
        document.getElementById('previewTableBody').innerHTML = loadingHtml;

        // 直接保存到 LotPriorityDone 表
        fetch('/api/production/save-priority-done', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ records: orderData })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status}`);
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                alert('保存成功');
                // 先刷新历史时间列表，然后再刷新数据
                loadHistoryTimeList(false).then(() => {
                    refreshPreviewData();
                });
            } else {
                throw new Error(result.error || '保存到历史记录失败');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            
            // 提供详细的错误处理
            let errorMessage = '保存数据失败';
            let showRetry = true;
            
            if (error.message.includes('网络') || error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
                errorMessage = '网络连接失败，请检查网络连接后重试';
            } else if (error.message.includes('权限')) {
                errorMessage = '权限不足，请联系管理员';
                showRetry = false;
            } else if (error.message.includes('数据库') || error.message.includes('table')) {
                errorMessage = '数据库错误，请检查数据库配置或联系管理员';
            } else if (error.message.includes('服务器响应错误')) {
                errorMessage = '服务器错误，请稍后重试';
            } else {
                errorMessage = `保存失败: ${error.message || '未知错误'}`;
            }
            
            // 恢复原始表格
            document.getElementById('previewTableBody').innerHTML = originalHtml;
            
            // 显示友好的错误提示
            showErrorAlert(errorMessage, error);
            
            // 如果可以重试，添加重试按钮到表格中
            if (showRetry) {
                const retryButtonRow = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="alert alert-warning mb-0" role="alert">
                                <strong>保存失败:</strong> ${errorMessage}
                                <br>
                                <button class="btn btn-sm btn-primary mt-2" onclick="saveOrder()">
                                    <i class="fas fa-redo"></i> 重新保存
                                </button>
                                <button class="btn btn-sm btn-secondary mt-2 ms-2" onclick="refreshPreviewData()">
                                    <i class="fas fa-refresh"></i> 刷新数据
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                document.getElementById('previewTableBody').innerHTML = originalHtml + retryButtonRow;
            }
        });
    } catch (error) {
        console.error('准备保存数据时出错:', error);
        document.getElementById('previewTableBody').innerHTML = originalHtml;
        alert('保存失败：' + (error.message || '未知错误'));
    }
}
</script>
{% endblock %}