{% extends "base.html" %}

{% block title %}AEC-FT ICP - 自动排产
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
    /* 保持与系统一致的样式 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #9a1f1f;
        border-color: #9a1f1f;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.25);
    }

    .btn-info {
        background-color: #b72424;
        border-color: #b72424;
        color: white;
    }
    .btn-info:hover {
        background-color: #9a1f1f;
        border-color: #9a1f1f;
        color: white;
    }

    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 状态卡片样式 */
    .status-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        text-align: center;
    }

    .status-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .status-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .status-icon.primary { color: #b72424; }
    .status-icon.success { color: #28a745; }
    .status-icon.info { color: #17a2b8; }
    .status-icon.warning { color: #ffc107; }

    .status-value {
        font-size: 2rem;
        font-weight: 700;
        margin: 5px 0;
    }

    .status-label {
        color: #666;
        font-size: 0.9rem;
        margin: 0;
    }

    /* 进度条样式 */
    .progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .progress-container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        width: 400px;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .progress {
        height: 25px;
        margin: 20px 0;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar {
        background-color: #b72424 !important;
        height: 100%;
        position: relative;
    }

    .progress-text {
        margin-bottom: 15px;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .progress-percent {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        line-height: 25px;
        color: white;
        text-align: center;
        font-weight: bold;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }

    /* 表格样式 */
    .table {
        font-size: 0.9rem;
    }
    
    .table td, 
    .table th {
        padding: 0.5rem;
        vertical-align: middle;
    }
    
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
    }
    
    .table tbody tr:hover {
        background-color: #fff1f0;
    }

    /* 历史记录样式 */
    .history-item {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        background-color: #fff;
        transition: all 0.3s ease;
    }

    .history-item:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-color: #b72424;
    }

    .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .history-title {
        font-weight: 600;
        color: #333;
    }

    .history-time {
        color: #666;
        font-size: 0.9rem;
    }

    .history-stats {
        display: flex;
        gap: 20px;
        font-size: 0.9rem;
        color: #666;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .status-card {
            margin-bottom: 15px;
        }
        
        .progress-container {
            width: 90%;
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-cogs fa-2x text-primary"></i>
                        </div>
                        <div>
                            <h4 class="mb-1">APS智能排产调度平台</h4>
                            <p class="text-muted mb-0">车规芯片终测智能调度系统 - 集成APS Scheduler统一调度方案</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态监控卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon primary">
                    <i class="fas fa-list-ul"></i>
                </div>
                <div class="status-value" id="waitingLots">171</div>
                <div class="status-label">待排产批次</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="status-value" id="scheduledLots">171</div>
                <div class="status-label">已排产批次</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="status-value" id="executionTime">0.03s</div>
                <div class="status-label">执行时间</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon warning">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="status-value" id="efficiency">100%</div>
                <div class="status-label">排产效率</div>
            </div>
        </div>
    </div>

    <!-- 排产参数配置 -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-sliders-h me-2"></i>排产参数配置
                    </h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">排产策略</label>
                                <select class="form-select" id="scheduleStrategy">
                                    <option value="deadline">交期优先策略</option>
                                    <option value="priority">产品优先策略</option>
                                    <option value="value">产值优先策略</option>
                                    <option value="intelligent" selected>智能综合策略</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">优化目标</label>
                                <select class="form-select" id="optimizationTarget">
                                    <option value="makespan">最小化完工时间</option>
                                    <option value="balanced" selected>均衡优化</option>
                                    <option value="efficiency">最大化效率</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">计算时间限制</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="timeLimit" value="30" min="10" max="300">
                                    <span class="input-group-text">秒</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">算法参数</label>
                                <div class="input-group">
                                    <span class="input-group-text">种群大小</span>
                                    <input type="number" class="form-control" id="populationSize" value="100" min="50" max="500">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-end">
                                <button type="button" class="btn btn-primary btn-lg me-2" onclick="executeScheduling()">
                                    <i class="fas fa-play me-2"></i>开始排产
                                </button>
                                <button type="button" class="btn btn-info me-2" onclick="exportResults()" id="exportBtn" disabled>
                                    <i class="fas fa-download me-2"></i>导出结果
                                </button>
                                <button type="button" class="btn btn-success" onclick="saveHistory()" id="saveBtn" disabled>
                                    <i class="fas fa-save me-2"></i>保存历史
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 排产结果展示 -->
    <div class="row mb-4" id="resultsSection" style="display: none;">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>排产结果
                        </h5>
                        <div>
                            <span class="badge bg-primary" id="resultCount">0 条记录</span>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="resultsTable">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>批次号</th>
                                    <th>产品型号</th>
                                    <th>工序</th>
                                    <th>数量</th>
                                    <th>优先级</th>
                                    <th>测试机</th>
                                    <th>分选机</th>
                                    <th>UPH</th>
                                    <th>预计时长</th>
                                    <th>匹配原因</th>
                                    <th>效率分数</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 -->
                    <nav>
                        <ul class="pagination justify-content-center mt-3" id="resultsPagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录 -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>排产历史记录
                        </h5>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadHistory()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                    <div id="historyList">
                        <!-- 动态生成历史记录 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进度条模态框 -->
<div class="progress-overlay" id="progressOverlay">
    <div class="progress-container">
        <div class="progress-text" id="progressText">正在执行排产...</div>
        <div class="progress">
            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
            <div class="progress-percent" id="progressPercent">0%</div>
        </div>
        <div class="mt-3">
            <small class="text-muted" id="progressDetail">准备中...</small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentScheduleResults = [];
let currentPage = 1;
const pageSize = 20;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadHistory();
});

// 执行排产
function executeScheduling() {
    const strategy = document.getElementById('scheduleStrategy').value;
    const target = document.getElementById('optimizationTarget').value;
    const timeLimit = parseInt(document.getElementById('timeLimit').value);
    const populationSize = parseInt(document.getElementById('populationSize').value);

    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在初始化排产参数...', '准备排产数据');

    const requestData = {
        algorithm: strategy,
        optimization_target: target,
        time_limit: timeLimit,
        population_size: populationSize,
        auto_mode: false
    };

    // 模拟进度更新
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        updateProgress(progress, '正在执行排产算法...', '计算最优排产方案');
    }, 200);

    fetch('/api/production/auto-schedule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(result => {
        clearInterval(progressInterval);
        
        if (result.success) {
            updateProgress(100, '排产完成！', '正在生成结果报告');
            
            setTimeout(() => {
                showProgress(false);
                
                // 更新状态卡片
                updateStatusCards(result);
                
                // 显示排产结果
                displayResults(result.schedule_results || []);
                
                // 启用导出和保存按钮
                document.getElementById('exportBtn').disabled = false;
                document.getElementById('saveBtn').disabled = false;
                
                // 显示成功消息
                showNotification('排产完成！', `成功生成 ${result.schedule_results?.length || 0} 条排产记录`, 'success');
            }, 1000);
        } else {
            clearInterval(progressInterval);
            showProgress(false);
            showNotification('排产失败', result.error || '未知错误', 'error');
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        showProgress(false);
        console.error('排产错误:', error);
        showNotification('排产失败', '网络错误或服务器异常', 'error');
    });
}

// 更新状态卡片
function updateStatusCards(result) {
    const stats = result.statistics || {};
    document.getElementById('waitingLots').textContent = stats.total_lots || '0';
    document.getElementById('scheduledLots').textContent = stats.scheduled_lots || '0';
    document.getElementById('executionTime').textContent = (stats.execution_time || 0).toFixed(3) + 's';
    document.getElementById('efficiency').textContent = (stats.efficiency || 100).toFixed(1) + '%';
}

// 显示排产结果
function displayResults(results) {
    currentScheduleResults = results;
    currentPage = 1;
    
    if (results.length === 0) {
        document.getElementById('resultsSection').style.display = 'none';
        return;
    }
    
    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('resultCount').textContent = `${results.length} 条记录`;
    
    renderResultsTable();
    renderResultsPagination();
}

// 渲染结果表格
function renderResultsTable() {
    const tbody = document.getElementById('resultsTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, currentScheduleResults.length);
    const pageResults = currentScheduleResults.slice(startIndex, endIndex);
    
    tbody.innerHTML = pageResults.map((item, index) => `
        <tr>
            <td>${startIndex + index + 1}</td>
            <td><strong>${item.LOT_ID || 'N/A'}</strong></td>
            <td>${item.DEVICE || 'N/A'}</td>
            <td>${item.STAGE || 'N/A'}</td>
            <td>${item.GOOD_QTY || 'N/A'}</td>
            <td>
                <span class="badge ${getPriorityClass(item.Priority)}">
                    ${item.Priority || 'medium'}
                </span>
            </td>
            <td><span class="badge bg-info">${item.TESTER_ID || 'N/A'}</span></td>
            <td><span class="badge bg-secondary">${item.HANDLER_ID || 'N/A'}</span></td>
            <td><strong class="text-primary">${item.UPH || 0}</strong></td>
            <td><span class="text-success">${item.estimated_hours || 0}h</span></td>
            <td>
                <small class="text-muted" title="${item.match_reason || '默认分配'}">
                    ${(item.match_reason || '默认分配').substring(0, 20)}${(item.match_reason || '').length > 20 ? '...' : ''}
                </small>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-success" style="width: ${Math.min(item.equipment_efficiency || 0, 100)}%"></div>
                    </div>
                    <small>${Math.round(item.equipment_efficiency || 0)}</small>
                </div>
            </td>
        </tr>
    `).join('');
}

// 渲染分页
function renderResultsPagination() {
    const totalPages = Math.ceil(currentScheduleResults.length / pageSize);
    const pagination = document.getElementById('resultsPagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > Math.ceil(currentScheduleResults.length / pageSize)) return;
    currentPage = page;
    renderResultsTable();
    renderResultsPagination();
}

// 导出结果
function exportResults() {
    if (currentScheduleResults.length === 0) {
        showNotification('导出失败', '没有可导出的排产结果', 'warning');
        return;
    }
    
    showProgress(true);
    updateProgress(50, '正在生成Excel文件...', '准备导出数据');
    
    fetch('/api/production/export-schedule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            schedule_results: currentScheduleResults,
            export_format: 'excel'
        })
    })
    .then(response => {
        if (!response.ok) throw new Error('导出失败');
        return response.blob();
    })
    .then(blob => {
        updateProgress(100, '导出完成！', '正在下载文件');
        
        setTimeout(() => {
            showProgress(false);
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `排产结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showNotification('导出成功', '排产结果已保存到Excel文件', 'success');
        }, 500);
    })
    .catch(error => {
        showProgress(false);
        console.error('导出错误:', error);
        showNotification('导出失败', '无法生成Excel文件', 'error');
    });
}

// 保存历史记录
function saveHistory() {
    if (currentScheduleResults.length === 0) {
        showNotification('保存失败', '没有可保存的排产结果', 'warning');
        return;
    }
    
    const strategy = document.getElementById('scheduleStrategy').value;
    const target = document.getElementById('optimizationTarget').value;
    
    const historyData = {
        schedule_results: currentScheduleResults,
        parameters: {
            strategy: strategy,
            optimization_target: target,
            time_limit: parseInt(document.getElementById('timeLimit').value),
            population_size: parseInt(document.getElementById('populationSize').value)
        },
        statistics: {
            total_lots: currentScheduleResults.length,
            execution_time: parseFloat(document.getElementById('executionTime').textContent)
        }
    };
    
    fetch('/api/production/save-schedule-history', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(historyData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('保存成功', '排产历史记录已保存', 'success');
            loadHistory(); // 刷新历史记录
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        showNotification('保存失败', '无法保存历史记录', 'error');
    });
}

// 加载历史记录
function loadHistory() {
    fetch('/api/production/schedule-history')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayHistory(result.history || []);
        }
    })
    .catch(error => {
        console.error('加载历史记录失败:', error);
    });
}

// 显示历史记录
function displayHistory(history) {
    const historyList = document.getElementById('historyList');
    
    if (history.length === 0) {
        historyList.innerHTML = '<p class="text-muted text-center py-4">暂无历史记录</p>';
        return;
    }
    
    historyList.innerHTML = history.map(item => `
        <div class="history-item">
            <div class="history-header">
                <div class="history-title">
                    排产记录 #${item.id} - ${getStrategyName(item.parameters?.strategy)}
                </div>
                <div class="history-time">
                    ${formatDateTime(item.created_at)}
                </div>
            </div>
            <div class="history-stats">
                <span><i class="fas fa-list-ul me-1"></i>批次数: ${item.statistics?.total_lots || 0}</span>
                <span><i class="fas fa-clock me-1"></i>耗时: ${(item.statistics?.execution_time || 0).toFixed(3)}s</span>
                <span><i class="fas fa-cog me-1"></i>策略: ${getStrategyName(item.parameters?.strategy)}</span>
            </div>
            <div class="mt-2">
                <button class="btn btn-sm btn-outline-primary me-2" onclick="viewHistoryDetail(${item.id})">
                    <i class="fas fa-eye me-1"></i>查看详情
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="loadHistoryResults(${item.id})">
                    <i class="fas fa-download me-1"></i>加载结果
                </button>
            </div>
        </div>
    `).join('');
}

// 查看历史详情
function viewHistoryDetail(id) {
    fetch(`/api/production/schedule-history/${id}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const item = result.history;
            const detail = `
排产记录详情：

ID: ${item.id}
创建时间: ${formatDateTime(item.created_at)}
排产策略: ${getStrategyName(item.parameters?.strategy)}
优化目标: ${getTargetName(item.parameters?.optimization_target)}
时间限制: ${item.parameters?.time_limit || 0}秒
种群大小: ${item.parameters?.population_size || 0}

统计信息:
- 总批次数: ${item.statistics?.total_lots || 0}
- 执行时间: ${(item.statistics?.execution_time || 0).toFixed(3)}秒
- 排产效率: ${(item.statistics?.efficiency || 100).toFixed(1)}%
            `;
            alert(detail);
        }
    })
    .catch(error => {
        console.error('获取历史详情失败:', error);
        showNotification('获取失败', '无法获取历史记录详情', 'error');
    });
}

// 加载历史结果
function loadHistoryResults(id) {
    fetch(`/api/production/schedule-history/${id}`)
    .then(response => response.json())
    .then(result => {
        if (result.success && result.history.schedule_results) {
            displayResults(result.history.schedule_results);
            showNotification('加载成功', '历史排产结果已加载', 'success');
            
            // 启用导出按钮
            document.getElementById('exportBtn').disabled = false;
        } else {
            showNotification('加载失败', '历史记录中没有排产结果', 'warning');
        }
    })
    .catch(error => {
        console.error('加载历史结果失败:', error);
        showNotification('加载失败', '无法加载历史排产结果', 'error');
    });
}

// 工具函数
function showProgress(show) {
    document.getElementById('progressOverlay').style.display = show ? 'flex' : 'none';
}

function updateProgress(percent, text, detail) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressPercent = document.getElementById('progressPercent');
    const progressDetail = document.getElementById('progressDetail');
    
    progressBar.style.width = `${percent}%`;
    progressText.textContent = text;
    progressPercent.textContent = `${Math.round(percent)}%`;
    if (detail) progressDetail.textContent = detail;
}

function showNotification(title, message, type) {
    const icon = type === 'success' ? 'check-circle' : 
                 type === 'warning' ? 'exclamation-triangle' : 
                 type === 'error' ? 'times-circle' : 'info-circle';
    
    alert(`${title}\n\n${message}`);
}

function getPriorityClass(priority) {
    switch(priority?.toLowerCase()) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-secondary';
        default: return 'bg-primary';
    }
}

function formatDateTime(dateStr) {
    if (!dateStr) return 'N/A';
    try {
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN');
    } catch {
        return dateStr;
    }
}

function getStrategyName(strategy) {
    const names = {
        'deadline': '交期优先',
        'priority': '产品优先',
        'value': '产值优先',
        'intelligent': '智能综合'
    };
    return names[strategy] || strategy || '未知';
}

function getTargetName(target) {
    const names = {
        'makespan': '最小化完工时间',
        'balanced': '均衡优化',
        'efficiency': '最大化效率'
    };
    return names[target] || target || '未知';
}
</script>
{% endblock %} 