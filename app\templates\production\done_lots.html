{% extends "resources/base_resource.html" %}

{% set page_title = "已排产批次" %}
{% set table_title = "已排产批次" %}
{% set page_description = "管理已排产批次信息，包含批次号、产品名称、工序、数量、排产时间、完成率等。支持筛选、编辑、删除等操作。" %}
{% set table_name = "lotprioritydone" %}
{% set api_endpoint = "/api/v2/resources" %}

{% block extra_css %}
{{ super() }}
<style>
/* 已排产批次专用样式 */
.status-scheduled { background-color: #d1ecf1; color: #0c5460; }
.status-running { background-color: #d4edda; color: #155724; }
.status-completed { background-color: #d1e7dd; color: #0f5132; }
.status-cancelled { background-color: #f8d7da; color: #721c24; }

.priority-high { font-weight: bold; color: #dc3545; }
.priority-medium { font-weight: bold; color: #fd7e14; }
.priority-low { color: #6c757d; }

/* 批次号列样式 */
.lot-id-column {
    font-family: 'Monaco', 'Consolas', monospace;
    font-weight: bold;
}

/* 数量列右对齐 */
.quantity-column {
    text-align: right;
}

/* 完成率进度条 */
.completion-rate {
    width: 80px;
}

.completion-rate .progress {
    height: 18px;
}

.completion-high { background-color: #28a745; }
.completion-medium { background-color: #ffc107; }
.completion-low { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">{{ page_title }}</h5>
                        <div>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-info me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                            <button type="button" class="btn btn-secondary me-2" onclick="moveToWaiting()">
                                <i class="fas fa-arrow-left me-1"></i>移至待排产
                            </button>
                        </div>
                    </div>
                    
                    <!-- 说明区域 -->
                    <div class="info-box">
                        <h6><i class="fas fa-info-circle me-1"></i>{{ page_title }}</h6>
                        <p class="mb-0">{{ page_description }}</p>
                    </div>

                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">{{ table_title }}数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="batchMoveToWaiting()">
                                    <i class="fas fa-arrow-left me-1"></i>批量移至待排产
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// 页面配置
const API_ENDPOINT = '{{ api_endpoint }}';
const TABLE_NAME = '{{ table_name }}';

// 移至待排产功能
function moveToWaiting() {
    if (confirm('确定要将选中的批次移至待排产吗？')) {
        // 获取选中的记录
        const selectedRows = document.querySelectorAll('#dataTable tbody input[type="checkbox"]:checked');
        if (selectedRows.length === 0) {
            alert('请选择要移动的批次');
            return;
        }
        
        const selectedIds = Array.from(selectedRows).map(row => 
            parseInt(row.closest('tr').dataset.id)
        );
        
        moveLotsToWaiting(selectedIds);
    }
}

function batchMoveToWaiting() {
    const selectedIds = getSelectedRowIds();
    if (selectedIds.length === 0) {
        alert('请选择要移动的批次');
        return;
    }
    
    if (confirm(`确定要将选中的 ${selectedIds.length} 个批次移至待排产吗？`)) {
        moveLotsToWaiting(selectedIds);
    }
}

function moveLotsToWaiting(ids) {
    // 显示加载状态
    showLoading();
    
    // 调用API将批次移至待排产
    fetch('/api/v2/production/done-lots/move-to-waiting', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ids: ids }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        hideLoading();
        
        if (result.success) {
            showNotification('success', `成功移动 ${result.moved_count} 个批次至待排产`);
            loadData(); // 刷新数据
            clearSelection();
        } else {
            showNotification('error', result.error || '移动失败');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('移动批次失败:', error);
        showNotification('error', '移动失败: ' + error.message);
    });
}

// 自定义数据格式化函数
function formatCellValue(value, column) {
    if (column === 'status') {
        const statusClass = value ? `status-${value.toLowerCase()}` : '';
        return `<span class="badge ${statusClass}">${value || ''}</span>`;
    }
    
    if (column === 'priority') {
        let priorityClass = 'priority-low';
        if (value >= 8) priorityClass = 'priority-high';
        else if (value >= 5) priorityClass = 'priority-medium';
        
        return `<span class="${priorityClass}">${value || ''}</span>`;
    }
    
    if (column === 'lot_id') {
        return `<span class="lot-id-column">${value || ''}</span>`;
    }
    
    if (column === 'quantity') {
        return `<span class="quantity-column">${value || ''}</span>`;
    }
    
    if (column === 'completion_rate') {
        const rate = parseFloat(value) || 0;
        let progressClass = 'completion-low';
        if (rate >= 80) progressClass = 'completion-high';
        else if (rate >= 50) progressClass = 'completion-medium';
        
        return `
            <div class="completion-rate">
                <div class="progress">
                    <div class="progress-bar ${progressClass}" style="width: ${rate}%">
                        ${rate.toFixed(1)}%
                    </div>
                </div>
            </div>
        `;
    }
    
    // 默认格式化
    return value || '';
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadColumns();
    loadData();
});
</script>
{% endblock %} 