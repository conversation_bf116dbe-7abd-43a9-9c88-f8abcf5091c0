{% extends "base.html" %}

{% block title %}优先级设定 - AEC-FT ICP{% endblock %}

{% block extra_css %}
<style>
:root {
    --theme-color: #b72424;
    --theme-hover: #d73027;
}

.btn-primary {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

.btn-primary:hover {
    background-color: var(--theme-hover);
    border-color: var(--theme-hover);
}

.card-header {
    background-color: var(--theme-color);
    color: white;
}

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card h3 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: bold;
}

.stats-card p {
    margin: 5px 0 0 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.stats-card.device { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-card.lot { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-card.high-priority { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-card.total-products { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

/* 表格类型切换 */
.table-type-selector {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.table-type-selector .form-check {
    display: inline-block;
    margin-right: 30px;
}

.table-type-selector .form-check-input:checked {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

/* Excel上传按钮 */
.excel-upload-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s;
}

.excel-upload-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
}

.info-box {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-box h6 {
    color: #1976d2;
    margin-bottom: 8px;
}

.table-responsive {
    max-height: 75vh;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.table th {
    white-space: nowrap;
    min-width: 80px;
    max-width: 200px;
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
    padding: 6px 8px;
    font-size: 0.875rem;
    font-weight: 600;
}

.table td {
    white-space: nowrap;
    padding: 4px 8px;
    font-size: 0.875rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-sm th,
.table-sm td {
    padding: 4px 6px;
}

.btn-sm {
    padding: 2px 6px;
    font-size: 0.75rem;
}

.form-control-sm, .form-select-sm {
    padding: 2px 6px;
    font-size: 0.875rem;
}

.action-column {
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}

.select-column {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    text-align: center;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.filter-row {
    display: flex;
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
}

.filter-field, .filter-operator, .filter-value {
    flex: 1;
}

.filter-actions {
    flex: 0 0 auto;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 统计卡片区域 -->
    <div class="row stats-cards mb-4">
        <div class="col-md-3">
            <div class="stats-card device">
                <h3 id="deviceConfigCount">-</h3>
                <p>产品优先级配置</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card lot">
                <h3 id="lotConfigCount">-</h3>
                <p>批次优先级配置</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card high-priority">
                <h3 id="highPriorityCount">-</h3>
                <p>高优先级项目</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card total-products">
                <h3 id="totalProductCount">-</h3>
                <p>涉及产品总数</p>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">优先级设定</h5>
                        <div>
                            <button type="button" class="btn excel-upload-btn me-2" onclick="showExcelUploadModal()">
                                <i class="fas fa-file-excel me-1"></i>Excel导入
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-info me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                        </div>
                    </div>
                    
                    <!-- 说明区域 -->
                    <div class="info-box">
                        <h6><i class="fas fa-info-circle me-1"></i>优先级设定</h6>
                        <p class="mb-0">管理产品优先级和批次优先级配置，支持Excel批量导入和实时编辑。</p>
                    </div>

                    <!-- 表格类型选择 -->
                    <div class="table-type-selector">
                        <h6 class="mb-3">选择数据类型：</h6>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="tableType" id="deviceType" value="devicepriorityconfig" checked>
                            <label class="form-check-label" for="deviceType">
                                <i class="fas fa-microchip me-1"></i>产品优先级配置
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="tableType" id="lotType" value="lotpriorityconfig">
                            <label class="form-check-label" for="lotType">
                                <i class="fas fa-boxes me-1"></i>批次优先级配置
                            </label>
                        </div>
                    </div>

                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0" id="currentTableTitle">产品优先级配置数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Excel上传模态框 -->
<div class="modal fade" id="excelUploadModal" tabindex="-1" aria-labelledby="excelUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="excelUploadModalLabel">Excel文件上传</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="excelFiles" class="form-label">选择Excel文件</label>
                    <input class="form-control" type="file" id="excelFiles" multiple accept=".xlsx,.xls">
                    <div class="form-text">
                        支持同时上传多个文件。文件名包含"device"的为产品优先级配置，包含"lot"的为批次优先级配置。
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>文件格式说明</h6>
                    <p><strong>产品优先级配置文件字段：</strong>ID, DEVICE, PRIORITY, FROM_TIME, END_TIME, REFRESH_TIME, USER</p>
                    <p><strong>批次优先级配置文件字段：</strong>ID, DEVICE, STAGE, PRIORITY, REFRESH_TIME, USER</p>
                </div>

                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="uploadStatus">准备上传...</small>
                </div>

                <div class="upload-result" id="uploadResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadExcelFiles()">开始上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 新增/编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">新增记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="formFields">
                        <!-- 表单字段将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRecord()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条记录吗？此操作不可撤销。</p>
                <div id="deleteRecordInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="batchDeleteCount">0</span> 条记录吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 重新定义所有变量，不继承父模板
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let totalRecords = 0;
let advancedFilters = [];
let filterConditionIndex = 0;
let availableFields = [];
let tableData = [];

// 优先级设定专用变量
let currentTableType = 'devicepriorityconfig';
let TABLE_NAME = currentTableType; // 动态表名

// 选择相关变量
let selectedRows = new Set();
window.currentEditingIndex = -1;
window.currentDeletingIndex = -1;
window.currentTableData = [];
window.currentTableColumns = [];
window.inlineEditingRows = new Set();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('优先级设定页面加载完成，开始初始化...');
    
    // 监听表格类型切换
    document.querySelectorAll('input[name="tableType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                switchTableType(this.value);
            }
        });
    });
    
    // 加载统计数据和表格数据
    loadStatistics();
    loadData();
});

// 切换表格类型
function switchTableType(tableType) {
    currentTableType = tableType;
    TABLE_NAME = tableType; // 更新全局表名
    
    console.log(`切换到表格类型: ${tableType}`);
    
    // 更新标题
    const titles = {
        'devicepriorityconfig': '产品优先级配置数据预览',
        'lotpriorityconfig': '批次优先级配置数据预览'
    };
    document.getElementById('currentTableTitle').textContent = titles[tableType];
    
    // 重新加载数据
    clearFilter();
    loadData(1);
}

// 加载统计数据
function loadStatistics() {
    console.log('加载统计数据...');
    
    // 并发获取统计数据
    Promise.all([
        fetch('/api/v2/resources/data/devicepriorityconfig?page=1&per_page=1').then(r => r.json()),
        fetch('/api/v2/resources/data/lotpriorityconfig?page=1&per_page=1').then(r => r.json()),
    ]).then(results => {
        const [deviceResult, lotResult] = results;
        
        // 更新统计卡片
        document.getElementById('deviceConfigCount').textContent = deviceResult.pagination?.total || 0;
        document.getElementById('lotConfigCount').textContent = lotResult.pagination?.total || 0;
        document.getElementById('highPriorityCount').textContent = 0;
        document.getElementById('totalProductCount').textContent = 0;
        
        console.log('✅ 统计数据加载完成');
    }).catch(error => {
        console.error('统计数据加载失败:', error);
        // 设置默认值
        document.getElementById('deviceConfigCount').textContent = '0';
        document.getElementById('lotConfigCount').textContent = '0';
        document.getElementById('highPriorityCount').textContent = '0';
        document.getElementById('totalProductCount').textContent = '0';
    });
}

// 重写loadData函数以支持动态表名
function loadData(page = 1) {
    currentPage = page;
    showLoading(true);
    
    console.log(`正在加载 ${TABLE_NAME} 数据，第 ${page} 页...`);
    
    // 构建参数
    const params = {
        page: page,
        per_page: pageSize
    };
    
    // 添加筛选条件
    if (advancedFilters.length > 0) {
        params.advanced_filters = JSON.stringify(advancedFilters);
    }
    
    // 调用API v2
    const url = `/api/v2/resources/data/${TABLE_NAME}?` + new URLSearchParams(params);
    
    fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => {
        console.log(`API响应状态: ${response.status}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log(`API返回数据:`, data);
        
        if (!data.success) {
            throw new Error(data.error || '数据加载失败');
        }
        
        // 处理数据
        const columns = data.columns || [];
        const rows = data.data || [];
        totalRecords = data.pagination?.total || 0;
        totalPages = data.pagination?.pages || 1;
        
        // 更新界面
        renderTable(columns, rows);
        renderPagination();
        updateStats();
        updateFieldOptions(columns);
        
        // 刷新统计数据
        loadStatistics();
        
        console.log(`✅ 成功加载 ${rows.length} 条数据，共 ${totalRecords} 条记录`);
    })
    .catch(error => {
        console.error('加载数据失败:', error);
        showError('加载数据失败: ' + error.message);
        
        // 显示错误状态
        document.getElementById('tableBody').innerHTML = 
            `<tr><td colspan="20" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载失败: ${error.message}
            </td></tr>`;
    })
    .finally(() => {
        showLoading(false);
    });
}

// Excel上传相关函数
function showExcelUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('excelUploadModal'));
    modal.show();
    
    // 重置表单
    document.getElementById('excelFiles').value = '';
    document.getElementById('uploadProgress').style.display = 'none';
    document.getElementById('uploadResult').style.display = 'none';
}

function uploadExcelFiles() {
    const fileInput = document.getElementById('excelFiles');
    const files = fileInput.files;
    
    if (files.length === 0) {
        alert('请选择要上传的Excel文件');
        return;
    }
    
    const progressBar = document.querySelector('.progress-bar');
    const progressContainer = document.getElementById('uploadProgress');
    const statusText = document.getElementById('uploadStatus');
    const resultContainer = document.getElementById('uploadResult');
    
    // 显示进度条
    progressContainer.style.display = 'block';
    resultContainer.style.display = 'none';
    progressBar.style.width = '0%';
    statusText.textContent = '准备上传...';
    
    // 禁用上传按钮
    const uploadBtn = document.querySelector('#excelUploadModal .btn-primary');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';
    
    // 创建FormData
    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    
    // 上传文件
    fetch('/api/v2/production/priority-settings/upload', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        progressBar.style.width = '100%';
        statusText.textContent = '上传完成';
        
        // 显示结果
        resultContainer.style.display = 'block';
        if (result.success) {
            resultContainer.className = 'alert alert-success';
            resultContainer.innerHTML = `
                <h6><i class="fas fa-check-circle me-1"></i>上传成功</h6>
                <p>共处理 ${result.total_processed || 0} 条记录</p>
                ${result.details ? `<pre>${JSON.stringify(result.details, null, 2)}</pre>` : ''}
            `;
            
            // 刷新数据
            setTimeout(() => {
                loadData();
                loadStatistics();
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('excelUploadModal'));
                modal.hide();
            }, 2000);
        } else {
            resultContainer.className = 'alert alert-danger';
            resultContainer.innerHTML = `
                <h6><i class="fas fa-exclamation-triangle me-1"></i>上传失败</h6>
                <p>${result.error || '未知错误'}</p>
            `;
        }
    })
    .catch(error => {
        console.error('上传失败:', error);
        
        progressBar.style.width = '100%';
        progressBar.className = 'progress-bar bg-danger';
        statusText.textContent = '上传失败';
        
        resultContainer.style.display = 'block';
        resultContainer.className = 'alert alert-danger';
        resultContainer.innerHTML = `
            <h6><i class="fas fa-exclamation-triangle me-1"></i>上传失败</h6>
            <p>${error.message}</p>
        `;
    })
    .finally(() => {
        // 恢复上传按钮
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '开始上传';
    });
}

// 从base_resource.html复制的必要函数
function renderTable(columns, rows) {
    // 渲染表头
    const headerRow = document.getElementById('tableHeaders');
    headerRow.innerHTML = '';
    
    // 添加选择框列
    const selectTh = document.createElement('th');
    selectTh.className = 'select-column';
    const selectAllCheckbox = document.createElement('input');
    selectAllCheckbox.type = 'checkbox';
    selectAllCheckbox.id = 'selectAll';
    selectAllCheckbox.onchange = toggleSelectAll;
    selectTh.appendChild(selectAllCheckbox);
    headerRow.appendChild(selectTh);
    
    // 添加数据列
    columns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column;
        th.style.cursor = 'pointer';
        th.onclick = () => sortTable(column);
        headerRow.appendChild(th);
    });
    
    // 添加操作列
    const actionTh = document.createElement('th');
    actionTh.textContent = '操作';
    actionTh.className = 'action-column';
    headerRow.appendChild(actionTh);
    
    // 渲染数据行
    const tableBody = document.getElementById('tableBody');
    tableBody.innerHTML = '';
    
    if (rows.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="${columns.length + 2}" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>暂无数据
                </td>
            </tr>
        `;
        return;
    }
    
    rows.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.className = 'selectable-row';
        tr.setAttribute('data-row-index', index);
        
        // 添加选择框
        const selectTd = document.createElement('td');
        selectTd.className = 'select-column';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'row-checkbox';
        checkbox.value = index;
        checkbox.onchange = updateSelection;
        selectTd.appendChild(checkbox);
        tr.appendChild(selectTd);
        
        // 添加数据列
        columns.forEach(column => {
            const td = document.createElement('td');
            const value = row[column];
            
            // 处理不同数据类型
            if (value === null || value === undefined) {
                td.textContent = '';
            } else if (column.includes('DATE') || column.includes('TIME')) {
                td.textContent = formatDateTime(value);
            } else {
                td.textContent = String(value);
            }
            
            td.title = String(value || ''); // 添加tooltip
            tr.appendChild(td);
        });
        
        // 添加操作按钮
        const actionTd = document.createElement('td');
        actionTd.className = 'action-column';
        actionTd.innerHTML = `
            <button class="btn btn-sm btn-outline-success me-1" onclick="toggleInlineEdit(${index})" title="行内编辑" id="inlineEdit_${index}">
                <i class="fas fa-pen"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary me-1" onclick="editRecord(${index})" title="弹窗编辑">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${index})" title="删除">
                <i class="fas fa-trash"></i>
            </button>
        `;
        tr.appendChild(actionTd);
        
        tableBody.appendChild(tr);
    });
    
    // 存储当前数据供操作使用
    window.currentTableData = rows;
    window.currentTableColumns = columns;
}

function renderPagination() {
    const pagination = document.getElementById('pagination');
    let html = '';
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    // 上一页
    html += `
        <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadData(${currentPage - 1}); return false;">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadData(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadData(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadData(${totalPages}); return false;">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadData(${currentPage + 1}); return false;">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

function updateStats() {
    document.getElementById('recordCount').textContent = `${totalRecords} 条记录`;
}

function updateFieldOptions(columns) {
    availableFields = columns;
    
    document.querySelectorAll('.filter-row select[name="field"]').forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">请选择字段</option>' +
            columns.map(col => `<option value="${col}" ${col === currentValue ? 'selected' : ''}>${col}</option>`).join('');
    });
}

// 工具函数
function refreshData() {
    loadData(currentPage);
}

function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    loadData(1);
}

function exportData() {
    const params = new URLSearchParams();
    if (advancedFilters.length > 0) {
        params.append('advanced_filters', JSON.stringify(advancedFilters));
    }
    params.append('export', 'true');
    
    const url = `/api/v2/resources/data/${TABLE_NAME}?${params}`;
    window.open(url, '_blank');
}

function sortTable(column) {
    console.log('排序功能待实现:', column);
}

function formatDateTime(dateTime) {
    if (!dateTime) return '';
    try {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return String(dateTime);
    }
}

function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

function showError(message) {
    alert(message);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 筛选功能 - 简化版本
function addFilterCondition() {
    filterConditionIndex++;
    const container = document.getElementById('filterConditions');
    const newRow = document.createElement('div');
    newRow.className = 'filter-row';
    newRow.setAttribute('data-index', filterConditionIndex);
    
    newRow.innerHTML = `
        <div class="filter-field">
            <label class="form-label form-label-sm">字段</label>
            <select class="form-select form-select-sm" name="field">
                <option value="">请选择字段</option>
                ${availableFields.map(col => `<option value="${col}">${col}</option>`).join('')}
            </select>
        </div>
        <div class="filter-operator">
            <label class="form-label form-label-sm">操作符</label>
            <select class="form-select form-select-sm" name="operator">
                <option value="contains">包含</option>
                <option value="equals">等于</option>
                <option value="starts_with">开始于</option>
                <option value="ends_with">结束于</option>
                <option value="not_equals">不等于</option>
            </select>
        </div>
        <div class="filter-value">
            <label class="form-label form-label-sm">值</label>
            <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
        </div>
        <div class="filter-actions">
            <label class="form-label form-label-sm">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(${filterConditionIndex})" title="删除条件">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newRow);
}

function removeFilterCondition(index) {
    const row = document.querySelector(`[data-index="${index}"]`);
    if (row && document.querySelectorAll('.filter-row').length > 1) {
        row.remove();
    }
}

function applyFilter() {
    advancedFilters = [];
    
    document.querySelectorAll('.filter-row').forEach(row => {
        const field = row.querySelector('select[name="field"]').value;
        const operator = row.querySelector('select[name="operator"]').value;
        const value = row.querySelector('input[name="value"]').value;
        
        if (field && operator && value) {
            advancedFilters.push({ field, operator, value });
        }
    });
    
    loadData(1);
}

function clearFilter() {
    advancedFilters = [];
    
    document.querySelectorAll('.filter-row').forEach((row, index) => {
        if (index === 0) {
            row.querySelector('select[name="field"]').value = '';
            row.querySelector('select[name="operator"]').value = 'contains';
            row.querySelector('input[name="value"]').value = '';
        } else {
            row.remove();
        }
    });
    
    loadData(1);
}

// CRUD操作 - 简化版本
function addRecord() {
    console.log('新增记录功能待实现');
}

function editRecord(index) {
    console.log('编辑记录功能待实现');
}

function deleteRecord(index) {
    console.log('删除记录功能待实现');
}

function toggleInlineEdit(index) {
    console.log('行内编辑功能待实现');
}

// 选择功能 - 简化版本
function toggleSelectAll() {
    console.log('全选功能待实现');
}

function updateSelection() {
    console.log('更新选择功能待实现');
}

function batchDelete() {
    console.log('批量删除功能待实现');
}

function clearSelection() {
    console.log('清除选择功能待实现');
}
</script>
{% endblock %}