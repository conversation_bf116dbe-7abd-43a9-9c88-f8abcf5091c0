<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API v3 高级功能测试</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .log-area {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            border-radius: 4px;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
        }
        .search-box {
            position: relative;
        }
        .search-box .form-control {
            padding-right: 40px;
        }
        .search-box .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: none;
            color: #6c757d;
        }
        .sort-indicator {
            margin-left: 5px;
            font-size: 12px;
        }
        .table-container {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-rocket text-primary me-2"></i>API v3 高级功能测试</h2>
                    <div>
                        <span class="badge bg-info">第17步</span>
                        <span class="badge bg-success">搜索·排序·分页·导出</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- 表选择 -->
                <div class="feature-card">
                    <h5><i class="fas fa-table text-primary me-2"></i>选择测试表</h5>
                    <select class="form-select" id="testTable">
                        <option value="">请选择测试表...</option>
                        <option value="eqp_status">设备状态管理</option>
                        <option value="et_uph_eqp">UPH设备管理</option>
                        <option value="et_ft_test_spec">测试规格管理</option>
                        <option value="ct">产品周期管理</option>
                    </select>
                </div>

                <!-- 搜索功能 -->
                <div class="feature-card">
                    <h5><i class="fas fa-search text-success me-2"></i>全文搜索功能</h5>
                    <div class="search-box">
                        <input type="text" class="form-control" id="globalSearch" placeholder="输入关键词进行全文搜索...">
                        <button class="search-btn" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-success btn-sm" onclick="performSearch()">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSearch()">
                            <i class="fas fa-times me-1"></i>清除
                        </button>
                    </div>
                </div>

                <!-- 排序功能 -->
                <div class="feature-card">
                    <h5><i class="fas fa-sort text-warning me-2"></i>排序功能</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">排序字段：</label>
                            <select class="form-select" id="sortField">
                                <option value="">请选择排序字段...</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">排序方向：</label>
                            <select class="form-select" id="sortOrder">
                                <option value="asc">升序 (A-Z, 1-9)</option>
                                <option value="desc">降序 (Z-A, 9-1)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-warning btn-sm" onclick="applySorting()">
                            <i class="fas fa-sort me-1"></i>应用排序
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSorting()">
                            <i class="fas fa-times me-1"></i>清除排序
                        </button>
                    </div>
                </div>

                <!-- 分页控制 -->
                <div class="feature-card">
                    <h5><i class="fas fa-list text-info me-2"></i>分页控制</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">每页显示：</label>
                            <select class="form-select" id="perPage">
                                <option value="10">10条</option>
                                <option value="25">25条</option>
                                <option value="50" selected>50条</option>
                                <option value="100">100条</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">当前页：</label>
                            <input type="number" class="form-control" id="currentPage" value="1" min="1">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">操作：</label>
                            <div>
                                <button class="btn btn-info btn-sm" onclick="loadData()">
                                    <i class="fas fa-sync me-1"></i>刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 导出功能 -->
                <div class="feature-card">
                    <h5><i class="fas fa-download text-danger me-2"></i>导出功能</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">导出格式：</label>
                            <select class="form-select" id="exportFormat">
                                <option value="excel">Excel (.xlsx)</option>
                                <option value="csv">CSV (.csv)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">导出范围：</label>
                            <div>
                                <button class="btn btn-danger btn-sm" onclick="exportData()">
                                    <i class="fas fa-download me-1"></i>导出当前数据
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            导出将包含当前的搜索和排序条件
                        </small>
                    </div>
                </div>

                <!-- 数据显示区域 -->
                <div class="feature-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="fas fa-table text-secondary me-2"></i>数据显示</h5>
                        <div id="dataStats" class="text-muted">
                            <small>等待加载数据...</small>
                        </div>
                    </div>
                    <div class="table-container">
                        <div id="dataTable">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-table fa-3x mb-3"></i>
                                <p>请选择表并加载数据</p>
                            </div>
                        </div>
                    </div>

                    <!-- 分页导航 -->
                    <div id="paginationNav" class="d-flex justify-content-between align-items-center mt-3" style="display: none;">
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="goToPage(1)" id="firstPageBtn">
                                <i class="fas fa-angle-double-left"></i> 首页
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="goToPage(currentSettings.page - 1)" id="prevPageBtn">
                                <i class="fas fa-angle-left"></i> 上一页
                            </button>
                        </div>

                        <div class="d-flex align-items-center">
                            <span class="me-2">第</span>
                            <input type="number" class="form-control form-control-sm" style="width: 80px;"
                                   id="pageInput" min="1" onchange="goToPageInput()">
                            <span class="mx-2">页，共 <span id="totalPages">0</span> 页</span>
                        </div>

                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="goToPage(currentSettings.page + 1)" id="nextPageBtn">
                                下一页 <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="goToPage(totalPagesCount)" id="lastPageBtn">
                                末页 <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 状态面板 -->
                <div class="feature-card">
                    <h6><i class="fas fa-chart-line me-2"></i>测试状态</h6>
                    <div id="statusPanel">
                        <div class="badge bg-secondary">等待开始</div>
                    </div>
                </div>

                <!-- 当前设置 -->
                <div class="feature-card">
                    <h6><i class="fas fa-cog me-2"></i>当前设置</h6>
                    <div id="currentSettings">
                        <small class="text-muted">
                            <div>表: <span id="currentTableName">未选择</span></div>
                            <div>搜索: <span id="currentSearch">无</span></div>
                            <div>排序: <span id="currentSort">无</span></div>
                            <div>分页: <span id="currentPaging">50条/页</span></div>
                        </small>
                    </div>
                </div>

                <!-- 缓存管理 -->
                <div class="feature-card">
                    <h6><i class="fas fa-memory me-2"></i>缓存管理</h6>
                    <div id="cacheStatus" class="mb-2">
                        <small class="text-muted">缓存状态加载中...</small>
                    </div>
                    <div>
                        <button class="btn btn-outline-warning btn-sm me-2" onclick="refreshCache()">
                            <i class="fas fa-sync me-1"></i>刷新缓存
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearCache()">
                            <i class="fas fa-trash me-1"></i>清理缓存
                        </button>
                    </div>
                </div>

                <!-- 日志区域 -->
                <div class="feature-card">
                    <h6><i class="fas fa-terminal me-2"></i>操作日志</h6>
                    <div id="logArea" class="log-area"></div>
                    <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLog()">清空日志</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        const API_BASE = '/api/v3';
        let currentTable = '';
        let currentData = [];
        let totalPagesCount = 1;
        let currentSettings = {
            search: '',
            sortBy: '',
            sortOrder: 'asc',
            page: 1,
            perPage: 50
        };

        // 表选择事件
        document.getElementById('testTable').addEventListener('change', function() {
            currentTable = this.value;
            if (currentTable) {
                log(`选择测试表: ${currentTable}`);
                updateCurrentSettings();
                loadTableFields();
                loadData();
            } else {
                clearData();
            }
        });

        // 每页数量变化事件
        document.getElementById('perPage').addEventListener('change', function() {
            currentSettings.perPage = parseInt(this.value);
            currentSettings.page = 1; // 重置到第一页
            updateCurrentSettings();
            if (currentTable) {
                loadData();
                log(`📄 每页显示数量变更: ${currentSettings.perPage}条`);
            }
        });

        // 加载表字段用于排序选择
        async function loadTableFields() {
            try {
                const response = await fetch(`${API_BASE}/tables/${currentTable}/info`);
                const result = await response.json();
                
                if (result.success) {
                    const sortField = document.getElementById('sortField');
                    sortField.innerHTML = '<option value="">请选择排序字段...</option>';
                    
                    result.table_info.fields.forEach(field => {
                        sortField.innerHTML += `<option value="${field}">${field}</option>`;
                    });
                    
                    log(`✅ 加载字段成功: ${result.table_info.fields.length}个字段`);
                }
            } catch (error) {
                log(`❌ 加载字段失败: ${error.message}`);
            }
        }

        // 执行搜索
        function performSearch() {
            const searchTerm = document.getElementById('globalSearch').value;
            currentSettings.search = searchTerm;
            currentSettings.page = 1; // 重置到第一页
            updateCurrentSettings();
            loadData();
            log(`🔍 执行搜索: "${searchTerm}"`);
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('globalSearch').value = '';
            currentSettings.search = '';
            updateCurrentSettings();
            loadData();
            log('🔍 清除搜索条件');
        }

        // 应用排序
        function applySorting() {
            const sortBy = document.getElementById('sortField').value;
            const sortOrder = document.getElementById('sortOrder').value;
            
            if (!sortBy) {
                alert('请选择排序字段');
                return;
            }
            
            currentSettings.sortBy = sortBy;
            currentSettings.sortOrder = sortOrder;
            updateCurrentSettings();
            loadData();
            log(`📊 应用排序: ${sortBy} ${sortOrder}`);
        }

        // 清除排序
        function clearSorting() {
            document.getElementById('sortField').value = '';
            currentSettings.sortBy = '';
            currentSettings.sortOrder = 'asc';
            updateCurrentSettings();
            loadData();
            log('📊 清除排序条件');
        }

        // 加载数据
        async function loadData() {
            if (!currentTable) {
                alert('请先选择测试表');
                return;
            }

            setStatus('loading', '正在加载数据...');
            log(`📊 开始加载数据...`);

            try {
                // 构建请求参数
                const params = new URLSearchParams({
                    page: currentSettings.page,
                    per_page: currentSettings.perPage
                });

                if (currentSettings.search) {
                    params.append('search', currentSettings.search);
                }

                if (currentSettings.sortBy) {
                    params.append('sort_by', currentSettings.sortBy);
                    params.append('sort_order', currentSettings.sortOrder);
                }

                const response = await fetch(`${API_BASE}/tables/${currentTable}/data?${params}`);
                const result = await response.json();

                if (result.success) {
                    currentData = result.data;
                    renderTable(result.columns, result.data);
                    updateDataStats(result);
                    setStatus('success', '数据加载成功');
                    log(`✅ 数据加载成功: ${result.total}条记录`);
                } else {
                    setStatus('error', '数据加载失败');
                    log(`❌ 数据加载失败: ${result.error}`);
                }

            } catch (error) {
                setStatus('error', '请求失败');
                log(`❌ 请求异常: ${error.message}`);
            }
        }

        // 渲染数据表格
        function renderTable(columns, data) {
            const container = document.getElementById('dataTable');
            
            if (!data || data.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>暂无数据</p>
                    </div>
                `;
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-sm table-striped table-hover">';
            
            // 表头
            html += '<thead class="table-light"><tr>';
            columns.forEach(col => {
                const sortIcon = currentSettings.sortBy === col ? 
                    `<span class="sort-indicator">
                        <i class="fas fa-sort-${currentSettings.sortOrder === 'asc' ? 'up' : 'down'}"></i>
                    </span>` : '';
                html += `<th style="cursor: pointer;" onclick="sortByColumn('${col}')">${col}${sortIcon}</th>`;
            });
            html += '</tr></thead>';
            
            // 数据
            html += '<tbody>';
            data.forEach(row => {
                html += '<tr>';
                columns.forEach(col => {
                    const value = row[col] || '';
                    const displayValue = String(value).length > 30 ? 
                        String(value).substring(0, 30) + '...' : value;
                    html += `<td title="${value}">${displayValue}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table></div>';
            
            container.innerHTML = html;
        }

        // 点击列头排序
        function sortByColumn(column) {
            const currentSort = currentSettings.sortBy;
            const currentOrder = currentSettings.sortOrder;
            
            if (currentSort === column) {
                // 切换排序方向
                currentSettings.sortOrder = currentOrder === 'asc' ? 'desc' : 'asc';
            } else {
                // 新字段，默认升序
                currentSettings.sortBy = column;
                currentSettings.sortOrder = 'asc';
            }
            
            // 更新UI
            document.getElementById('sortField').value = currentSettings.sortBy;
            document.getElementById('sortOrder').value = currentSettings.sortOrder;
            
            updateCurrentSettings();
            loadData();
        }

        // 更新数据统计
        function updateDataStats(result) {
            const stats = document.getElementById('dataStats');
            stats.innerHTML = `
                <small>
                    显示 ${result.data.length} 条，共 ${result.total} 条记录
                    (第 ${currentSettings.page} 页，共 ${result.pages} 页)
                </small>
            `;

            // 更新分页导航
            totalPagesCount = result.pages;
            updatePaginationNav(result);
        }

        // 更新分页导航
        function updatePaginationNav(result) {
            const paginationNav = document.getElementById('paginationNav');
            const pageInput = document.getElementById('pageInput');
            const totalPages = document.getElementById('totalPages');

            if (result.total > currentSettings.perPage) {
                paginationNav.style.display = 'flex';

                // 更新页码输入框和总页数
                pageInput.value = currentSettings.page;
                pageInput.max = result.pages;
                totalPages.textContent = result.pages;

                // 更新按钮状态
                document.getElementById('firstPageBtn').disabled = currentSettings.page <= 1;
                document.getElementById('prevPageBtn').disabled = currentSettings.page <= 1;
                document.getElementById('nextPageBtn').disabled = currentSettings.page >= result.pages;
                document.getElementById('lastPageBtn').disabled = currentSettings.page >= result.pages;
            } else {
                paginationNav.style.display = 'none';
            }
        }

        // 跳转到指定页
        function goToPage(page) {
            if (page < 1 || page > totalPagesCount) {
                return;
            }

            currentSettings.page = page;
            updateCurrentSettings();
            loadData();
            log(`📄 跳转到第 ${page} 页`);
        }

        // 页码输入框跳转
        function goToPageInput() {
            const pageInput = document.getElementById('pageInput');
            const page = parseInt(pageInput.value);

            if (page && page >= 1 && page <= totalPagesCount) {
                goToPage(page);
            } else {
                pageInput.value = currentSettings.page;
            }
        }

        // 导出数据
        function exportData() {
            if (!currentTable) {
                alert('请先选择测试表');
                return;
            }

            const format = document.getElementById('exportFormat').value;
            setStatus('loading', '正在导出数据...');
            log(`📥 开始导出数据: ${format.toUpperCase()} 格式`);

            try {
                // 构建导出参数
                const params = new URLSearchParams({
                    format: format
                });

                if (currentSettings.search) {
                    params.append('search', currentSettings.search);
                }

                if (currentSettings.sortBy) {
                    params.append('sort_by', currentSettings.sortBy);
                    params.append('sort_order', currentSettings.sortOrder);
                }

                // 创建下载链接
                const exportUrl = `${API_BASE}/tables/${currentTable}/export?${params}`;

                // 创建隐藏的下载链接
                const link = document.createElement('a');
                link.href = exportUrl;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                setStatus('success', '导出成功');
                log(`✅ 导出成功: ${currentTable}_export.${format === 'excel' ? 'xlsx' : 'csv'}`);

            } catch (error) {
                setStatus('error', '导出失败');
                log(`❌ 导出异常: ${error.message}`);
            }
        }

        // 更新当前设置显示
        function updateCurrentSettings() {
            document.getElementById('currentTableName').textContent = currentTable || '未选择';
            document.getElementById('currentSearch').textContent = currentSettings.search || '无';
            document.getElementById('currentSort').textContent = currentSettings.sortBy ? 
                `${currentSettings.sortBy} ${currentSettings.sortOrder}` : '无';
            document.getElementById('currentPaging').textContent = `${currentSettings.perPage}条/页`;
        }

        // 清除数据
        function clearData() {
            document.getElementById('dataTable').innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-table fa-3x mb-3"></i>
                    <p>请选择表并加载数据</p>
                </div>
            `;
            document.getElementById('dataStats').innerHTML = '<small>等待加载数据...</small>';
        }

        // 工具函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        function setStatus(type, message) {
            const panel = document.getElementById('statusPanel');
            const badgeClass = type === 'success' ? 'bg-success' : 
                             type === 'error' ? 'bg-danger' : 
                             type === 'loading' ? 'bg-warning' : 'bg-secondary';
            panel.innerHTML = `<div class="badge ${badgeClass}">${message}</div>`;
        }

        // 刷新缓存
        async function refreshCache() {
            log('🔄 刷新缓存状态...');
            await loadCacheStatus();
        }

        // 清理缓存
        async function clearCache() {
            if (!confirm('确定要清理所有缓存吗？这将重新加载所有数据。')) {
                return;
            }

            setStatus('loading', '正在清理缓存...');
            log('🗑️ 开始清理缓存...');

            try {
                const response = await fetch(`${API_BASE}/cache/clear`, {
                    method: 'POST',
                    credentials: 'same-origin'
                });

                const result = await response.json();

                if (result.success) {
                    setStatus('success', '缓存清理成功');
                    log('✅ 缓存清理成功');

                    // 重新加载缓存状态和数据
                    await loadCacheStatus();
                    if (currentTable) {
                        loadData();
                    }
                } else {
                    setStatus('error', '缓存清理失败');
                    log(`❌ 缓存清理失败: ${result.error}`);
                }

            } catch (error) {
                setStatus('error', '请求失败');
                log(`❌ 缓存清理异常: ${error.message}`);
            }
        }

        // 加载缓存状态
        async function loadCacheStatus() {
            try {
                const response = await fetch(`${API_BASE}/cache/status`);
                const result = await response.json();

                if (result.success) {
                    const cacheInfo = result.cache_info;
                    const cacheStatus = document.getElementById('cacheStatus');
                    cacheStatus.innerHTML = `
                        <small class="text-muted">
                            <div>字段缓存: ${cacheInfo.field_cache_size} 项</div>
                            <div>数据缓存: ${cacheInfo.data_cache_size} 项</div>
                            <div>缓存超时: ${cacheInfo.cache_timeout}秒</div>
                        </small>
                    `;
                } else {
                    document.getElementById('cacheStatus').innerHTML =
                        '<small class="text-danger">缓存状态获取失败</small>';
                }

            } catch (error) {
                document.getElementById('cacheStatus').innerHTML =
                    '<small class="text-danger">缓存状态加载异常</small>';
            }
        }

        // 初始化
        log('🚀 高级功能测试页面已加载');
        updateCurrentSettings();
        loadCacheStatus();
    </script>
</body>
</html>
