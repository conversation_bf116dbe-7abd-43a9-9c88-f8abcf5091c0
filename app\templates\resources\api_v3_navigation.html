<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API v3 导航中心 - APS平台</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .nav-card {
            border: 1px solid #ddd;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        .nav-card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        .nav-card-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .nav-card-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .nav-card-features {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        .nav-card-features li {
            padding: 5px 0;
            color: #555;
        }
        .nav-card-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        .nav-card-button {
            width: 100%;
            padding: 12px;
            font-size: 1.1rem;
            font-weight: bold;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        .nav-card-button:hover {
            text-decoration: none;
            transform: translateY(-2px);
        }
        .btn-primary-custom {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
        }
        .btn-success-custom {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
        }
        .btn-warning-custom {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border: none;
        }
        .btn-info-custom {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
        }
        .stats-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .stats-item {
            text-align: center;
            padding: 20px;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .stats-label {
            color: #666;
            font-size: 1.1rem;
        }
        .version-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .legacy-link {
            color: #6c757d;
            font-size: 0.9rem;
            text-decoration: none;
            margin-top: 10px;
            display: inline-block;
        }
        .legacy-link:hover {
            color: #495057;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-rocket me-3"></i>API v3 导航中心
                    </h1>
                    <p class="mb-0 opacity-75 fs-5">现代化的APS平台管理界面 - 基于API v3技术栈</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success fs-6">API v3</span>
                    <span class="badge bg-info fs-6 ms-2">生产就绪</span>
                    <span class="badge bg-warning fs-6 ms-2">现代化</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 统计信息 -->
        <div class="stats-section">
            <div class="row">
                <div class="col-md-3">
                    <div class="stats-item">
                        <div class="stats-number">4</div>
                        <div class="stats-label">生产级页面</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-item">
                        <div class="stats-number">100%</div>
                        <div class="stats-label">API v3 覆盖</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-item">
                        <div class="stats-number">15+</div>
                        <div class="stats-label">高级功能</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-item">
                        <div class="stats-number">2025</div>
                        <div class="stats-label">现代化设计</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航卡片 -->
        <div class="row">
            <!-- 设备状态管理 -->
            <div class="col-lg-6 col-xl-3">
                <div class="nav-card position-relative">
                    <div class="version-badge">v3</div>
                    <div class="text-center">
                        <i class="fas fa-desktop nav-card-icon text-primary"></i>
                        <h3 class="nav-card-title">设备状态管理</h3>
                        <p class="nav-card-description">现代化的设备状态监控和管理系统，实时掌握设备运行状态</p>
                        <ul class="nav-card-features">
                            <li>实时状态监控</li>
                            <li>智能筛选搜索</li>
                            <li>批量操作管理</li>
                            <li>数据导出功能</li>
                        </ul>
                        <a href="/api/v3/pages/eqp_status" class="nav-card-button btn-primary-custom" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>进入管理
                        </a>
                        <a href="/resources/hardware" class="legacy-link">
                            <i class="fas fa-history me-1"></i>查看旧版本
                        </a>
                    </div>
                </div>
            </div>

            <!-- UPH设备管理 -->
            <div class="col-lg-6 col-xl-3">
                <div class="nav-card position-relative">
                    <div class="version-badge">v3</div>
                    <div class="text-center">
                        <i class="fas fa-tachometer-alt nav-card-icon text-success"></i>
                        <h3 class="nav-card-title">UPH设备管理</h3>
                        <p class="nav-card-description">专业的UPH（每小时产出）管理系统，优化生产效率</p>
                        <ul class="nav-card-features">
                            <li>UPH性能分析</li>
                            <li>产能数据统计</li>
                            <li>工序效率对比</li>
                            <li>趋势分析报告</li>
                        </ul>
                        <a href="/api/v3/pages/et_uph_eqp" class="nav-card-button btn-success-custom" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>进入管理
                        </a>
                        <a href="/resources/uph" class="legacy-link">
                            <i class="fas fa-history me-1"></i>查看旧版本
                        </a>
                    </div>
                </div>
            </div>

            <!-- 测试规格管理 -->
            <div class="col-lg-6 col-xl-3">
                <div class="nav-card position-relative">
                    <div class="version-badge">v3</div>
                    <div class="text-center">
                        <i class="fas fa-cogs nav-card-icon text-info"></i>
                        <h3 class="nav-card-title">测试规格管理</h3>
                        <p class="nav-card-description">全面的测试规格配置和管理系统，确保测试标准化</p>
                        <ul class="nav-card-features">
                            <li>规格版本控制</li>
                            <li>审批流程管理</li>
                            <li>激活状态控制</li>
                            <li>规格分析报告</li>
                        </ul>
                        <a href="/api/v3/pages/et_ft_test_spec" class="nav-card-button btn-info-custom" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>进入管理
                        </a>
                        <a href="/resources/specs" class="legacy-link">
                            <i class="fas fa-history me-1"></i>查看旧版本
                        </a>
                    </div>
                </div>
            </div>

            <!-- 产品周期管理 -->
            <div class="col-lg-6 col-xl-3">
                <div class="nav-card position-relative">
                    <div class="version-badge">v3</div>
                    <div class="text-center">
                        <i class="fas fa-clock nav-card-icon text-warning"></i>
                        <h3 class="nav-card-title">产品周期管理</h3>
                        <p class="nav-card-description">智能的产品生产周期跟踪和分析系统，优化生产流程</p>
                        <ul class="nav-card-features">
                            <li>周期时间分析</li>
                            <li>良率统计监控</li>
                            <li>批次跟踪管理</li>
                            <li>生产效率报告</li>
                        </ul>
                        <a href="/api/v3/pages/ct" class="nav-card-button btn-warning-custom" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>进入管理
                        </a>
                        <a href="/resources/product-cycle" class="legacy-link">
                            <i class="fas fa-history me-1"></i>查看旧版本
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术特性说明 -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-star me-2"></i>API v3 技术特性</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h5><i class="fas fa-rocket text-primary me-2"></i>现代化架构</h5>
                                <ul>
                                    <li>基于RESTful API v3设计</li>
                                    <li>响应式Bootstrap 5界面</li>
                                    <li>异步JavaScript交互</li>
                                    <li>模块化组件设计</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h5><i class="fas fa-cogs text-success me-2"></i>高级功能</h5>
                                <ul>
                                    <li>全文搜索和高级筛选</li>
                                    <li>多字段排序和分页</li>
                                    <li>Excel/CSV数据导出</li>
                                    <li>实时数据刷新</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h5><i class="fas fa-shield-alt text-info me-2"></i>企业级特性</h5>
                                <ul>
                                    <li>完整的CRUD操作支持</li>
                                    <li>数据验证和错误处理</li>
                                    <li>缓存管理和性能优化</li>
                                    <li>用户权限和安全控制</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 开发者信息 -->
        <div class="row mt-4 mb-5">
            <div class="col-12">
                <div class="text-center text-muted">
                    <p class="mb-2">
                        <i class="fas fa-code me-2"></i>
                        基于 API v3 架构开发 | 
                        <i class="fas fa-calendar-alt me-2 ms-3"></i>
                        2025年6月更新 |
                        <i class="fas fa-user-cog me-2 ms-3"></i>
                        APS开发团队
                    </p>
                    <p class="mb-0">
                        <small>如有问题或建议，请联系系统管理员</small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 API v3 导航中心加载完成');
            
            // 为所有导航卡片添加点击统计
            document.querySelectorAll('.nav-card-button').forEach(button => {
                button.addEventListener('click', function(e) {
                    const pageName = this.closest('.nav-card').querySelector('.nav-card-title').textContent;
                    console.log(`📊 用户访问: ${pageName}`);
                });
            });
            
            // 添加卡片悬停效果
            document.querySelectorAll('.nav-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.borderColor = '#667eea';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.borderColor = '#ddd';
                });
            });
        });
    </script>
</body>
</html>
