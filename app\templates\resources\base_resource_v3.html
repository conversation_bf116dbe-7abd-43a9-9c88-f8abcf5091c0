{% extends "base.html" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<!-- 引入APS主题样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
<style>
.info-box {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-box h6 {
    color: #d21919;
    margin-bottom: 8px;
}

.table-responsive {
    max-height: 75vh;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.table th {
    white-space: nowrap;
    min-width: 80px;
    max-width: 200px;
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
    padding: 6px 8px;
    font-size: 0.875rem;
    font-weight: 600;
}

.table td {
    white-space: nowrap;
    padding: 4px 8px;
    font-size: 0.875rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-sm th,
.table-sm td {
    padding: 4px 6px;
}

/* 紧凑型按钮 */
.btn-sm {
    padding: 2px 6px;
    font-size: 0.75rem;
}

/* 紧凑型表单控件 */
.form-control-sm, .form-select-sm {
    padding: 2px 6px;
    font-size: 0.875rem;
}

/* 操作列固定宽度 */
.action-column {
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}

/* 选择列固定宽度 */
.select-column {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    text-align: center;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.filter-row {
    display: flex;
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
}

.filter-field, .filter-operator, .filter-value {
    flex: 1;
}

.filter-actions {
    flex: 0 0 auto;
}

/* 卡片头部样式 */
.card-header {
    background-color: var(--aps-primary);
    color: white;
}



/* 业务键字段高亮 */
.business-key-col {
    background-color: #fff3cd !important;
    font-weight: bold;
}

/* 只读字段样式 */
.readonly-col {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* 日期时间字段样式 */
.datetime-col {
    background-color: #e7f3ff !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">{{ page_title }}</h5>
                        <div>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-info me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                        </div>
                    </div>
                    
                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">{{ table_title }}数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                    <option value="500">500 条/页</option>
                                    <option value="1000">1000 条/页</option>
                                    <option value="5000">5000 条/页</option>
                                    <option value="10000">显示全部</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增/编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">新增记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="formFields">
                        <!-- 表单字段将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRecord()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条记录吗？此操作不可撤销。</p>
                <div id="deleteRecordInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="batchDeleteCount">0</span> 条记录吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let totalRecords = 0;
let advancedFilters = [];
let filterConditionIndex = 0;
let availableFields = [];
let tableData = [];
let sortColumn = '';
let sortDirection = 'asc'; // 'asc' 或 'desc'

// 表名配置 - 子页面需要设置这个变量
const TABLE_NAME = '{{ table_name }}';

// API版本检测 - 检查是否启用API v3
const USE_API_V3 = window.location.pathname.includes('/api/v3/') || '{{ use_api_v3 }}' === 'true';
const API_BASE = USE_API_V3 ? '/api/v3' : '/api/v2/resources';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(`${TABLE_NAME} 页面加载完成，开始初始化数据...`);

    if (USE_API_V3) {
        initializeV3Features();
        // 不显示API v3标记和验证字段按钮
    }
    loadData();
});

// 初始化API v3特性
async function initializeV3Features() {
    try {
        // 加载表信息
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/columns`);
        const data = await response.json();

        if (data.success && data.table_info) {
            window.tableInfo = data.table_info;
            updatePageInfo(data.table_info);
            updateFieldOptions(data.columns);
        }
    } catch (error) {
        console.warn('加载API v3表信息失败:', error);
    }
}

// 更新页面信息显示
function updatePageInfo(tableInfo) {
    const descElement = document.querySelector('.info-box p');
    if (descElement && tableInfo) {
        descElement.innerHTML = `
            <i class="fas fa-key me-1"></i>主键: ${tableInfo.primary_key || 'id'} | 
            <i class="fas fa-star me-1"></i>业务键: ${tableInfo.business_key || '未识别'} | 
            <i class="fas fa-database me-1"></i>数据源: ${tableInfo.database || 'MySQL'} |
            <i class="fas fa-layers me-1"></i>字段数: ${tableInfo.total_fields || '-'}
        `;
    }
}



// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建临时提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动移除
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 加载数据
function loadData(page = 1) {
    currentPage = page;
    showLoading(true);
    
    console.log(`正在加载 ${TABLE_NAME} 数据，第 ${page} 页...`);
    
    // 构建参数
    const params = {
        page: page,
        per_page: pageSize
    };
    
    // 添加筛选条件
    if (advancedFilters.length > 0) {
        params.advanced_filters = JSON.stringify(advancedFilters);
    }
    
    // 调用对应版本API
    const url = USE_API_V3 ?
        `${API_BASE}/tables/${TABLE_NAME}/data?` + new URLSearchParams(params) :
        `/api/v2/resources/data/${TABLE_NAME}?` + new URLSearchParams(params);
    
    fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => {
        console.log(`API响应状态: ${response.status}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log(`API返回数据:`, data);
        
        if (!data.success) {
            throw new Error(data.error || '数据加载失败');
        }
        
        // 处理数据
        const columns = data.columns || [];
        const rows = data.data || [];
        
        // 兼容不同的API响应格式
        if (data.pagination) {
            // API v2 格式
            totalRecords = data.pagination.total || 0;
            totalPages = data.pagination.pages || 1;
        } else {
            // API v3 格式 - 使用实际数据库记录数
            totalRecords = data.total || 0;
            totalPages = data.pages || 1;
        }
        
        // 更新界面
        renderTable(columns, rows);
        renderPagination();
        updateStats();
        updateFieldOptions(columns);
        
        console.log(`✅ 成功加载 ${rows.length} 条数据，共 ${totalRecords} 条记录`);
    })
    .catch(error => {
        console.error('加载数据失败:', error);
        showError('加载数据失败: ' + error.message);
        
        // 显示错误状态
        document.getElementById('tableBody').innerHTML = 
            `<tr><td colspan="20" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载失败: ${error.message}
            </td></tr>`;
    })
    .finally(() => {
        showLoading(false);
    });
}

// 渲染表格
function renderTable(columns, rows) {
    // 渲染表头
    const headerRow = document.getElementById('tableHeaders');
    headerRow.innerHTML = '';
    
    // 添加选择框列
    const selectTh = document.createElement('th');
    selectTh.className = 'select-column';
    const selectAllCheckbox = document.createElement('input');
    selectAllCheckbox.type = 'checkbox';
    selectAllCheckbox.id = 'selectAll';
    selectAllCheckbox.onchange = toggleSelectAll;
    selectTh.appendChild(selectAllCheckbox);
    headerRow.appendChild(selectTh);
    
    // 添加数据列
    columns.forEach(column => {
        const th = document.createElement('th');
        th.style.cursor = 'pointer';
        th.onclick = () => sortTable(column);
        
        // 创建排序指示器
        const sortIndicator = document.createElement('span');
        sortIndicator.className = 'sort-indicator ms-1';
        
        if (sortColumn === column) {
            sortIndicator.innerHTML = sortDirection === 'asc' ? 
                '<i class="fas fa-sort-up text-primary"></i>' : 
                '<i class="fas fa-sort-down text-primary"></i>';
        } else {
            sortIndicator.innerHTML = '<i class="fas fa-sort text-muted"></i>';
        }
        
        th.appendChild(document.createTextNode(column));
        
        // API v3字段类型标识
        if (USE_API_V3 && window.tableInfo) {
            if (window.tableInfo.business_key === column) {
                const badge = document.createElement('span');
                badge.className = 'badge bg-warning ms-1';
                badge.style.fontSize = '0.6rem';
                badge.textContent = '业务键';
                th.appendChild(badge);
            }
            if (window.tableInfo.readonly_fields && window.tableInfo.readonly_fields.includes(column)) {
                const badge = document.createElement('span');
                badge.className = 'badge bg-secondary ms-1';
                badge.style.fontSize = '0.6rem';
                badge.textContent = '只读';
                th.appendChild(badge);
            }
            if (window.tableInfo.datetime_fields && window.tableInfo.datetime_fields.includes(column)) {
                const badge = document.createElement('span');
                badge.className = 'badge bg-info ms-1';
                badge.style.fontSize = '0.6rem';
                badge.textContent = '日期';
                th.appendChild(badge);
            }
        }
        
        th.appendChild(sortIndicator);
        headerRow.appendChild(th);
    });
    
    // 添加操作列
    const actionTh = document.createElement('th');
    actionTh.textContent = '操作';
    actionTh.className = 'action-column';
    headerRow.appendChild(actionTh);
    
    // 渲染数据行
    const tableBody = document.getElementById('tableBody');
    tableBody.innerHTML = '';
    
    if (rows.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="${columns.length + 2}" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>暂无数据
                </td>
            </tr>
        `;
        return;
    }
    
    rows.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.className = 'selectable-row';
        tr.setAttribute('data-row-index', index);
        
        // 添加选择框
        const selectTd = document.createElement('td');
        selectTd.className = 'select-column';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'row-checkbox';
        checkbox.value = index;
        checkbox.onchange = updateSelection;
        selectTd.appendChild(checkbox);
        tr.appendChild(selectTd);
        
        // 添加数据列
        columns.forEach(column => {
            const td = document.createElement('td');
            const value = row[column];
            
            // API v3字段样式应用
            if (USE_API_V3 && window.tableInfo) {
                if (window.tableInfo.business_key === column) {
                    td.classList.add('business-key-col');
                }
                if (window.tableInfo.readonly_fields && window.tableInfo.readonly_fields.includes(column)) {
                    td.classList.add('readonly-col');
                }
                if (window.tableInfo.datetime_fields && window.tableInfo.datetime_fields.includes(column)) {
                    td.classList.add('datetime-col');
                }
            }
            
            // 处理不同数据类型
            if (value === null || value === undefined) {
                td.textContent = '';
            } else if (column.includes('DATE') || column.includes('TIME')) {
                td.textContent = formatDateTime(value);
            } else {
                td.textContent = String(value);
            }
            
            td.title = String(value || ''); // 添加tooltip
            tr.appendChild(td);
        });
        
        // 添加操作按钮
        const actionTd = document.createElement('td');
        actionTd.className = 'action-column';
        actionTd.innerHTML = `
            <button class="btn btn-sm btn-outline-success me-1" onclick="toggleInlineEdit(${index})" title="行内编辑" id="inlineEdit_${index}">
                <i class="fas fa-pen"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary me-1" onclick="editRecord(${index})" title="弹窗编辑">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${index})" title="删除">
                <i class="fas fa-trash"></i>
            </button>
        `;
        tr.appendChild(actionTd);
        
        tableBody.appendChild(tr);
    });
    
    // 存储当前数据供操作使用
    window.currentTableData = rows;
    window.currentTableColumns = columns;
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    let html = '';
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    // 上一页
    html += `
        <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadData(${currentPage - 1}); return false;">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadData(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadData(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadData(${totalPages}); return false;">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadData(${currentPage + 1}); return false;">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

// 更新统计信息
function updateStats() {
    // 更新记录总数显示
    document.getElementById('recordCount').textContent = `${totalRecords} 条记录`;

    // 更新分页信息显示
    const currentPageDataLength = window.currentTableData ? window.currentTableData.length : 0;
    const startRecord = totalRecords > 0 ? (currentPage - 1) * pageSize + 1 : 0;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);

    // 更新页面范围显示（如果存在相应元素）
    const pageRangeElement = document.getElementById('pageRange');
    if (pageRangeElement) {
        pageRangeElement.textContent = `显示 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录`;
    }

    // 更新统计卡片（如果存在）
    const totalRecordsElement = document.getElementById('totalRecords');
    if (totalRecordsElement) {
        totalRecordsElement.textContent = totalRecords;
    }

    const currentPageElement = document.getElementById('currentPage');
    if (currentPageElement) {
        currentPageElement.textContent = currentPage;
    }

    const totalPagesElement = document.getElementById('totalPages');
    if (totalPagesElement) {
        totalPagesElement.textContent = totalPages;
    }

    console.log(`📊 统计信息更新: 总记录数=${totalRecords}, 当前页=${currentPage}/${totalPages}, 当前页数据=${currentPageDataLength}条`);
}

// 更新字段选择器选项
function updateFieldOptions(columns) {
    availableFields = columns;
    
    document.querySelectorAll('.filter-row select[name="field"]').forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">请选择字段</option>' +
            columns.map(col => `<option value="${col}" ${col === currentValue ? 'selected' : ''}>${col}</option>`).join('');
    });
}

// 筛选相关函数
function addFilterCondition() {
    filterConditionIndex++;
    const container = document.getElementById('filterConditions');
    const newRow = document.createElement('div');
    newRow.className = 'filter-row';
    newRow.setAttribute('data-index', filterConditionIndex);
    
    newRow.innerHTML = `
        <div class="filter-field">
            <label class="form-label form-label-sm">字段</label>
            <select class="form-select form-select-sm" name="field">
                <option value="">请选择字段</option>
                ${availableFields.map(col => `<option value="${col}">${col}</option>`).join('')}
            </select>
        </div>
        <div class="filter-operator">
            <label class="form-label form-label-sm">操作符</label>
            <select class="form-select form-select-sm" name="operator">
                <option value="contains">包含</option>
                <option value="equals">等于</option>
                <option value="starts_with">开始于</option>
                <option value="ends_with">结束于</option>
                <option value="not_equals">不等于</option>
            </select>
        </div>
        <div class="filter-value">
            <label class="form-label form-label-sm">值</label>
            <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
        </div>
        <div class="filter-actions">
            <label class="form-label form-label-sm">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(${filterConditionIndex})" title="删除条件">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newRow);
}

function removeFilterCondition(index) {
    const row = document.querySelector(`[data-index="${index}"]`);
    if (row && document.querySelectorAll('.filter-row').length > 1) {
        row.remove();
    }
}

function applyFilter() {
    advancedFilters = [];
    
    document.querySelectorAll('.filter-row').forEach(row => {
        const field = row.querySelector('select[name="field"]').value;
        const operator = row.querySelector('select[name="operator"]').value;
        const value = row.querySelector('input[name="value"]').value;
        
        if (field && operator && value) {
            advancedFilters.push({ field, operator, value });
        }
    });
    
    loadData(1);
}

function clearFilter() {
    advancedFilters = [];
    
    document.querySelectorAll('.filter-row').forEach((row, index) => {
        if (index === 0) {
            row.querySelector('select[name="field"]').value = '';
            row.querySelector('select[name="operator"]').value = 'contains';
            row.querySelector('input[name="value"]').value = '';
        } else {
            row.remove();
        }
    });
    
    loadData(1);
}

// 工具函数
function refreshData() {
    loadData(currentPage);
}

function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    loadData(1);
}

function exportData() {
    const params = new URLSearchParams();
    if (advancedFilters.length > 0) {
        params.append('advanced_filters', JSON.stringify(advancedFilters));
    }
    params.append('export', 'true');
    
    const url = `/api/v2/resources/data/${TABLE_NAME}/export?${params}`;
    window.open(url, '_blank');
}

function sortTable(column) {
    console.log(`点击排序列: ${column}`);
    
    // 如果点击的是相同列，切换排序方向
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        // 如果是新列，默认升序
        sortColumn = column;
        sortDirection = 'asc';
    }
    
    // 对当前数据进行排序
    if (window.currentTableData && window.currentTableData.length > 0) {
        window.currentTableData.sort((a, b) => {
            let valueA = a[column];
            let valueB = b[column];
            
            // 处理空值
            if (valueA === null || valueA === undefined) valueA = '';
            if (valueB === null || valueB === undefined) valueB = '';
            
            // 尝试数字比较
            const numA = parseFloat(valueA);
            const numB = parseFloat(valueB);
            
            if (!isNaN(numA) && !isNaN(numB)) {
                // 数字比较
                return sortDirection === 'asc' ? numA - numB : numB - numA;
            } else {
                // 字符串比较
                const strA = String(valueA).toLowerCase();
                const strB = String(valueB).toLowerCase();
                
                if (sortDirection === 'asc') {
                    return strA.localeCompare(strB);
                } else {
                    return strB.localeCompare(strA);
                }
            }
        });
        
        // 重新渲染表格
        renderTable(window.currentTableColumns, window.currentTableData);
        
        console.log(`✅ 已按 ${column} ${sortDirection === 'asc' ? '升序' : '降序'} 排序`);
        
        // 显示排序提示
        showSuccess(`已按 ${column} ${sortDirection === 'asc' ? '升序' : '降序'} 排序`);
    }
}

function formatDateTime(dateTime) {
    if (!dateTime) return '';
    try {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return String(dateTime);
    }
}

function showLoading(show) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

// 使用统一的Toast管理器
function showError(message) {
    if (window.ToastManager) {
        window.ToastManager.error(message);
    } else {
        console.error('Toast管理器未加载:', message);
        alert('错误: ' + message);
    }
}

function showSuccess(message) {
    if (window.ToastManager) {
        window.ToastManager.success(message);
    } else {
        console.log('Toast管理器未加载:', message);
        alert('成功: ' + message);
    }
}

function showInfo(message) {
    if (window.ToastManager) {
        window.ToastManager.info(message);
    } else {
        console.info('Toast管理器未加载:', message);
        alert('信息: ' + message);
    }
}

function showWarning(message) {
    if (window.ToastManager) {
        window.ToastManager.warning(message);
    } else {
        console.warn('Toast管理器未加载:', message);
        alert('警告: ' + message);
    }
}

// CRUD 操作函数

// 选择相关函数
let selectedRows = new Set();

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        if (selectAll.checked) {
            selectedRows.add(parseInt(checkbox.value));
        } else {
            selectedRows.delete(parseInt(checkbox.value));
        }
    });
    
    updateSelection();
}

function updateSelection() {
    selectedRows.clear();
    document.querySelectorAll('.row-checkbox:checked').forEach(checkbox => {
        selectedRows.add(parseInt(checkbox.value));
    });
    
    const count = selectedRows.size;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('batchOperations').style.display = count > 0 ? 'block' : 'none';
    
    // 更新全选框状态
    const allCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    const selectAll = document.getElementById('selectAll');
    
    if (checkedCheckboxes.length === 0) {
        selectAll.indeterminate = false;
        selectAll.checked = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
        selectAll.indeterminate = false;
        selectAll.checked = true;
    } else {
        selectAll.indeterminate = true;
        selectAll.checked = false;
    }
}

function clearSelection() {
    selectedRows.clear();
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    document.getElementById('selectAll').indeterminate = false;
    document.getElementById('batchOperations').style.display = 'none';
}

// 新增记录
function addRecord() {
    if (!window.currentTableColumns) {
        showError('请先加载数据');
        return;
    }
    
    document.getElementById('editModalLabel').textContent = '新增记录';
    generateFormFields(window.currentTableColumns, null);
    
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
    
    // 重置表单
    document.getElementById('editForm').reset();
    window.currentEditingIndex = -1;
}

// 编辑记录
function editRecord(index) {
    if (!window.currentTableData || !window.currentTableData[index]) {
        showError('记录不存在');
        return;
    }
    
    const record = window.currentTableData[index];
    document.getElementById('editModalLabel').textContent = '编辑记录';
    generateFormFields(window.currentTableColumns, record);
    
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
    
    window.currentEditingIndex = index;
}

// 生成表单字段
function generateFormFields(columns, data) {
    const container = document.getElementById('formFields');
    container.innerHTML = '';
    
    columns.forEach(column => {
        const div = document.createElement('div');
        div.className = 'mb-3';
        
        const label = document.createElement('label');
        label.className = 'form-label';
        label.textContent = column;
        label.setAttribute('for', `field_${column}`);
        
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'form-control';
        input.id = `field_${column}`;
        input.name = column;
        
        if (data && data[column] !== null && data[column] !== undefined) {
            input.value = String(data[column]);
        }
        
        div.appendChild(label);
        div.appendChild(input);
        container.appendChild(div);
    });
}

// 保存记录
function saveRecord() {
    const formData = new FormData(document.getElementById('editForm'));
    const data = {};
    
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    const isEdit = window.currentEditingIndex >= 0;
    const url = `/api/v2/resources/data/${TABLE_NAME}`;
    const method = isEdit ? 'PUT' : 'POST';
    
    // 如果是编辑，需要添加ID或主键
    if (isEdit && window.currentTableData[window.currentEditingIndex]) {
        const originalRecord = window.currentTableData[window.currentEditingIndex];
        // 假设第一个字段是主键
        const primaryKey = window.currentTableColumns[0];
        data[primaryKey] = originalRecord[primaryKey];
    }
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess(isEdit ? '记录更新成功' : '记录添加成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
            modal.hide();
            loadData(currentPage); // 重新加载数据
        } else {
            showError(result.error || '操作失败');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        showError('保存失败: ' + error.message);
    });
}

// 删除记录
function deleteRecord(index) {
    if (!window.currentTableData || !window.currentTableData[index]) {
        showError('记录不存在');
        return;
    }
    
    const record = window.currentTableData[index];
    window.currentDeletingIndex = index;
    
    // 显示记录信息
    const infoDiv = document.getElementById('deleteRecordInfo');
    const primaryKey = window.currentTableColumns[0];
    infoDiv.innerHTML = `<strong>主键:</strong> ${record[primaryKey]}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 确认删除
function confirmDelete() {
    const index = window.currentDeletingIndex;
    if (index < 0 || !window.currentTableData[index]) {
        showError('记录不存在');
        return;
    }
    
    const record = window.currentTableData[index];
    const primaryKey = window.currentTableColumns[0];
    const primaryValue = record[primaryKey];
    
    fetch(`/api/v2/resources/data/${TABLE_NAME}/${primaryValue}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess('记录删除成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();
            loadData(currentPage); // 重新加载数据
        } else {
            showError(result.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showError('删除失败: ' + error.message);
    });
}

// 批量删除
function batchDelete() {
    console.log('batchDelete called, selectedRows.size:', selectedRows.size);
    
    if (selectedRows.size === 0) {
        showError('请先选择要删除的记录');
        return;
    }
    
    try {
        // 更新模态框中的记录数
        const countElement = document.getElementById('batchDeleteCount');
        if (countElement) {
            countElement.textContent = selectedRows.size;
        } else {
            console.error('找不到 batchDeleteCount 元素');
        }
        
        // 检查模态框元素是否存在
        const modalElement = document.getElementById('batchDeleteModal');
        if (!modalElement) {
            console.error('找不到 batchDeleteModal 元素');
            // 使用简单的确认对话框作为备用方案
            if (confirm(`确定要删除选中的 ${selectedRows.size} 条记录吗？此操作不可撤销。`)) {
                confirmBatchDelete();
            }
            return;
        }
        
        // 显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('模态框已显示');
        
    } catch (error) {
        console.error('显示批量删除模态框失败:', error);
        // 使用简单的确认对话框作为备用方案
        if (confirm(`确定要删除选中的 ${selectedRows.size} 条记录吗？此操作不可撤销。`)) {
            confirmBatchDelete();
        }
    }
}

// 确认批量删除
function confirmBatchDelete() {
    if (selectedRows.size === 0) {
        showError('没有选择要删除的记录');
        return;
    }
    
    // 调试信息
    console.log('selectedRows:', Array.from(selectedRows));
    console.log('currentTableData:', window.currentTableData);
    console.log('currentTableColumns:', window.currentTableColumns);
    
    if (!window.currentTableData || !window.currentTableColumns || window.currentTableColumns.length === 0) {
        showError('数据未正确加载，请刷新页面重试');
        return;
    }
    
    const primaryKey = window.currentTableColumns[0];
    console.log('primaryKey:', primaryKey);
    
    const idsToDelete = [];
    
    // 将selectedRows中的索引转换为实际的主键值
    Array.from(selectedRows).forEach(index => {
        console.log(`处理索引 ${index}`);
        if (window.currentTableData[index] && window.currentTableData[index][primaryKey] !== undefined) {
            const primaryValue = window.currentTableData[index][primaryKey];
            console.log(`添加ID: ${primaryValue}`);
            idsToDelete.push(primaryValue);
        } else {
            console.warn(`索引 ${index} 对应的数据不存在或缺少主键`);
        }
    });
    
    if (idsToDelete.length === 0) {
        showError('无法获取要删除的记录ID，请重新选择');
        return;
    }
    
    console.log('idsToDelete:', idsToDelete);
    
    // 注释掉JavaScript confirm，使用Bootstrap模态框
    // 已经通过Bootstrap模态框确认，不需要再次确认
    
    // 显示加载状态
    showLoading(true);
    
    fetch(`/api/v2/resources/data/${TABLE_NAME}/batch`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({ ids: idsToDelete })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        showLoading(false);
        console.log('批量删除结果:', result);
        
        if (result.success) {
            showSuccess(`成功删除 ${result.deleted_count || idsToDelete.length} 条记录`);
            
            // 关闭模态框（如果存在）
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal'));
            if (modal) {
                modal.hide();
            }
            
            clearSelection();
            loadData(currentPage); // 重新加载数据
        } else {
            showError(result.error || '批量删除失败');
        }
    })
    .catch(error => {
        showLoading(false);
        console.error('批量删除失败:', error);
        showError('批量删除失败: ' + error.message);
    });
}

// 这个重复的showSuccess函数已被移除，统一使用上面的版本

// 初始化变量
window.currentEditingIndex = -1;
window.currentDeletingIndex = -1;
window.currentTableData = [];
window.currentTableColumns = [];
window.inlineEditingRows = new Set(); // 正在行内编辑的行

// 行内编辑功能
function toggleInlineEdit(index) {
    const row = document.querySelector(`[data-row-index="${index}"]`);
    const editBtn = document.getElementById(`inlineEdit_${index}`);
    
    if (window.inlineEditingRows.has(index)) {
        // 保存行内编辑
        saveInlineEdit(index);
    } else {
        // 开始行内编辑
        startInlineEdit(index, row, editBtn);
    }
}

function startInlineEdit(index, row, editBtn) {
    const record = window.currentTableData[index];
    const cells = row.querySelectorAll('td');
    
    // 跳过选择框列和操作列，从第二列开始编辑
    for (let i = 1; i < cells.length - 1; i++) {
        const cell = cells[i];
        const columnName = window.currentTableColumns[i - 1];
        const currentValue = record[columnName];
        
        // 保存原始值
        cell.setAttribute('data-original-value', currentValue || '');
        
        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'form-control form-control-sm inline-edit-input';
        input.value = currentValue || '';
        input.style.cssText = 'width: 100%; border: 1px solid #007bff; background-color: #f8f9ff;';
        
        // 替换单元格内容
        cell.innerHTML = '';
        cell.appendChild(input);
        
        // 第一个输入框自动聚焦
        if (i === 1) {
            input.focus();
            input.select();
        }
    }
    
    // 更新按钮状态
    editBtn.className = 'btn btn-sm btn-success me-1';
    editBtn.innerHTML = '<i class="fas fa-save"></i>';
    editBtn.title = '保存';
    
    // 添加到编辑集合
    window.inlineEditingRows.add(index);
    
    // 禁用其他行的编辑按钮
    document.querySelectorAll('[id^="inlineEdit_"]').forEach(btn => {
        if (btn.id !== `inlineEdit_${index}`) {
            btn.disabled = true;
        }
    });
}

function saveInlineEdit(index) {
    const row = document.querySelector(`[data-row-index="${index}"]`);
    const editBtn = document.getElementById(`inlineEdit_${index}`);
    const record = window.currentTableData[index];
    const cells = row.querySelectorAll('td');
    
    // 收集编辑后的数据
    const updatedData = {};
    let hasChanges = false;
    
    // 添加主键
    const primaryKey = window.currentTableColumns[0];
    updatedData[primaryKey] = record[primaryKey];
    
    for (let i = 1; i < cells.length - 1; i++) {
        const cell = cells[i];
        const input = cell.querySelector('.inline-edit-input');
        const columnName = window.currentTableColumns[i - 1];
        const originalValue = cell.getAttribute('data-original-value') || '';
        const newValue = input ? input.value : originalValue;
        
        updatedData[columnName] = newValue;
        
        if (newValue !== originalValue) {
            hasChanges = true;
        }
    }
    
    if (!hasChanges) {
        // 没有变化，直接取消编辑
        cancelInlineEdit(index);
        return;
    }
    
    // 显示保存状态
    editBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    editBtn.disabled = true;
    
    // 发送更新请求
    fetch(`/api/v2/resources/data/${TABLE_NAME}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify(updatedData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess('记录更新成功');
            
            // 更新内存中的数据
            Object.assign(window.currentTableData[index], updatedData);
            
            // 恢复单元格显示
            finishInlineEdit(index, true);
        } else {
            showError(result.error || '更新失败');
            // 恢复原始值
            finishInlineEdit(index, false);
        }
    })
    .catch(error => {
        console.error('行内编辑保存失败:', error);
        showError('保存失败: ' + error.message);
        // 恢复原始值
        finishInlineEdit(index, false);
    });
}

function cancelInlineEdit(index) {
    finishInlineEdit(index, false);
}

function finishInlineEdit(index, saveSuccess) {
    const row = document.querySelector(`[data-row-index="${index}"]`);
    const editBtn = document.getElementById(`inlineEdit_${index}`);
    const cells = row.querySelectorAll('td');
    
    // 恢复单元格内容
    for (let i = 1; i < cells.length - 1; i++) {
        const cell = cells[i];
        const input = cell.querySelector('.inline-edit-input');
        const columnName = window.currentTableColumns[i - 1];
        
        if (input) {
            let displayValue;
            if (saveSuccess) {
                // 使用新值
                displayValue = input.value;
            } else {
                // 恢复原始值
                displayValue = cell.getAttribute('data-original-value') || '';
            }
            
            // 格式化显示值
            if (columnName.includes('DATE') || columnName.includes('TIME')) {
                displayValue = formatDateTime(displayValue);
            }
            
            cell.innerHTML = displayValue;
            cell.title = displayValue;
        }
    }
    
    // 恢复按钮状态
    editBtn.className = 'btn btn-sm btn-outline-success me-1';
    editBtn.innerHTML = '<i class="fas fa-pen"></i>';
    editBtn.title = '行内编辑';
    editBtn.disabled = false;
    
    // 从编辑集合中移除
    window.inlineEditingRows.delete(index);
    
    // 启用其他行的编辑按钮
    document.querySelectorAll('[id^="inlineEdit_"]').forEach(btn => {
        btn.disabled = false;
    });
}

// 添加ESC键取消编辑的支持
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && window.inlineEditingRows.size > 0) {
        const editingIndex = Array.from(window.inlineEditingRows)[0];
        cancelInlineEdit(editingIndex);
    }
});
</script>
{% endblock %} 