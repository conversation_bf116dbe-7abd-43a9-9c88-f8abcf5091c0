{% extends "resources/base_resource.html" %}

{% block title %}产品周期管理 - AEC-FT ICP{% endblock %}

{% set page_title = "产品周期管理" %}
{% set page_description = "世界领先的半导体产品周期管理系统，支持实时监控、智能调度和预测性维护。基于API v3的现代化产品周期管理平台。" %}
{% set table_title = "产品周期" %}
{% set table_name = "ct" %}
{% set use_api_v3 = true %}

{% block extra_css %}
{{ super() }}
<style>
/* 产品周期专用样式 */
.cycle-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}
.cycle-fast { background-color: #28a745; color: #fff; }
.cycle-normal { background-color: #17a2b8; color: #fff; }
.cycle-slow { background-color: #ffc107; color: #000; }
.cycle-critical { background-color: #dc3545; color: #fff; }

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}
.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stats-label {
    color: #6c757d;
    font-size: 14px;
}
</style>
{% endblock %}

{% block content %}
<!-- 在基础模板内容之前添加统计卡片 -->
<div class="row stats-cards mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-primary" id="totalProducts">0</div>
            <div class="stats-label">产品总数</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-success" id="fastCycles">0</div>
            <div class="stats-label">快速周期</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-warning" id="slowCycles">0</div>
            <div class="stats-label">慢速周期</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-danger" id="criticalCycles">0</div>
            <div class="stats-label">关键周期</div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// 产品周期管理专用JavaScript扩展

// 页面加载完成后的额外初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 产品周期管理v3页面加载完成');

    // 重写表格渲染函数以支持周期显示
    const originalRenderTable = window.renderTable;
    window.renderTable = function(columns, rows) {
        originalRenderTable(columns, rows);
        updateStatsCards(rows);
        applyCycleStyling();
    };
});

// 更新统计卡片
function updateStatsCards(data) {
    if (!data || !Array.isArray(data)) return;

    const totalProducts = data.length;
    let fastCycles = 0;
    let slowCycles = 0;
    let criticalCycles = 0;

    data.forEach(row => {
        const cycleTime = parseFloat(row.CYCLE_TIME) || 0;
        if (cycleTime > 0) {
            if (cycleTime <= 60) {
                fastCycles++;
            } else if (cycleTime <= 120) {
                // normal cycles - not counted separately
            } else if (cycleTime <= 300) {
                slowCycles++;
            } else {
                criticalCycles++;
            }
        }
    });

    // 更新统计数字
    const totalProductsEl = document.getElementById('totalProducts');
    const fastCyclesEl = document.getElementById('fastCycles');
    const slowCyclesEl = document.getElementById('slowCycles');
    const criticalCyclesEl = document.getElementById('criticalCycles');

    if (totalProductsEl) totalProductsEl.textContent = totalProducts;
    if (fastCyclesEl) fastCyclesEl.textContent = fastCycles;
    if (slowCyclesEl) slowCyclesEl.textContent = slowCycles;
    if (criticalCyclesEl) criticalCyclesEl.textContent = criticalCycles;
}

// 应用周期样式
function applyCycleStyling() {
    const tableBody = document.getElementById('tableBody');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            const headerCell = document.querySelector(`#tableHeaders th:nth-child(${index + 1})`);
            if (headerCell && headerCell.textContent.includes('周期')) {
                // 找到周期列，应用徽章样式
                const cycleValue = parseFloat(cell.textContent.trim()) || 0;
                if (cycleValue > 0 && !cell.querySelector('.cycle-badge')) {
                    const cycleClass = getCycleClass(cycleValue);
                    cell.innerHTML = `<span class="cycle-badge ${cycleClass}">${cycleValue}s</span>`;
                }
            }
        });
    });
}

// 根据周期时间获取样式类
function getCycleClass(cycleTime) {
    if (cycleTime <= 60) {
        return 'cycle-fast';
    } else if (cycleTime <= 120) {
        return 'cycle-normal';
    } else if (cycleTime <= 300) {
        return 'cycle-slow';
    } else {
        return 'cycle-critical';
    }
}

console.log('✅ 产品周期管理v3 JavaScript扩展加载完成');
</script>
{% endblock %}