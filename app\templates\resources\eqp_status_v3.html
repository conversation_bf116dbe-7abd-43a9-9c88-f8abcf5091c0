{% extends "resources/base_resource.html" %}

{% block title %}设备状态管理 - AEC-FT ICP{% endblock %}

{% set page_title = "设备状态管理" %}
{% set page_description = "世界领先的半导体设备状态管理系统，支持实时监控、智能调度和预测性维护。基于API v3的现代化设备状态管理平台。" %}
{% set table_title = "设备状态" %}
{% set table_name = "eqp_status" %}
{% set use_api_v3 = true %}

{% block extra_css %}
{{ super() }}
<style>
/* 设备状态专用样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}
.status-IDLE { background-color: #ffc107; color: #000; }
.status-RUN { background-color: #28a745; color: #fff; }
.status-DOWN { background-color: #dc3545; color: #fff; }
.status-MAINT { background-color: #6c757d; color: #fff; }

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}
.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stats-label {
    color: #6c757d;
    font-size: 14px;
}
</style>
{% endblock %}
{% block content %}
<!-- 在基础模板内容之前添加统计卡片 -->
<div class="row stats-cards mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-success" id="runningCount">0</div>
            <div class="stats-label">运行中设备</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-warning" id="idleCount">0</div>
            <div class="stats-label">空闲设备</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-danger" id="downCount">0</div>
            <div class="stats-label">故障设备</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-secondary" id="maintCount">0</div>
            <div class="stats-label">维护中设备</div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// 设备状态管理专用JavaScript扩展

// 页面加载完成后的额外初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 设备状态管理v3页面加载完成');

    // 添加状态筛选器
    addStatusFilter();

    // 重写表格渲染函数以支持状态显示
    const originalRenderTable = window.renderTable;
    window.renderTable = function(columns, rows) {
        originalRenderTable(columns, rows);
        updateStatsCards(rows);
    };
});

// 添加状态筛选器
function addStatusFilter() {
    const filterConditions = document.getElementById('filterConditions');
    if (filterConditions) {
        const firstRow = filterConditions.querySelector('.filter-row');
        if (firstRow) {
            const fieldSelect = firstRow.querySelector('select[name="field"]');
            if (fieldSelect) {
                // 添加状态筛选选项
                const statusOption = document.createElement('option');
                statusOption.value = 'STATUS';
                statusOption.textContent = '状态';
                fieldSelect.appendChild(statusOption);
            }
        }
    }
}

// 更新统计卡片
function updateStatsCards(data) {
    const stats = {
        RUN: 0,
        IDLE: 0,
        DOWN: 0,
        MAINT: 0
    };

    if (data && Array.isArray(data)) {
        data.forEach(row => {
            if (stats.hasOwnProperty(row.STATUS)) {
                stats[row.STATUS]++;
            }
        });
    }

    // 更新统计数字
    const runningCount = document.getElementById('runningCount');
    const idleCount = document.getElementById('idleCount');
    const downCount = document.getElementById('downCount');
    const maintCount = document.getElementById('maintCount');

    if (runningCount) runningCount.textContent = stats.RUN;
    if (idleCount) idleCount.textContent = stats.IDLE;
    if (downCount) downCount.textContent = stats.DOWN;
    if (maintCount) maintCount.textContent = stats.MAINT;
}

// 重写表格单元格渲染以支持状态徽章
const originalCreateTableCell = window.createTableCell || function(column, value, row) {
    const td = document.createElement('td');

    if (column === 'STATUS' && value) {
        // 状态列特殊处理
        const badge = document.createElement('span');
        badge.className = `status-badge status-${value}`;
        badge.textContent = getStatusText(value);
        td.appendChild(badge);
    } else if (column.includes('DATE') || column.includes('TIME')) {
        // 日期时间格式化
        td.textContent = formatDateTime(value);
    } else {
        // 普通文本
        td.textContent = value || '';
    }

    td.title = String(value || ''); // 添加tooltip
    return td;
};

// 状态文本映射
function getStatusText(status) {
    const statusMap = {
        'RUN': '运行中',
        'IDLE': '空闲',
        'DOWN': '故障',
        'MAINT': '维护中'
    };
    return statusMap[status] || status || '未知';
}

// 格式化日期时间
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    try {
        return new Date(dateStr).toLocaleString('zh-CN');
    } catch {
        return dateStr;
    }
}

// 扩展基础表格渲染函数
if (typeof window.renderTable === 'function') {
    const originalRenderTable = window.renderTable;
    window.renderTable = function(columns, rows) {
        // 调用原始渲染函数
        originalRenderTable(columns, rows);

        // 应用设备状态特殊样式
        applyStatusStyling();

        // 更新统计卡片
        updateStatsCards(rows);
    };
}

// 应用状态样式
function applyStatusStyling() {
    const tableBody = document.getElementById('tableBody');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            const headerCell = document.querySelector(`#tableHeaders th:nth-child(${index + 1})`);
            if (headerCell && headerCell.textContent.includes('状态')) {
                // 找到状态列，应用徽章样式
                const statusText = cell.textContent.trim();
                if (statusText && !cell.querySelector('.status-badge')) {
                    const statusValue = getStatusValue(statusText);
                    if (statusValue) {
                        cell.innerHTML = `<span class="status-badge status-${statusValue}">${statusText}</span>`;
                    }
                }
            }
        });
    });
}

// 根据状态文本获取状态值
function getStatusValue(statusText) {
    const reverseStatusMap = {
        '运行中': 'RUN',
        '空闲': 'IDLE',
        '故障': 'DOWN',
        '维护中': 'MAINT'
    };
    return reverseStatusMap[statusText] || statusText;
}

console.log('✅ 设备状态管理v3 JavaScript扩展加载完成');
</script>
{% endblock %}




