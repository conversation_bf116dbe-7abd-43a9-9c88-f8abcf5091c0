{% extends "resources/base_resource.html" %}

{% block title %}测试规格管理 - AEC-FT ICP{% endblock %}

{% set page_title = "测试规格管理" %}
{% set page_description = "世界领先的半导体测试规格管理系统，支持实时监控、智能调度和预测性维护。基于API v3的现代化测试规格管理平台。" %}
{% set table_title = "测试规格" %}
{% set table_name = "et_ft_test_spec" %}
{% set use_api_v3 = true %}

{% block extra_css %}
{{ super() }}
<style>
/* 测试规格专用样式 */
.spec-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}
.spec-pass { background-color: #28a745; color: #fff; }
.spec-fail { background-color: #dc3545; color: #fff; }
.spec-pending { background-color: #ffc107; color: #000; }

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}
.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stats-label {
    color: #6c757d;
    font-size: 14px;
}
</style>
{% endblock %}

{% block content %}
<!-- 在基础模板内容之前添加统计卡片 -->
<div class="row stats-cards mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-primary" id="totalSpecs">0</div>
            <div class="stats-label">规格总数</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-success" id="passSpecs">0</div>
            <div class="stats-label">通过规格</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-danger" id="failSpecs">0</div>
            <div class="stats-label">失败规格</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-warning" id="pendingSpecs">0</div>
            <div class="stats-label">待测规格</div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// 测试规格管理专用JavaScript扩展

// 页面加载完成后的额外初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 测试规格管理v3页面加载完成');

    // 重写表格渲染函数以支持规格显示
    const originalRenderTable = window.renderTable;
    window.renderTable = function(columns, rows) {
        originalRenderTable(columns, rows);
        updateStatsCards(rows);
        applySpecStyling();
    };
});

// 更新统计卡片
function updateStatsCards(data) {
    if (!data || !Array.isArray(data)) return;

    const totalSpecs = data.length;
    let passSpecs = 0;
    let failSpecs = 0;
    let pendingSpecs = 0;

    data.forEach(row => {
        const status = row.STATUS || row.TEST_RESULT || '';
        if (status.toLowerCase().includes('pass') || status.toLowerCase().includes('通过')) {
            passSpecs++;
        } else if (status.toLowerCase().includes('fail') || status.toLowerCase().includes('失败')) {
            failSpecs++;
        } else if (status.toLowerCase().includes('pending') || status.toLowerCase().includes('待测')) {
            pendingSpecs++;
        }
    });

    // 更新统计数字
    const totalSpecsEl = document.getElementById('totalSpecs');
    const passSpecsEl = document.getElementById('passSpecs');
    const failSpecsEl = document.getElementById('failSpecs');
    const pendingSpecsEl = document.getElementById('pendingSpecs');

    if (totalSpecsEl) totalSpecsEl.textContent = totalSpecs;
    if (passSpecsEl) passSpecsEl.textContent = passSpecs;
    if (failSpecsEl) failSpecsEl.textContent = failSpecs;
    if (pendingSpecsEl) pendingSpecsEl.textContent = pendingSpecs;
}

// 应用规格样式
function applySpecStyling() {
    const tableBody = document.getElementById('tableBody');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            const headerCell = document.querySelector(`#tableHeaders th:nth-child(${index + 1})`);
            if (headerCell && (headerCell.textContent.includes('状态') || headerCell.textContent.includes('结果'))) {
                // 找到状态列，应用徽章样式
                const statusText = cell.textContent.trim();
                if (statusText && !cell.querySelector('.spec-badge')) {
                    const specClass = getSpecClass(statusText);
                    cell.innerHTML = `<span class="spec-badge ${specClass}">${statusText}</span>`;
                }
            }
        });
    });
}

// 根据状态获取样式类
function getSpecClass(statusText) {
    const status = statusText.toLowerCase();
    if (status.includes('pass') || status.includes('通过')) {
        return 'spec-pass';
    } else if (status.includes('fail') || status.includes('失败')) {
        return 'spec-fail';
    } else {
        return 'spec-pending';
    }
}

console.log('✅ 测试规格管理v3 JavaScript扩展加载完成');
</script>
{% endblock %}