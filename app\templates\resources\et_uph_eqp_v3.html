{% extends "resources/base_resource.html" %}

{% block title %}UPH设备管理 - AEC-FT ICP{% endblock %}

{% set page_title = "UPH设备管理" %}
{% set page_description = "世界领先的半导体UPH设备管理系统，支持实时监控、智能调度和预测性维护。基于API v3的现代化UPH设备管理平台。" %}
{% set table_title = "UPH设备" %}
{% set table_name = "et_uph_eqp" %}
{% set use_api_v3 = true %}

{% block extra_css %}
{{ super() }}
<style>
/* UPH设备专用样式 */
.uph-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}
.uph-high { background-color: #28a745; color: #fff; }
.uph-medium { background-color: #ffc107; color: #000; }
.uph-low { background-color: #dc3545; color: #fff; }

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}
.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stats-label {
    color: #6c757d;
    font-size: 14px;
}
</style>
{% endblock %}

{% block content %}
<!-- 在基础模板内容之前添加统计卡片 -->
<div class="row stats-cards mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-success" id="totalEquipment">0</div>
            <div class="stats-label">设备总数</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-info" id="avgUph">0</div>
            <div class="stats-label">平均UPH</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-warning" id="highUphCount">0</div>
            <div class="stats-label">高效设备</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number text-danger" id="lowUphCount">0</div>
            <div class="stats-label">低效设备</div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// UPH设备管理专用JavaScript扩展

// 页面加载完成后的额外初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 UPH设备管理v3页面加载完成');
    
    // 重写表格渲染函数以支持UPH显示
    const originalRenderTable = window.renderTable;
    window.renderTable = function(columns, rows) {
        originalRenderTable(columns, rows);
        updateStatsCards(rows);
        applyUphStyling();
    };
});

// 更新统计卡片
function updateStatsCards(data) {
    if (!data || !Array.isArray(data)) return;
    
    const totalEquipment = data.length;
    let totalUph = 0;
    let highUphCount = 0;
    let lowUphCount = 0;
    
    data.forEach(row => {
        const uph = parseFloat(row.UPH) || 0;
        totalUph += uph;
        
        if (uph >= 1000) {
            highUphCount++;
        } else if (uph < 500) {
            lowUphCount++;
        }
    });
    
    const avgUph = totalEquipment > 0 ? Math.round(totalUph / totalEquipment) : 0;
    
    // 更新统计数字
    const totalEquipmentEl = document.getElementById('totalEquipment');
    const avgUphEl = document.getElementById('avgUph');
    const highUphCountEl = document.getElementById('highUphCount');
    const lowUphCountEl = document.getElementById('lowUphCount');
    
    if (totalEquipmentEl) totalEquipmentEl.textContent = totalEquipment;
    if (avgUphEl) avgUphEl.textContent = avgUph;
    if (highUphCountEl) highUphCountEl.textContent = highUphCount;
    if (lowUphCountEl) lowUphCountEl.textContent = lowUphCount;
}

// 应用UPH样式
function applyUphStyling() {
    const tableBody = document.getElementById('tableBody');
    if (!tableBody) return;
    
    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            const headerCell = document.querySelector(`#tableHeaders th:nth-child(${index + 1})`);
            if (headerCell && headerCell.textContent.includes('UPH')) {
                // 找到UPH列，应用徽章样式
                const uphValue = parseFloat(cell.textContent.trim()) || 0;
                if (uphValue > 0 && !cell.querySelector('.uph-badge')) {
                    const uphClass = getUphClass(uphValue);
                    cell.innerHTML = `<span class="uph-badge ${uphClass}">${uphValue}</span>`;
                }
            }
        });
    });
}

// 根据UPH值获取样式类
function getUphClass(uphValue) {
    if (uphValue >= 1000) {
        return 'uph-high';
    } else if (uphValue >= 500) {
        return 'uph-medium';
    } else {
        return 'uph-low';
    }
}

console.log('✅ UPH设备管理v3 JavaScript扩展加载完成');
</script>
{% endblock %}
