﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API v3 迁移测试中心</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}" rel="stylesheet">
    <style>
        .migration-card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .migration-card:hover { transform: translateY(-2px); }
        .status-perfect { color: #28a745; }
        .status-good { color: #ffc107; }
        .status-poor { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-rocket text-primary"></i>
                    API v3 迁移测试中心
                </h1>
                <p class="text-center text-muted">动态字段管理器 vs 硬编码方案对比测试</p>
            </div>
        </div>

        <!-- 迁移状态面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card migration-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> 迁移状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="migration-status" class="text-center">
                            <i class="fas fa-spinner fa-spin"></i> 正在检查迁移状态...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API对比测试 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card migration-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-database"></i> API v2 (硬编码)</h5>
                    </div>
                    <div class="card-body">
                        <select id="table-v2" class="form-select mb-3">
                            <option value="eqp_status">设备状态</option>
                            <option value="et_uph_eqp">UPH设备</option>
                            <option value="ct">产品周期</option>
                        </select>
                        <button id="test-v2" class="btn btn-warning">测试API v2</button>
                        <div id="result-v2" class="mt-3"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card migration-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-magic"></i> API v3 (动态字段)</h5>
                    </div>
                    <div class="card-body">
                        <select id="table-v3" class="form-select mb-3">
                            <option value="eqp_status">设备状态</option>
                            <option value="et_uph_eqp">UPH设备</option>
                            <option value="ct">产品周期</option>
                        </select>
                        <button id="test-v3" class="btn btn-success">测试API v3</button>
                        <div id="result-v3" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 字段验证 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card migration-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-check-circle"></i> 字段映射验证</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <select id="validate-table" class="form-select">
                                    <option value="eqp_status">设备状态</option>
                                    <option value="et_uph_eqp">UPH设备</option>
                                    <option value="ct">产品周期</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button id="validate-btn" class="btn btn-info">验证字段映射</button>
                            </div>
                        </div>
                        <div id="validate-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        const API_V2 = '/api/v2/resources';
        const API_V3 = '/api/v3';

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkMigrationStatus();
        });

        // 检查迁移状态
        async function checkMigrationStatus() {
            try {
                const response = await fetch(`${API_V3}/migration/status`);
                const data = await response.json();
                
                document.getElementById('migration-status').innerHTML = `
                    <div class="row">
                        <div class="col-3 text-center">
                            <h4 class="text-primary">${data.status.total_tables}</h4>
                            <small>支持表数</small>
                        </div>
                        <div class="col-3 text-center">
                            <h4 class="text-success">✓</h4>
                            <small>迁移就绪</small>
                        </div>
                        <div class="col-3 text-center">
                            <h4 class="text-info">${data.status.cache_size}</h4>
                            <small>缓存项</small>
                        </div>
                        <div class="col-3 text-center">
                            <h4 class="text-success">v3</h4>
                            <small>API版本</small>
                        </div>
                    </div>
                `;
            } catch (error) {
                document.getElementById('migration-status').innerHTML = 
                    `<div class="alert alert-danger">状态检查失败: ${error.message}</div>`;
            }
        }

        // 测试API v2
        document.getElementById('test-v2').addEventListener('click', async function() {
            const table = document.getElementById('table-v2').value;
            const resultDiv = document.getElementById('result-v2');
            
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
            
            try {
                const start = performance.now();
                const response = await fetch(`${API_V2}/${table}/data?page=1&per_page=5`);
                const data = await response.json();
                const time = (performance.now() - start).toFixed(2);
                
                resultDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <strong>API v2 结果:</strong><br>
                        记录数: ${data.total || 0}<br>
                        字段数: ${data.columns ? data.columns.length : 0}<br>
                        耗时: ${time}ms<br>
                        状态: ${data.success ? '✓ 成功' : '✗ 失败'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">测试失败: ${error.message}</div>`;
            }
        });

        // 测试API v3
        document.getElementById('test-v3').addEventListener('click', async function() {
            const table = document.getElementById('table-v3').value;
            const resultDiv = document.getElementById('result-v3');
            
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
            
            try {
                const start = performance.now();
                const response = await fetch(`${API_V3}/tables/${table}/data?page=1&per_page=5`);
                const data = await response.json();
                const time = (performance.now() - start).toFixed(2);
                
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>API v3 结果:</strong><br>
                        记录数: ${data.total}<br>
                        字段数: ${data.columns.length}<br>
                        耗时: ${time}ms<br>
                        状态: ${data.success ? '✓ 成功' : '✗ 失败'}<br>
                        业务键: ${data.table_info.business_key}<br>
                        数据库: ${data.data_source}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">测试失败: ${error.message}</div>`;
            }
        });

        // 验证字段映射
        document.getElementById('validate-btn').addEventListener('click', async function() {
            const table = document.getElementById('validate-table').value;
            const resultDiv = document.getElementById('validate-result');
            
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';
            
            try {
                const response = await fetch(`${API_V3}/tables/${table}/validate`);
                const data = await response.json();
                
                const statusClass = data.status === 'perfect' ? 'success' : 
                                  data.status === 'good' ? 'warning' : 'danger';
                
                resultDiv.innerHTML = `
                    <div class="alert alert-${statusClass}">
                        <h6><i class="fas fa-check-circle"></i> 验证结果 - ${data.table_name}</h6>
                        <div class="row">
                            <div class="col-3">
                                <strong>匹配率:</strong><br>
                                <span class="h4 status-${data.status}">${data.match_rate}%</span>
                            </div>
                            <div class="col-3">
                                <strong>配置字段:</strong><br>
                                ${data.config_fields_count}个
                            </div>
                            <div class="col-3">
                                <strong>数据库字段:</strong><br>
                                ${data.db_fields_count}个
                            </div>
                            <div class="col-3">
                                <strong>匹配字段:</strong><br>
                                ${data.common_fields_count}个
                            </div>
                        </div>
                        ${data.recommendations ? '<hr><strong>建议:</strong> ' + data.recommendations.join(', ') : ''}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger">验证失败: ${error.message}</div>`;
            }
        });

        // 同步表选择器
        ['table-v2', 'table-v3', 'validate-table'].forEach(id => {
            document.getElementById(id).addEventListener('change', function() {
                ['table-v2', 'table-v3', 'validate-table'].forEach(otherId => {
                    if (otherId !== id) {
                        document.getElementById(otherId).value = this.value;
                    }
                });
            });
        });
    </script>
</body>
</html>
