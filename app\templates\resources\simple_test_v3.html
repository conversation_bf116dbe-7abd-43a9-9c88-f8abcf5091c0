<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{ page_title }} - API v3测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            color: #d21919;
            border-bottom: 2px solid #d21919;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            margin: 20px 0;
            border-radius: 6px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .loading { text-align: center; padding: 20px; }
        .table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px;
        }
        .table th, .table td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left;
        }
        .table th { 
            background: #f8f9fa; 
            font-weight: bold;
        }
        .business-key { background: #fff3cd; font-weight: bold; }
        .readonly-field { background: #f8f9fa; color: #6c757d; }
        .datetime-field { background: #e7f3ff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ page_title }}<span class="badge">API v3</span></h1>
            <p>表名: <strong>{{ table_name }}</strong></p>
        </div>
        
        <div class="info-box">
            <h3>📊 API v3动态字段管理测试</h3>
            <p id="table-info">加载表信息中...</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="loadData()">🔄 刷新数据</button>
            <button class="btn btn-success" onclick="validateFields()">✅ 验证字段</button>
            <span id="status"></span>
        </div>
        
        <div id="content">
            <div class="loading">加载中...</div>
        </div>
    </div>

    <script>
        const TABLE_NAME = '{{ table_name }}';
        const API_BASE = '/api/v3';
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 API v3测试页面加载完成:', TABLE_NAME);
            initializePage();
        });
        
        async function initializePage() {
            try {
                // 加载表信息
                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/columns`);
                const data = await response.json();
                
                if (data.success) {
                    updateTableInfo(data);
                    await loadData();
                } else {
                    throw new Error(data.error || '获取表信息失败');
                }
            } catch (error) {
                console.error('初始化失败:', error);
                showError('初始化失败: ' + error.message);
            }
        }
        
        function updateTableInfo(data) {
            const info = data.table_info || {};
            document.getElementById('table-info').innerHTML = `
                <strong>主键:</strong> ${info.primary_key || 'id'} | 
                <strong>业务键:</strong> ${info.business_key || '未识别'} | 
                <strong>字段数:</strong> ${data.columns ? data.columns.length : 0} | 
                <strong>数据库:</strong> ${info.database || 'MySQL'}
            `;
        }
        
        async function loadData() {
            try {
                showStatus('加载数据中...', 'info');
                
                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?page=1&per_page=20`);
                const data = await response.json();
                
                if (data.success) {
                    renderTable(data.columns, data.data);
                    showStatus(`成功加载 ${data.data.length}/${data.total} 条记录`, 'success');
                } else {
                    throw new Error(data.error || '数据加载失败');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('加载数据失败: ' + error.message);
            }
        }
        
        async function validateFields() {
            try {
                showStatus('验证字段中...', 'info');
                
                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/validate`);
                const data = await response.json();
                
                if (data.success) {
                    const matchRate = data.match_rate || 0;
                    showStatus(`字段验证完成: ${matchRate}% 匹配率`, 'success');
                } else {
                    throw new Error(data.error || '字段验证失败');
                }
            } catch (error) {
                console.error('字段验证失败:', error);
                showError('字段验证失败: ' + error.message);
            }
        }
        
        function renderTable(columns, rows) {
            if (!columns || !rows) {
                document.getElementById('content').innerHTML = '<p>无数据</p>';
                return;
            }
            
            let html = '<table class="table">';
            
            // 表头
            html += '<thead><tr>';
            columns.forEach(col => {
                html += `<th>${col}</th>`;
            });
            html += '</tr></thead>';
            
            // 数据行
            html += '<tbody>';
            rows.forEach(row => {
                html += '<tr>';
                columns.forEach(col => {
                    const value = row[col] || '';
                    html += `<td>${value}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table>';
            
            document.getElementById('content').innerHTML = html;
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.color = type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue';
        }
        
        function showError(message) {
            document.getElementById('content').innerHTML = `
                <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 4px;">
                    ❌ ${message}
                </div>
            `;
            showStatus('发生错误', 'error');
        }
    </script>
</body>
</html> 