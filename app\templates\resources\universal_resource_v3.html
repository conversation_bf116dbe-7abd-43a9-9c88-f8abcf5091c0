{% extends "base.html" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<!-- 引入APS主题样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
<style>


.table-responsive {
    max-height: 75vh;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.table th {
    white-space: nowrap;
    min-width: 80px;
    max-width: 200px;
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
    padding: 6px 8px;
    font-size: 0.875rem;
    font-weight: 600;
}

.table td {
    white-space: nowrap;
    padding: 4px 8px;
    font-size: 0.875rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-sm th,
.table-sm td {
    padding: 4px 6px;
}

/* 紧凑型按钮 */
.btn-sm {
    padding: 2px 6px;
    font-size: 0.75rem;
}

/* 紧凑型表单控件 */
.form-control-sm, .form-select-sm {
    padding: 2px 6px;
    font-size: 0.875rem;
}

/* 操作列固定宽度 */
.action-column {
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}

/* 选择列固定宽度 */
.select-column {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    text-align: center;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.filter-row {
    display: flex;
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
}

.filter-field, .filter-operator, .filter-value {
    flex: 1;
}

.filter-actions {
    flex: 0 0 auto;
}

/* 卡片头部样式 */
.card-header {
    background-color: var(--aps-primary);
    color: white;
}



/* 业务键字段高亮 */
.business-key-col {
    background-color: #fff3cd !important;
    font-weight: bold;
}

/* 只读字段样式 */
.readonly-col {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* 日期时间字段样式 */
.datetime-col {
    background-color: #e7f3ff !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">{{ page_title }}</h5>
                        <div>
                            <button type="button" class="btn btn-success me-2" onclick="addRecord()">
                                <i class="fas fa-plus me-1"></i>新增
                            </button>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-info me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                        </div>
                    </div>
                    <!-- 高级筛选面板 -->
                    <div class="mb-3">
                        <h6>
                            <i class="fas fa-filter me-2"></i>高级筛选
                            <small class="text-muted ms-2">支持多条件组合查询</small>
                        </h6>
                        
                        <!-- 筛选条件 -->
                        <div id="filterConditions">
                            <div class="filter-row" data-index="0">
                                <div class="filter-field">
                                    <label class="form-label form-label-sm">字段</label>
                                    <select class="form-select form-select-sm" name="field">
                                        <option value="">请选择字段</option>
                                    </select>
                                </div>
                                <div class="filter-operator">
                                    <label class="form-label form-label-sm">操作符</label>
                                    <select class="form-select form-select-sm" name="operator">
                                        <option value="contains">包含</option>
                                        <option value="equals">等于</option>
                                        <option value="starts_with">开始于</option>
                                        <option value="ends_with">结束于</option>
                                        <option value="not_equals">不等于</option>
                                    </select>
                                </div>
                                <div class="filter-value">
                                    <label class="form-label form-label-sm">值</label>
                                    <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                </div>
                                <div class="filter-actions">
                                    <label class="form-label form-label-sm">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyFilter()">
                                    <i class="fas fa-search me-1"></i>应用筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">{{ page_title }}数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                    <option value="500">500 条/页</option>
                                    <option value="1000">1000 条/页</option>
                                    <option value="5000">5000 条/页</option>
                                    <option value="10000">显示全部</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="dataTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增/编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">新增记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="formFields">
                        <!-- 表单字段将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRecord()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条记录吗？此操作不可撤销。</p>
                <div id="deleteRecordInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="batchDeleteCount">0</span> 条记录吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let totalRecords = 0;
let advancedFilters = [];
let filterConditionIndex = 0;
let availableFields = [];
let tableData = [];
let sortColumn = '';
let sortDirection = 'asc'; // 'asc' 或 'desc'
let currentColumns = [];

// 表名配置
const TABLE_NAME = '{{ table_name }}';
const API_BASE = '/api/v3';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(`${TABLE_NAME} 页面加载完成，开始初始化数据...`);
    initializePage();
});

// 初始化页面
async function initializePage() {
    try {
        showLoading(true);
        await loadTableStructure();
        await loadData();
        bindEvents();
        console.log('✅ 页面初始化完成');
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        showError('页面初始化失败: ' + error.message);

        // 显示错误状态
        document.getElementById('tableBody').innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>数据加载失败</div>
                    <div class="small">${error.message}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="initializePage()">
                        <i class="fas fa-redo me-1"></i>重试
                    </button>
                </td>
            </tr>
        `;
    } finally {
        showLoading(false);
    }
}

// 加载表结构
async function loadTableStructure() {
    try {
        console.log(`📋 正在加载表结构: ${TABLE_NAME}`);
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/structure`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('用户未登录或会话已过期，请重新登录');
            } else if (response.status === 403) {
                throw new Error('没有权限访问此资源');
            } else if (response.status === 404) {
                throw new Error(`表 ${TABLE_NAME} 不存在`);
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        }

        const result = await response.json();

        if (result.success) {
            currentColumns = result.columns || [];
            console.log(`✅ 表结构加载成功，字段数: ${currentColumns.length}`);
            setupFilterFields();
        } else {
            throw new Error(result.error || '获取表结构失败');
        }
    } catch (error) {
        console.error('❌ 加载表结构失败:', error);
        throw error;
    }
}

// 设置筛选字段选项
function setupFilterFields() {
    const filterField = document.getElementById('filterConditions').querySelector('select[name="field"]');
    if (!filterField) return;

    filterField.innerHTML = '<option value="">请选择字段</option>';

    currentColumns.forEach(column => {
        if (!column.hidden) {
            const option = document.createElement('option');
            option.value = column.name;
            option.textContent = column.display_name || column.name;
            filterField.appendChild(option);
        }
    });

    console.log(`✅ 筛选字段设置完成，可用字段: ${currentColumns.filter(c => !c.hidden).length}个`);
}

// 加载数据
async function loadData() {
    showLoading(true);

    try {
        console.log(`📊 正在加载数据: ${TABLE_NAME}, 页码: ${currentPage}`);

        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            per_page: pageSize
        });

        // 添加排序参数
        if (sortColumn) {
            params.append('sort_by', sortColumn);
            params.append('sort_order', sortDirection);
        }

        // 添加筛选参数
        if (advancedFilters.length > 0) {
            params.append('filters', JSON.stringify(advancedFilters));
        }

        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            tableData = result.data || [];
            totalRecords = result.total || 0;
            totalPages = Math.ceil(totalRecords / pageSize);

            console.log(`✅ 数据加载成功: ${tableData.length} 条记录`);

            renderTable();
            renderPagination();
            updateRecordCount();
        } else {
            throw new Error(result.error || '获取数据失败');
        }
    } catch (error) {
        console.error('❌ 加载数据失败:', error);
        showError('数据加载失败: ' + error.message);

        document.getElementById('tableBody').innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>数据加载失败</div>
                    <div class="small">${error.message}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadData()">
                        <i class="fas fa-redo me-1"></i>重试
                    </button>
                </td>
            </tr>
        `;
    } finally {
        showLoading(false);
    }
}

// 渲染表格
function renderTable() {
    const tableHeaders = document.getElementById('tableHeaders');
    const tableBody = document.getElementById('tableBody');

    // 清空现有内容
    tableHeaders.innerHTML = '';
    tableBody.innerHTML = '';

    if (!currentColumns.length || !tableData.length) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <div>暂无数据</div>
                </td>
            </tr>
        `;
        return;
    }

    // 渲染表头
    renderTableHeaders();

    // 渲染数据行
    renderTableRows();
}

// 渲染表头
function renderTableHeaders() {
    const tableHeaders = document.getElementById('tableHeaders');

    // 选择列
    const selectTh = document.createElement('th');
    selectTh.className = 'select-column';
    selectTh.innerHTML = `
        <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
    `;
    tableHeaders.appendChild(selectTh);

    // 数据列
    currentColumns.forEach(column => {
        if (!column.hidden) {
            const th = document.createElement('th');
            th.innerHTML = `
                <div class="d-flex align-items-center justify-content-between">
                    <span>${column.display_name || column.name}</span>
                    <div class="sort-icons">
                        <i class="fas fa-sort text-muted" style="cursor: pointer;" onclick="sortTable('${column.name}')"></i>
                    </div>
                </div>
            `;

            // 添加字段类型样式
            if (column.primary_key) {
                th.classList.add('readonly-col');
            } else if (column.type === 'datetime') {
                th.classList.add('datetime-col');
            }

            tableHeaders.appendChild(th);
        }
    });

    // 操作列
    const actionTh = document.createElement('th');
    actionTh.className = 'action-column';
    actionTh.textContent = '操作';
    tableHeaders.appendChild(actionTh);
}

// 渲染数据行
function renderTableRows() {
    const tableBody = document.getElementById('tableBody');

    tableData.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-row-index', index);

        // 选择列
        const selectTd = document.createElement('td');
        selectTd.className = 'select-column';
        selectTd.innerHTML = `
            <input type="checkbox" class="form-check-input row-select" value="${index}" onchange="updateBatchOperations()">
        `;
        tr.appendChild(selectTd);

        // 数据列
        currentColumns.forEach(column => {
            if (!column.hidden) {
                const td = document.createElement('td');
                const value = row[column.name] || '';

                // 格式化显示值
                td.innerHTML = formatCellValue(value, column);
                td.title = value; // 悬停显示完整内容

                // 添加字段类型样式
                if (column.primary_key) {
                    td.classList.add('readonly-col');
                } else if (column.type === 'datetime') {
                    td.classList.add('datetime-col');
                }

                tr.appendChild(td);
            }
        });

        // 操作列
        const actionTd = document.createElement('td');
        actionTd.className = 'action-column';
        actionTd.innerHTML = `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editRecord(${index})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${index})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        tr.appendChild(actionTd);

        tableBody.appendChild(tr);
    });
}

// 格式化单元格值
function formatCellValue(value, column) {
    if (!value) return '';

    // 根据字段类型格式化
    switch (column.type) {
        case 'status_badge':
            return formatStatusBadge(value);
        case 'datetime':
            return formatDateTime(value);
        default:
            return escapeHtml(value);
    }
}

// 格式化状态徽章
function formatStatusBadge(value) {
    const statusMap = {
        'ONLINE': { class: 'success', text: '在线' },
        'OFFLINE': { class: 'danger', text: '离线' },
        'MAINTENANCE': { class: 'warning', text: '维护中' },
        'ERROR': { class: 'danger', text: '故障' }
    };

    const status = statusMap[value] || { class: 'secondary', text: value };
    return `<span class="badge bg-${status.class}">${status.text}</span>`;
}

// 格式化日期时间
function formatDateTime(value) {
    if (!value) return '';
    try {
        const date = new Date(value);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return value;
    }
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示/隐藏加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以集成Toast组件或其他通知系统
    console.log('✅ ' + message);
    alert('成功: ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以集成Toast组件或其他通知系统
    console.error('❌ ' + message);
    alert('错误: ' + message);
}

// 绑定事件
function bindEvents() {
    // 这里可以添加其他事件绑定
    console.log('✅ 事件绑定完成');
}

// 刷新数据
function refreshData() {
    currentPage = 1;
    loadData();
}

// 更新记录数显示
function updateRecordCount() {
    const recordCount = document.getElementById('recordCount');
    if (recordCount) {
        recordCount.textContent = `${totalRecords} 条记录`;
    }

    // 更新分页信息显示
    const currentPageDataLength = tableData ? tableData.length : 0;
    const startRecord = totalRecords > 0 ? (currentPage - 1) * pageSize + 1 : 0;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);

    // 更新页面范围显示（如果存在相应元素）
    const pageRangeElement = document.getElementById('pageRange');
    if (pageRangeElement) {
        pageRangeElement.textContent = `显示 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录`;
    }

    console.log(`📊 记录统计更新: 总记录数=${totalRecords}, 当前页=${currentPage}/${totalPages}, 当前页数据=${currentPageDataLength}条`);
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    if (totalPages <= 1) return;

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;
    currentPage = page;
    loadData();
}

// 改变每页显示数量
function changePageSize() {
    const pageSizeSelect = document.getElementById('pageSize');
    pageSize = parseInt(pageSizeSelect.value);
    currentPage = 1;
    loadData();
}

// CRUD功能实现
let currentEditingRecord = null;
let currentEditingIndex = -1;

function addRecord() {
    currentEditingRecord = null;
    currentEditingIndex = -1;

    // 设置模态框标题
    document.getElementById('editModalLabel').textContent = '新增记录';

    // 生成表单字段
    generateFormFields();

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

function editRecord(index) {
    if (index < 0 || index >= tableData.length) {
        showError('无效的记录索引');
        return;
    }

    currentEditingRecord = tableData[index];
    currentEditingIndex = index;

    // 设置模态框标题
    document.getElementById('editModalLabel').textContent = '编辑记录';

    // 生成表单字段并填充数据
    generateFormFields(currentEditingRecord);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

function deleteRecord(index) {
    if (index < 0 || index >= tableData.length) {
        showError('无效的记录索引');
        return;
    }

    const record = tableData[index];
    currentEditingRecord = record;
    currentEditingIndex = index;

    // 显示记录信息
    const recordInfo = document.getElementById('deleteRecordInfo');
    recordInfo.innerHTML = `
        <div class="alert alert-warning">
            <strong>即将删除的记录:</strong><br>
            ${formatRecordInfo(record)}
        </div>
    `;

    // 显示删除确认模态框
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function exportData() {
    try {
        showLoading(true);

        // 构建导出URL
        const params = new URLSearchParams({
            format: 'excel'
        });

        // 添加当前筛选条件
        if (advancedFilters.length > 0) {
            params.append('filters', JSON.stringify(advancedFilters));
        }

        // 添加排序条件
        if (sortColumn) {
            params.append('sort_by', sortColumn);
            params.append('sort_order', sortDirection);
        }

        const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

        // 生成带时间戳的文件名
        const now = new Date();
        const timestamp = now.getFullYear() +
                         String(now.getMonth() + 1).padStart(2, '0') +
                         String(now.getDate()).padStart(2, '0') + '_' +
                         String(now.getHours()).padStart(2, '0') +
                         String(now.getMinutes()).padStart(2, '0') +
                         String(now.getSeconds()).padStart(2, '0');

        // 创建下载链接
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `${TABLE_NAME}_${timestamp}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showSuccess('导出请求已发送，请稍候下载');
    } catch (error) {
        console.error('导出失败:', error);
        showError('导出失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}



// 生成表单字段
function generateFormFields(record = null) {
    const formFields = document.getElementById('formFields');
    formFields.innerHTML = '';

    if (!currentColumns.length) {
        formFields.innerHTML = '<div class="alert alert-warning">无法获取字段信息</div>';
        return;
    }

    currentColumns.forEach(column => {
        if (column.hidden || column.primary_key) return; // 跳过隐藏字段和主键

        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3';

        const label = document.createElement('label');
        label.className = 'form-label';
        label.textContent = column.display_name || column.name;
        if (column.nullable === false) {
            label.innerHTML += ' <span class="text-danger">*</span>';
        }

        let input;
        const value = record ? (record[column.name] || '') : '';

        // 根据字段类型创建不同的输入控件
        switch (column.type) {
            case 'datetime':
                input = document.createElement('input');
                input.type = 'datetime-local';
                input.className = 'form-control';
                if (value) {
                    try {
                        const date = new Date(value);
                        input.value = date.toISOString().slice(0, 16);
                    } catch (e) {
                        input.value = '';
                    }
                }
                break;
            case 'status_badge':
                input = document.createElement('select');
                input.className = 'form-select';
                const statusOptions = ['ONLINE', 'OFFLINE', 'MAINTENANCE', 'ERROR'];
                statusOptions.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status;
                    option.textContent = status;
                    if (value === status) option.selected = true;
                    input.appendChild(option);
                });
                break;
            default:
                input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control';
                input.value = value;
        }

        input.name = column.name;
        input.required = !column.nullable;

        fieldDiv.appendChild(label);
        fieldDiv.appendChild(input);
        formFields.appendChild(fieldDiv);
    });
}

// 保存记录
function saveRecord() {
    try {
        const form = document.getElementById('editForm');
        const formData = new FormData(form);
        const data = {};

        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 验证必填字段
        const missingFields = [];
        currentColumns.forEach(column => {
            if (!column.nullable && !column.hidden && !column.primary_key) {
                if (!data[column.name] || data[column.name].trim() === '') {
                    missingFields.push(column.display_name || column.name);
                }
            }
        });

        if (missingFields.length > 0) {
            showError(`请填写必填字段: ${missingFields.join(', ')}`);
            return;
        }

        showLoading(true);

        let url, method;
        if (currentEditingRecord) {
            // 编辑模式 - 使用正确的RESTful URL
            const recordId = currentEditingRecord.id;
            url = `${API_BASE}/tables/${TABLE_NAME}/data/${recordId}`;
            method = 'PUT';
        } else {
            // 新增模式
            url = `${API_BASE}/tables/${TABLE_NAME}/data`;
            method = 'POST';
        }

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess(currentEditingRecord ? '记录更新成功' : '记录创建成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                modal.hide();

                // 刷新数据
                loadData();
            } else {
                showError(result.error || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            showError('保存失败: ' + error.message);
        })
        .finally(() => {
            showLoading(false);
        });

    } catch (error) {
        console.error('保存记录失败:', error);
        showError('保存记录失败: ' + error.message);
        showLoading(false);
    }
}

// 确认删除
function confirmDelete() {
    if (!currentEditingRecord) {
        showError('没有选择要删除的记录');
        return;
    }

    showLoading(true);

    const recordId = currentEditingRecord.id;
    fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${recordId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess('记录删除成功');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();

            // 刷新数据
            loadData();
        } else {
            showError(result.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showError('删除失败: ' + error.message);
    })
    .finally(() => {
        showLoading(false);
    });
}

// 格式化记录信息用于显示
function formatRecordInfo(record) {
    const displayFields = currentColumns.filter(col => !col.hidden).slice(0, 3);
    return displayFields.map(col => {
        const value = record[col.name] || '';
        return `<strong>${col.display_name || col.name}:</strong> ${value}`;
    }).join('<br>');
}

// 筛选功能实现
function addFilterCondition() {
    filterConditionIndex++;
    const filterConditions = document.getElementById('filterConditions');

    const newFilterRow = document.createElement('div');
    newFilterRow.className = 'filter-row';
    newFilterRow.setAttribute('data-index', filterConditionIndex);

    newFilterRow.innerHTML = `
        <div class="filter-field">
            <label class="form-label form-label-sm">字段</label>
            <select class="form-select form-select-sm" name="field">
                <option value="">请选择字段</option>
                ${currentColumns.filter(col => !col.hidden).map(col =>
                    `<option value="${col.name}">${col.display_name || col.name}</option>`
                ).join('')}
            </select>
        </div>
        <div class="filter-operator">
            <label class="form-label form-label-sm">操作符</label>
            <select class="form-select form-select-sm" name="operator">
                <option value="contains">包含</option>
                <option value="equals">等于</option>
                <option value="starts_with">开始于</option>
                <option value="ends_with">结束于</option>
                <option value="not_equals">不等于</option>
            </select>
        </div>
        <div class="filter-value">
            <label class="form-label form-label-sm">值</label>
            <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
        </div>
        <div class="filter-actions">
            <label class="form-label form-label-sm">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(${filterConditionIndex})" title="删除条件">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
    `;

    filterConditions.appendChild(newFilterRow);
}

function removeFilterCondition(index) {
    const filterRow = document.querySelector(`[data-index="${index}"]`);
    if (filterRow) {
        filterRow.remove();
    }
}

function applyFilter() {
    const filterRows = document.querySelectorAll('.filter-row');
    advancedFilters = [];

    filterRows.forEach(row => {
        const field = row.querySelector('select[name="field"]').value;
        const operator = row.querySelector('select[name="operator"]').value;
        const value = row.querySelector('input[name="value"]').value;

        if (field && value) {
            advancedFilters.push({
                field: field,
                operator: operator,
                value: value
            });
        }
    });

    console.log('应用筛选条件:', advancedFilters);
    currentPage = 1; // 重置到第一页
    loadData();
}

function clearFilter() {
    advancedFilters = [];

    // 清空筛选表单
    const filterRows = document.querySelectorAll('.filter-row');
    filterRows.forEach((row, index) => {
        if (index > 0) { // 保留第一行
            row.remove();
        } else {
            // 重置第一行
            row.querySelector('select[name="field"]').value = '';
            row.querySelector('select[name="operator"]').value = 'contains';
            row.querySelector('input[name="value"]').value = '';
        }
    });

    currentPage = 1;
    loadData();
}

function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    // 更新表头排序图标
    updateSortIcons(column, sortDirection);

    loadData();
}

// 更新排序图标
function updateSortIcons(activeColumn, direction) {
    // 重置所有排序图标
    document.querySelectorAll('.sort-icons i').forEach(icon => {
        icon.className = 'fas fa-sort text-muted';
    });

    // 设置当前排序列的图标
    const activeIcon = document.querySelector(`[onclick="sortTable('${activeColumn}')"] i`);
    if (activeIcon) {
        activeIcon.className = direction === 'asc' ? 'fas fa-sort-up text-primary' : 'fas fa-sort-down text-primary';
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const rowSelects = document.querySelectorAll('.row-select');

    rowSelects.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBatchOperations();
}

function updateBatchOperations() {
    const selectedRows = document.querySelectorAll('.row-select:checked');
    const batchOperations = document.getElementById('batchOperations');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedRows.length > 0) {
        batchOperations.style.display = 'block';
        selectedCount.textContent = selectedRows.length;
    } else {
        batchOperations.style.display = 'none';
    }
}

function batchDelete() {
    const selectedRows = document.querySelectorAll('.row-select:checked');
    if (selectedRows.length === 0) {
        showError('请先选择要删除的记录');
        return;
    }

    // 更新批量删除模态框中的数量
    document.getElementById('batchDeleteCount').textContent = selectedRows.length;

    // 显示批量删除确认模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
    modal.show();
}

function confirmBatchDelete() {
    const selectedRows = document.querySelectorAll('.row-select:checked');
    if (selectedRows.length === 0) {
        showError('没有选择要删除的记录');
        return;
    }

    // 收集要删除的记录ID
    const idsToDelete = [];
    selectedRows.forEach(checkbox => {
        const rowIndex = parseInt(checkbox.value);
        if (rowIndex >= 0 && rowIndex < tableData.length) {
            const record = tableData[rowIndex];
            if (record.id) {
                idsToDelete.push(record.id);
            }
        }
    });

    if (idsToDelete.length === 0) {
        showError('无法获取要删除的记录ID');
        return;
    }

    showLoading(true);

    // 发送批量删除请求
    fetch(`${API_BASE}/tables/${TABLE_NAME}/data/batch`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin',
        body: JSON.stringify({ ids: idsToDelete })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showSuccess(`成功删除 ${idsToDelete.length} 条记录`);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal'));
            modal.hide();

            // 清除选择
            clearSelection();

            // 刷新数据
            loadData();
        } else {
            showError(result.error || '批量删除失败');
        }
    })
    .catch(error => {
        console.error('批量删除失败:', error);
        showError('批量删除失败: ' + error.message);
    })
    .finally(() => {
        showLoading(false);
    });
}

function clearSelection() {
    document.querySelectorAll('.row-select').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    updateBatchOperations();
}
</script>
{% endblock %}
