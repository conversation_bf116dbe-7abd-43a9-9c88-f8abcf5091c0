{% extends "resources/base_resource.html" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% set page_title = page_title %}
{% set page_description = page_description %}
{% set table_title = page_title %}
{% set table_name = table_name %}
{% set use_api_v3 = true %}

{% block extra_css %}
{{ super() }}
<style>
/* API v3通用页面专用样式 - 符合项目红色主题 */
:root {
    --theme-color: #b72424;
    --theme-light: #d21919;
    --theme-dark: #8b1c1c;
}

/* 页面头部信息框 */
.info-box {
    background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-dark) 100%);
    color: white;
    border: none;
    margin-bottom: 20px;
}

.info-box h6 {
    color: white;
    margin-bottom: 8px;
}

.info-box .badge {
    background-color: rgba(255,255,255,0.2);
    color: white;
}

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}

.stats-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    transition: box-shadow 0.15s ease;
}

.stats-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    color: #6c757d;
    font-size: 0.875rem;
}

/* 操作工具栏 */
.toolbar-container {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

/* 筛选器样式 */
.filter-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.filter-container.show {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.filter-badge {
    background: var(--theme-color);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.filter-badge:hover {
    background: var(--theme-dark);
    transform: translateY(-1px);
}

.filter-badge .remove-filter {
    cursor: pointer;
    opacity: 0.8;
    font-size: 0.8rem;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    transition: all 0.2s ease;
}

.filter-badge .remove-filter:hover {
    opacity: 1;
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

/* 筛选字段样式增强 */
.filter-container .form-label {
    font-weight: 600;
    color: var(--theme-color);
    margin-bottom: 4px;
}

.filter-container .form-select:focus,
.filter-container .form-control:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
}

/* 活动筛选器区域 */
#activeFilters {
    min-height: 40px;
    padding: 10px;
    background: white;
    border: 1px dashed #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

#activeFilters:empty::before {
    content: "暂无筛选条件";
    color: #6c757d;
    font-style: italic;
    font-size: 0.875rem;
}

#activeFilters:not(:empty) {
    border-style: solid;
    border-color: var(--theme-color);
    background: rgba(183, 36, 36, 0.05);
}

/* 按钮样式统一 */
.btn-primary {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

.btn-primary:hover {
    background-color: var(--theme-dark);
    border-color: var(--theme-dark);
}

/* 专业数据样式 */
.badge-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
}

.status-active { background-color: #28a745; color: #fff; }
.status-inactive { background-color: #dc3545; color: #fff; }
.status-maintenance { background-color: #ffc107; color: #000; }
.status-unknown { background-color: #6c757d; color: #fff; }

.priority-high { background-color: #dc3545; color: #fff; }
.priority-medium { background-color: #ffc107; color: #000; }
.priority-low { background-color: #28a745; color: #fff; }

.uph-high { background-color: #28a745; color: #fff; }
.uph-medium { background-color: #ffc107; color: #000; }
.uph-low { background-color: #dc3545; color: #fff; }

/* 表格排序指示器 */
.sort-header {
    cursor: pointer;
    user-select: none;
}

.sort-header:hover {
    background-color: #f8f9fa;
}

.sort-indicator {
    margin-left: 5px;
    opacity: 0.5;
}

.sort-active {
    opacity: 1;
    color: var(--theme-color);
}

/* 加载覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* 分页容器 */
.pagination-container {
    background: white;
    padding: 15px;
    border-top: 1px solid #dee2e6;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
    /* 移动端工具栏调整 */
    .toolbar-container .row {
        flex-direction: column;
        gap: 10px;
    }

    .toolbar-container .col-md-4,
    .toolbar-container .col-md-2,
    .toolbar-container .col-md-6 {
        width: 100%;
        max-width: 100%;
    }

    /* 移动端按钮组调整 */
    .toolbar-container .d-flex {
        flex-wrap: wrap;
        gap: 5px;
    }

    .toolbar-container .btn-group {
        flex-wrap: wrap;
    }

    /* 移动端统计卡片调整 */
    .stats-cards .col-md-3 {
        width: 50%;
        max-width: 50%;
        margin-bottom: 10px;
    }

    /* 移动端筛选器调整 */
    .filter-container .row {
        flex-direction: column;
        gap: 10px;
    }

    .filter-container .col-md-3,
    .filter-container .col-md-2,
    .filter-container .col-md-4 {
        width: 100%;
        max-width: 100%;
    }

    /* 移动端表格调整 */
    .table-responsive {
        font-size: 0.8rem;
    }

    .action-column {
        width: 120px;
        min-width: 120px;
    }

    .action-column .btn {
        padding: 2px 4px;
        margin: 1px;
    }

    /* 移动端分页调整 */
    .pagination-container .row {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .pagination-container .col-md-6 {
        width: 100%;
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    /* 超小屏幕优化 */
    .stats-cards .col-md-3 {
        width: 100%;
        max-width: 100%;
    }

    .toolbar-container {
        padding: 10px;
    }

    .filter-container {
        padding: 10px;
    }

    .info-box {
        padding: 10px;
    }

    .info-box .row {
        flex-direction: column;
        text-align: center;
    }

    /* 超小屏幕表格优化 */
    .table th,
    .table td {
        padding: 2px 4px;
        font-size: 0.75rem;
    }

    .action-column {
        width: 100px;
        min-width: 100px;
    }

    .action-column .btn {
        padding: 1px 3px;
        font-size: 0.7rem;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    .toolbar-container .col-md-6 .d-flex {
        flex-wrap: wrap;
        gap: 5px;
    }

    .stats-cards .col-md-3 {
        width: 50%;
        max-width: 50%;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- 页面信息框 -->
<div class="info-box">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6><i class="{{ page_icon }} me-2"></i>{{ page_title }}</h6>
            <p class="mb-0">{{ page_description }}</p>
        </div>
        <div class="col-md-4 text-end">
            <span class="badge">API v3</span>
            <span class="badge ms-2">实时数据</span>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row stats-cards" id="statsCards">
    <!-- 统计卡片将通过JavaScript动态生成 -->
</div>

<!-- 操作工具栏 -->
<div class="toolbar-container">
    <div class="row align-items-center">
        <div class="col-md-4">
            <div class="input-group input-group-sm">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control form-control-sm" id="globalSearch"
                       placeholder="全局搜索..." onkeypress="handleSearchKeyPress(event)">
                <button class="btn btn-primary btn-sm" onclick="performSearch()">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="perPage" onchange="handlePerPageChange()">
                <option value="25">25条/页</option>
                <option value="50" selected>50条/页</option>
                <option value="100">100条/页</option>
                <option value="200">200条/页</option>
            </select>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-end gap-2">
                <button class="btn btn-outline-secondary btn-sm" onclick="refreshData()" title="刷新数据">
                    <i class="fas fa-sync me-1"></i>刷新
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="showAdvancedFilters()" title="高级筛选">
                    <i class="fas fa-filter me-1"></i>高级筛选
                </button>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-success" onclick="exportData('excel')" title="导出Excel">
                        <i class="fas fa-file-excel me-1"></i>Excel
                    </button>
                    <button class="btn btn-outline-info" onclick="exportData('csv')" title="导出CSV">
                        <i class="fas fa-file-csv me-1"></i>CSV
                    </button>
                </div>
                <button class="btn btn-primary btn-sm" onclick="addNewRecord()" title="新增记录">
                    <i class="fas fa-plus me-1"></i>新增
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 高级筛选器 -->
<div id="advancedFilters" class="filter-container" style="display: none;">
    <div class="row">
        <div class="col-md-12">
            <h6><i class="fas fa-filter me-2"></i>高级筛选</h6>
            <div id="activeFilters" class="mb-3">
                <small class="text-muted">暂无筛选条件</small>
            </div>
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label small">字段</label>
                    <select class="form-select form-select-sm" id="filterField">
                        <option value="">选择字段</option>
                        <!-- 字段选项将通过JavaScript动态生成 -->
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">条件</label>
                    <select class="form-select form-select-sm" id="filterOperator">
                        <option value="contains">包含</option>
                        <option value="equals">等于</option>
                        <option value="starts_with">开始于</option>
                        <option value="ends_with">结束于</option>
                        <option value="greater_than">大于</option>
                        <option value="less_than">小于</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small">值</label>
                    <input type="text" class="form-control form-control-sm" id="filterValue"
                           placeholder="筛选值" onkeypress="handleFilterKeyPress(event)">
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button class="btn btn-primary btn-sm" onclick="addFilter()" title="添加筛选条件">
                                <i class="fas fa-search me-1"></i>应用筛选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="clearAllFilters()" title="清空所有筛选">
                                <i class="fas fa-times me-1"></i>清除筛选
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据表格 -->
<div class="position-relative">
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2">正在加载数据...</div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm table-hover" id="dataTable">
            <thead id="tableHeaders">
                <!-- 表头将通过JavaScript动态生成 -->
            </thead>
            <tbody id="tableBody">
                <tr>
                    <td colspan="100%" class="text-center py-4">
                        <i class="fas fa-spinner fa-spin me-2"></i>正在加载数据...
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 分页导航 -->
    <div class="pagination-container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div id="dataInfo" class="text-muted small">
                    显示第 1-50 条，共 0 条记录
                </div>
            </div>
            <div class="col-md-6">
                <nav>
                    <ul class="pagination pagination-sm justify-content-end mb-0" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
// 全局变量
const API_BASE = '/api/v3';
const TABLE_NAME = '{{ table_name }}';
const PAGE_CONFIG = {
    title: '{{ page_title }}',
    icon: '{{ page_icon }}',
    description: '{{ page_description }}',
    table_config: {{ table_config | tojson | safe }}
};

let currentData = [];
let currentColumns = [];
let currentPage = 1;
let totalPages = 1;
let currentSort = { field: '', order: 'asc' };
let currentFilters = [];
let currentSearch = '';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(`🚀 ${PAGE_CONFIG.title} v3页面加载完成`);
    initializePage();
});

// 初始化页面
async function initializePage() {
    try {
        showLoading(true);
        await loadTableStructure();
        await loadData();
        bindEvents();
        console.log('✅ 页面初始化完成');
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        showError('页面初始化失败: ' + error.message);

        // 显示错误状态
        document.getElementById('tableBody').innerHTML = `
            <tr>
                <td colspan="100%" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>数据加载失败</div>
                    <div class="small">${error.message}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="initializePage()">
                        <i class="fas fa-redo me-1"></i>重试
                    </button>
                </td>
            </tr>
        `;
    } finally {
        showLoading(false);
    }
}

// 加载表结构
async function loadTableStructure() {
    try {
        console.log(`📋 正在加载表结构: ${TABLE_NAME}`);
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/structure`, {
            method: 'GET',
            credentials: 'same-origin', // 包含认证信息
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('用户未登录或会话已过期，请重新登录');
            } else if (response.status === 403) {
                throw new Error('没有权限访问此资源');
            } else if (response.status === 404) {
                throw new Error(`表 ${TABLE_NAME} 不存在`);
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        }

        const result = await response.json();

        if (result.success) {
            currentColumns = result.columns || [];
            console.log(`✅ 表结构加载成功，字段数: ${currentColumns.length}`);
            setupFilterFields();
        } else {
            throw new Error(result.error || '获取表结构失败');
        }
    } catch (error) {
        console.error('❌ 加载表结构失败:', error);
        throw error;
    }
}

// 设置筛选字段选项
function setupFilterFields() {
    const filterField = document.getElementById('filterField');
    if (!filterField) return;

    filterField.innerHTML = '<option value="">选择字段</option>';

    currentColumns.forEach(column => {
        const option = document.createElement('option');
        option.value = column.name;
        option.textContent = column.display_name || column.name;
        filterField.appendChild(option);
    });

    console.log(`✅ 筛选字段设置完成，可用字段: ${currentColumns.length}个`);
}

// 加载数据
async function loadData() {
    showLoading(true);

    try {
        console.log(`📊 正在加载数据: ${TABLE_NAME}, 页码: ${currentPage}`);

        const perPageElement = document.getElementById('perPage');
        const perPageValue = perPageElement ? perPageElement.value : '50';

        const params = new URLSearchParams({
            page: currentPage,
            per_page: perPageValue
        });

        // 添加搜索条件
        if (currentSearch) {
            params.append('search', currentSearch);
            console.log(`🔍 搜索条件: ${currentSearch}`);
        }

        // 添加排序条件
        if (currentSort.field) {
            params.append('sort_by', currentSort.field);
            params.append('sort_order', currentSort.order);
            console.log(`📈 排序条件: ${currentSort.field} ${currentSort.order}`);
        }

        // 添加筛选条件
        if (currentFilters.length > 0) {
            params.append('filters', JSON.stringify(currentFilters));
            console.log(`🔧 筛选条件: ${currentFilters.length}个`);
        }

        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('用户未登录或会话已过期');
            } else if (response.status === 403) {
                throw new Error('没有权限访问此数据');
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        }

        const result = await response.json();

        if (result.success) {
            currentData = result.data || [];
            totalPages = result.pages || 1;

            console.log(`✅ 数据加载成功: ${currentData.length}条记录, 共${totalPages}页`);

            renderTable(currentData);
            renderPagination(result);
            updateStats(currentData);
            updateDataInfo(result);
        } else {
            throw new Error(result.error || '数据加载失败');
        }

    } catch (error) {
        console.error('❌ 数据加载失败:', error);
        showError('数据加载失败: ' + error.message);

        // 显示错误状态
        const tbody = document.getElementById('tableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="100%" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <div>数据加载失败</div>
                        <div class="small">${error.message}</div>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadData()">
                            <i class="fas fa-redo me-1"></i>重试
                        </button>
                    </td>
                </tr>
            `;
        }
    } finally {
        showLoading(false);
    }
}

// 渲染表格
function renderTable(data) {
    const thead = document.getElementById('tableHeaders');
    const tbody = document.getElementById('tableBody');

    if (!thead || !tbody) {
        console.error('❌ 表格元素未找到');
        return;
    }

    // 渲染表头
    if (currentColumns.length > 0) {
        thead.innerHTML = `
            <tr>
                ${currentColumns.map(column => `
                    <th class="sort-header" onclick="sortBy('${column.name}')" title="点击排序">
                        ${column.display_name || column.name}
                        <i class="fas fa-sort sort-indicator ms-1"></i>
                    </th>
                `).join('')}
                <th class="action-column">操作</th>
            </tr>
        `;
        console.log(`✅ 表头渲染完成: ${currentColumns.length}列`);
    } else {
        console.warn('⚠️ 没有可用的列信息');
        thead.innerHTML = '<tr><th>暂无列信息</th></tr>';
    }

    // 渲染表格数据
    if (!data || data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="${currentColumns.length + 1}" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无数据</div>
                    <div class="small text-muted">请检查筛选条件或联系管理员</div>
                </td>
            </tr>
        `;
        console.log('📭 表格显示无数据状态');
        return;
    }

    try {
        tbody.innerHTML = data.map((row, index) => {
            // 获取主键值，优先使用id，然后尝试其他可能的主键字段
            const primaryKey = row.id || row.ID || row.pk || index;

            return `
                <tr>
                    ${currentColumns.map(column => `
                        <td title="${formatCellValue(row[column.name], column, true)}">${formatCellValue(row[column.name], column)}</td>
                    `).join('')}
                    <td class="action-column">
                        <button class="btn btn-sm btn-outline-success me-1" onclick="toggleInlineEdit('${primaryKey}')" title="行内编辑" id="inlineEdit_${primaryKey}">
                            <i class="fas fa-pen"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editRecord('${primaryKey}')" title="弹窗编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info me-1" onclick="viewDetails('${primaryKey}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord('${primaryKey}')" title="删除记录">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        console.log(`✅ 表格数据渲染完成: ${data.length}行`);

        // 更新排序指示器
        updateSortIndicators();

    } catch (error) {
        console.error('❌ 表格渲染失败:', error);
        tbody.innerHTML = `
            <tr>
                <td colspan="${currentColumns.length + 1}" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>表格渲染失败</div>
                    <div class="small">${error.message}</div>
                </td>
            </tr>
        `;
    }
}

// 格式化单元格值 - 使用配置驱动
function formatCellValue(value, column, rawValue = false) {
    if (!value && value !== 0) return '';

    // 如果需要原始值（用于tooltip），直接返回
    if (rawValue) {
        return String(value);
    }

    // 获取字段配置
    const fieldConfig = PAGE_CONFIG.table_config.field_types || {};
    const fieldType = fieldConfig[column.name] || 'text';

    // 根据配置的字段类型格式化
    switch (fieldType) {
        case 'business_key':
            return `<strong>${value}</strong>`;
        case 'status_badge':
            return formatStatusBadge(value, column.name);
        case 'priority_badge':
            return formatPriorityBadge(value, column.name);
        case 'datetime':
            return formatDateTime(value);
        case 'date':
            return formatDate(value);
        case 'number':
            return formatNumber(value);
        case 'percentage':
            return formatPercentage(value);
        case 'duration':
            return formatDuration(value);
        case 'version':
            return formatVersion(value);
        default:
            // 智能判断（向后兼容）
            return formatCellValueLegacy(value, column);
    }
}

// 向后兼容的格式化函数
function formatCellValueLegacy(value, column) {
    const fieldName = column.name.toLowerCase();

    // 状态字段
    if (fieldName.includes('status') || fieldName.includes('state')) {
        return formatStatus(value);
    }

    // 优先级字段
    if (fieldName.includes('priority')) {
        return formatPriority(value);
    }

    // UPH字段
    if (fieldName.includes('uph')) {
        return formatUph(value);
    }

    // 时间字段
    if (fieldName.includes('time') || fieldName.includes('date')) {
        if (fieldName.includes('time')) {
            return formatDateTime(value);
        } else {
            return formatDate(value);
        }
    }

    // 数值字段
    if (column.type === 'number' || typeof value === 'number') {
        return formatNumber(value);
    }

    // 默认文本处理
    const strValue = String(value);
    if (strValue.length > 50) {
        return strValue.substring(0, 47) + '...';
    }
    return strValue;
}

// 格式化状态
function formatStatus(status) {
    if (!status) return '';
    const statusClass = getStatusClass(status);
    return `<span class="badge-status ${statusClass}">${status}</span>`;
}

// 格式化优先级
function formatPriority(priority) {
    if (!priority) return '';
    const priorityClass = getPriorityClass(priority);
    return `<span class="badge-status ${priorityClass}">${priority}</span>`;
}

// 格式化UPH
function formatUph(uph) {
    if (!uph) return '';
    const uphClass = getUphClass(parseFloat(uph));
    return `<span class="badge-status ${uphClass}">${uph}</span>`;
}

// 获取状态样式类
function getStatusClass(status) {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('active') || statusLower.includes('运行')) return 'status-active';
    if (statusLower.includes('inactive') || statusLower.includes('停机')) return 'status-inactive';
    if (statusLower.includes('maintenance') || statusLower.includes('维护')) return 'status-maintenance';
    return 'status-unknown';
}

// 获取优先级样式类
function getPriorityClass(priority) {
    const priorityLower = priority.toLowerCase();
    if (priorityLower.includes('high') || priorityLower.includes('高')) return 'priority-high';
    if (priorityLower.includes('medium') || priorityLower.includes('中')) return 'priority-medium';
    if (priorityLower.includes('low') || priorityLower.includes('低')) return 'priority-low';
    return 'priority-medium';
}

// 获取UPH样式类
function getUphClass(uphValue) {
    if (uphValue >= 1000) return 'uph-high';
    if (uphValue >= 500) return 'uph-medium';
    return 'uph-low';
}

// 渲染分页
function renderPagination(result) {
    const pagination = document.getElementById('pagination');
    const totalPages = result.pages;
    const currentPageNum = currentPage;

    let html = '';

    // 上一页
    html += `
        <li class="page-item ${currentPageNum <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToPage(${currentPageNum - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // 页码
    const startPage = Math.max(1, currentPageNum - 2);
    const endPage = Math.min(totalPages, currentPageNum + 2);

    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages})">${totalPages}</a></li>`;
    }

    // 下一页
    html += `
        <li class="page-item ${currentPageNum >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToPage(${currentPageNum + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    pagination.innerHTML = html;
}

// 更新统计信息
function updateStats(data) {
    const statsContainer = document.getElementById('statsCards');

    if (!statsContainer) {
        console.warn('⚠️ 统计卡片容器未找到');
        return;
    }

    if (!data || data.length === 0) {
        statsContainer.innerHTML = `
            <div class="col-md-12">
                <div class="stats-card">
                    <div class="stats-number text-muted">0</div>
                    <div class="stats-label">暂无数据</div>
                </div>
            </div>
        `;
        return;
    }

    console.log(`📊 正在计算统计信息: ${data.length}条记录`);

    // 基础统计
    const totalRecords = data.length;
    const stats = {
        total: totalRecords,
        category1: 0,
        category2: 0,
        category3: 0,
        avg_value: 0,
        labels: getStatsLabels()
    };

    // 智能分析数据字段
    const sampleRow = data[0] || {};
    const fields = Object.keys(sampleRow);

    // 查找状态字段
    const statusField = fields.find(field =>
        field.toLowerCase().includes('status') ||
        field.toLowerCase().includes('state') ||
        field.toLowerCase().includes('eqp_status')
    );

    // 查找优先级字段
    const priorityField = fields.find(field =>
        field.toLowerCase().includes('priority')
    );

    // 查找数值字段
    const valueField = fields.find(field =>
        field.toLowerCase().includes('uph') ||
        field.toLowerCase().includes('yield') ||
        field.toLowerCase().includes('rate') ||
        field.toLowerCase().includes('time')
    );

    let totalValue = 0;
    let valueCount = 0;

    // 根据实际数据计算统计
    data.forEach(row => {
        // 状态统计
        if (statusField) {
            const status = String(row[statusField] || '').toLowerCase();
            if (status.includes('active') || status.includes('运行') || status.includes('online')) {
                stats.category1++;
            } else if (status.includes('inactive') || status.includes('停机') || status.includes('offline')) {
                stats.category2++;
            } else if (status.includes('maintenance') || status.includes('维护')) {
                stats.category3++;
            }
        }

        // 优先级统计
        if (priorityField) {
            const priority = String(row[priorityField] || '').toLowerCase();
            if (priority.includes('high') || priority.includes('高')) {
                stats.category1++;
            } else if (priority.includes('medium') || priority.includes('中')) {
                stats.category2++;
            } else if (priority.includes('low') || priority.includes('低')) {
                stats.category3++;
            }
        }

        // 数值统计
        if (valueField) {
            const value = parseFloat(row[valueField]);
            if (!isNaN(value) && value > 0) {
                totalValue += value;
                valueCount++;
            }
        }
    });

    // 计算平均值
    if (valueCount > 0) {
        stats.avg_value = Math.round(totalValue / valueCount);
    }

    console.log(`✅ 统计计算完成:`, stats);

    // 渲染统计卡片
    statsContainer.innerHTML = `
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-primary">${totalRecords}</div>
                <div class="stats-label">${stats.labels.total}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-success">${stats.category1}</div>
                <div class="stats-label">${stats.labels.category1}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-warning">${stats.category2}</div>
                <div class="stats-label">${stats.labels.category2}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-info">${stats.avg_value}</div>
                <div class="stats-label">${stats.labels.avg_value}</div>
            </div>
        </div>
    `;
}

// 根据表类型获取统计标签
function getStatsLabels() {
    const tableSpecificLabels = {
        'eqp_status': {
            total: '设备总数',
            category1: '运行设备',
            category2: '停机设备',
            avg_value: '平均运行时间'
        },
        'et_uph_eqp': {
            total: '设备总数',
            category1: '高效设备',
            category2: '低效设备',
            avg_value: '平均UPH'
        },
        'et_ft_test_spec': {
            total: '规格总数',
            category1: '激活规格',
            category2: '停用规格',
            avg_value: '平均版本'
        },
        'ct': {
            total: '产品总数',
            category1: '快速周期',
            category2: '慢速周期',
            avg_value: '平均周期时间'
        },
        'wip_lot': {
            total: '批次总数',
            category1: '进行中',
            category2: '等待中',
            avg_value: '平均数量'
        }
    };

    return tableSpecificLabels[TABLE_NAME] || {
        total: '记录总数',
        category1: '类别1',
        category2: '类别2',
        avg_value: '平均值'
    };
}

// 更新数据信息
function updateDataInfo(result) {
    const start = (currentPage - 1) * parseInt(document.getElementById('perPage').value) + 1;
    const end = Math.min(start + result.data.length - 1, result.total);

    document.getElementById('dataInfo').textContent =
        `显示第 ${start}-${end} 条，共 ${result.total} 条记录`;
}

// 绑定事件
function bindEvents() {
    // 每页数量变化
    document.getElementById('perPage').addEventListener('change', function() {
        currentPage = 1;
        loadData();
    });

    // 搜索框回车事件
    document.getElementById('globalSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 筛选值回车事件
    document.getElementById('filterValue').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addFilter();
        }
    });
}

// 执行搜索
function performSearch() {
    currentSearch = document.getElementById('globalSearch').value;
    currentPage = 1;
    loadData();
}

// 显示/隐藏高级筛选
function showAdvancedFilters() {
    const filtersDiv = document.getElementById('advancedFilters');
    const button = event.target.closest('button');
    const isVisible = filtersDiv.style.display !== 'none';

    if (isVisible) {
        filtersDiv.style.display = 'none';
        filtersDiv.classList.remove('show');
        if (button) {
            button.innerHTML = '<i class="fas fa-filter me-1"></i>高级筛选';
            button.classList.remove('btn-primary');
            button.classList.add('btn-outline-primary');
        }
    } else {
        filtersDiv.style.display = 'block';
        filtersDiv.classList.add('show');
        if (button) {
            button.innerHTML = '<i class="fas fa-filter me-1"></i>收起筛选';
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-primary');
        }

        // 聚焦到第一个筛选字段
        setTimeout(() => {
            const firstField = document.getElementById('filterField');
            if (firstField) {
                firstField.focus();
            }
        }, 100);
    }
}

// 添加筛选条件
function addFilter() {
    const field = document.getElementById('filterField').value;
    const operator = document.getElementById('filterOperator').value;
    const value = document.getElementById('filterValue').value;

    if (!field || !value) {
        alert('请选择字段并输入筛选值');
        return;
    }

    // 检查是否已存在相同筛选条件
    const existingFilter = currentFilters.find(f =>
        f.field === field && f.operator === operator && f.value === value
    );

    if (existingFilter) {
        alert('该筛选条件已存在');
        return;
    }

    // 添加筛选条件
    currentFilters.push({ field, operator, value });

    // 清空输入
    document.getElementById('filterField').value = '';
    document.getElementById('filterValue').value = '';

    // 更新显示
    updateActiveFilters();

    // 重新加载数据
    currentPage = 1;
    loadData();
}

// 更新活动筛选器显示
function updateActiveFilters() {
    const container = document.getElementById('activeFilters');

    if (!container) return;

    if (currentFilters.length === 0) {
        container.innerHTML = '';
        container.classList.remove('has-filters');
        return;
    }

    container.classList.add('has-filters');
    container.innerHTML = currentFilters.map((filter, index) => {
        const fieldName = getFieldDisplayName(filter.field);
        const operatorText = getOperatorText(filter.operator);

        return `
            <span class="filter-badge" title="点击删除此筛选条件">
                <span class="filter-content">
                    <strong>${fieldName}</strong> ${operatorText} <em>"${filter.value}"</em>
                </span>
                <span class="remove-filter" onclick="removeFilter(${index})" title="删除">
                    <i class="fas fa-times"></i>
                </span>
            </span>
        `;
    }).join('');
}

// 获取字段显示名称
function getFieldDisplayName(fieldName) {
    const column = currentColumns.find(col => col.name === fieldName);
    return column ? (column.display_name || column.name) : fieldName;
}

// 获取操作符文本
function getOperatorText(operator) {
    const operatorMap = {
        'contains': '包含',
        'equals': '等于',
        'starts_with': '开始于',
        'ends_with': '结束于',
        'greater_than': '大于',
        'less_than': '小于'
    };
    return operatorMap[operator] || operator;
}

// 移除筛选条件
function removeFilter(index) {
    currentFilters.splice(index, 1);
    updateActiveFilters();
    currentPage = 1;
    loadData();
}

// 清空所有筛选条件
function clearAllFilters() {
    currentFilters = [];
    updateActiveFilters();
    currentPage = 1;
    loadData();
}

// 排序
function sortBy(field) {
    if (currentSort.field === field) {
        currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.order = 'asc';
    }

    currentPage = 1;
    loadData();
}

// 更新排序指示器
function updateSortIndicators() {
    document.querySelectorAll('.sort-indicator').forEach(indicator => {
        indicator.className = 'fas fa-sort sort-indicator';
    });

    if (currentSort.field) {
        const header = document.querySelector(`th[onclick*="'${currentSort.field}'"] .sort-indicator`);
        if (header) {
            header.className = `fas fa-sort-${currentSort.order === 'asc' ? 'up' : 'down'} sort-indicator sort-active`;
        }
    }
}

// 跳转页面
function goToPage(page) {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
        currentPage = page;
        loadData();
    }
}

// 刷新数据
function refreshData() {
    loadData();
}

// 导出数据
function exportData(format) {
    const params = new URLSearchParams({
        format: format
    });

    if (currentSearch) {
        params.append('search', currentSearch);
    }

    if (currentSort.field) {
        params.append('sort_by', currentSort.field);
        params.append('sort_order', currentSort.order);
    }

    if (currentFilters.length > 0) {
        params.append('filters', JSON.stringify(currentFilters));
    }

    const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

    const link = document.createElement('a');
    link.href = exportUrl;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 新增记录
function addNewRecord() {
    // 这里可以打开新增记录的模态框
    alert('新增功能开发中...');
}

// 编辑记录
function editRecord(id) {
    const record = currentData.find(item => item.id == id);
    if (!record) return;

    // 这里可以打开编辑记录的模态框
    alert(`编辑记录 ID: ${id}`);
}

// 查看详情
function viewDetails(id) {
    const record = currentData.find(item => item.id == id);
    if (!record) return;

    // 创建详情显示内容
    const detailsHtml = `
        <div class="row">
            ${currentColumns.map(column => `
                <div class="col-md-6 mb-2">
                    <strong>${column.display_name || column.name}:</strong>
                    <span class="ms-2">${formatCellValue(record[column.name], column)}</span>
                </div>
            `).join('')}
        </div>
    `;

    // 显示详情模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${PAGE_CONFIG.title}详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${detailsHtml}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// 删除记录
async function deleteRecord(id) {
    if (!confirm('确定要删除这条记录吗？')) return;

    try {
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('删除成功');
            loadData();
        } else {
            showError('删除失败: ' + result.error);
        }

    } catch (error) {
        showError('请求失败: ' + error.message);
    }
}

// 工具函数
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    try {
        return new Date(dateStr).toLocaleString('zh-CN');
    } catch {
        return dateStr;
    }
}

function formatDate(dateStr) {
    if (!dateStr) return '';
    try {
        return new Date(dateStr).toLocaleDateString('zh-CN');
    } catch {
        return dateStr;
    }
}

function formatNumber(num) {
    if (!num && num !== 0) return '';
    return parseFloat(num).toLocaleString();
}

// 新增格式化函数 - 配置驱动
function formatStatusBadge(status, fieldName) {
    if (!status) return '';

    // 获取状态映射配置
    const statusMapping = PAGE_CONFIG.table_config.status_mapping || {};
    const config = statusMapping[status] || { class: 'secondary', text: status };

    return `<span class="badge bg-${config.class}">${config.text}</span>`;
}

function formatPriorityBadge(priority, fieldName) {
    if (!priority) return '';

    // 获取优先级映射配置
    const priorityMapping = PAGE_CONFIG.table_config.priority_mapping || {};
    const config = priorityMapping[priority] || { class: 'secondary', text: priority };

    return `<span class="badge bg-${config.class}">${config.text}</span>`;
}

function formatPercentage(value) {
    if (!value && value !== 0) return '';
    const num = parseFloat(value);
    return `${num.toFixed(1)}%`;
}

function formatDuration(value) {
    if (!value) return '';
    // 假设值是分钟数
    const minutes = parseInt(value);
    if (minutes < 60) {
        return `${minutes}分钟`;
    } else {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`;
    }
}

function formatVersion(value) {
    if (!value) return '';
    return `<code class="version-badge">${value}</code>`;
}

// 行内编辑切换
function toggleInlineEdit(id) {
    console.log('切换行内编辑:', id);
    showError('行内编辑功能开发中...');
}

function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

function showSuccess(message) {
    // 创建Toast提示
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // 添加到页面
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // 显示Toast
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    } else {
        // 降级到alert
        alert('✅ ' + message);
        toast.remove();
    }
}

function showError(message) {
    // 创建Toast提示
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-exclamation-triangle me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // 添加到页面
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // 显示Toast
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    } else {
        // 降级到alert
        alert('❌ ' + message);
        toast.remove();
    }
}

console.log('✅ 通用资源管理v3 完整功能加载完成');
</script>
{% endblock %}
