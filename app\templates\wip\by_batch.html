{% extends "base.html" %}

{% block head %}
{{ super() }}
<script src="{{ url_for('static', filename='js/base/api-client-v2.js') }}"></script>
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block title %}批次WIP跟踪{% endblock %}

{% block extra_head %}
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
<style>
    .preview-area {
        margin-bottom: 1rem;
        padding: 1.25rem;
        background-color: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.06);
    }
    
    /* 高级筛选面板样式 */
    .advanced-filter-panel {
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    }
    
    .filter-row {
        display: flex;
        align-items: end;
        gap: 10px;
        margin-bottom: 10px;
        padding: 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }
    
    .filter-field {
        flex: 1;
        min-width: 160px;
    }
    
    .filter-operator {
        flex: 0 0 100px;
    }
    
    .filter-value {
        flex: 1;
        min-width: 160px;
    }
    
    .filter-actions {
        flex: 0 0 80px;
        display: flex;
        gap: 3px;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .collapse-toggle {
        cursor: pointer;
        user-select: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 0;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 10px;
    }
    
    .collapse-toggle:hover {
        color: var(--theme-color);
    }
    
    .collapse-icon {
        transition: transform 0.3s ease;
    }
    
    .collapse-icon.rotated {
        transform: rotate(180deg);
    }
    
    .table-responsive {
        max-height: 70vh;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }
    
    .table th {
        white-space: nowrap;
        min-width: 100px;
        position: relative;
        padding-right: 20px;
    }
    
    .table td {
        white-space: nowrap;
    }
    
    .selectable-row {
        cursor: pointer;
    }
    
    .selectable-row.selected {
        background-color: #fff1f0 !important;
    }
    
    .selectable-row:hover {
        background-color: #f8f9fa;
    }
    
    .sort-icon {
        position: absolute;
        right: 5px;
        cursor: pointer;
        color: #999;
    }
    
    .sort-icon.active {
        color: #b72424;
    }
    
    .batch-actions {
        margin-bottom: 10px;
    }
    
    .batch-actions .btn {
        margin-right: 5px;
    }
    
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    .quick-filter-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 10px;
    }
    
    .filter-tag {
        background-color: var(--theme-color);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .filter-tag .remove-filter {
        cursor: pointer;
        margin-left: 5px;
        font-weight: bold;
    }
    
    .filter-tag .remove-filter:hover {
        background-color: rgba(255,255,255,0.2);
        border-radius: 50%;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    
    .info-box {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .info-box h6 {
        color: #1976d2;
        margin-bottom: 8px;
    }
    
    /* 优化小尺寸表单标签 */
    .form-label-sm {
        font-size: 0.8rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    /* 高级筛选标题样式优化 */
    .advanced-filter-panel h6 {
        font-size: 0.95rem;
        margin-bottom: 0;
    }
    
    /* 数据预览区域标题强调 */
    .preview-area h6 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
    }
    
    #loadingIndicator {
        padding: 30px;
        text-align: center;
    }
    .spinner {
        margin: 0 auto;
        width: 70px;
        text-align: center;
    }
    .spinner > div {
        width: 18px;
        height: 18px;
        background-color: #333;
        border-radius: 100%;
        display: inline-block;
        animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    }
    .spinner .bounce1 {
        animation-delay: -0.32s;
    }
    .spinner .bounce2 {
        animation-delay: -0.16s;
    }
    @keyframes sk-bouncedelay {
        0%, 80%, 100% { 
            transform: scale(0);
        } 40% { 
            transform: scale(1.0);
        }
    }
    
    /* 统一的通知样式 */
    .notification-message {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #5cb85c;
        color: white;
        padding: 15px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        z-index: 1050;
        max-width: 400px;
        animation: fadeInOut 5s forwards;
    }
    
    .notification-icon {
        margin-right: 10px;
        font-size: 20px;
    }
    
    .notification-text {
        flex-grow: 1;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 0 5px;
    }
    
    .notification-close:hover {
        opacity: 0.8;
    }
    
    @keyframes fadeInOut {
        0% { opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { opacity: 0; display: none; }
    }
    
    /* 根据排产管理页面的样式，修改按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">批次WIP跟踪</h5>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                        </div>
                    </div>
                    
                    <!-- 添加说明区域 -->
                    <div class="info-box">
                        <h6><i class="fas fa-info-circle me-1"></i>批次WIP跟踪</h6>
                        <p class="mb-0">此界面用于<b>查看和跟踪批次在制品信息</b>。您可以查看批次状态、工序进度和相关详细信息。支持高级筛选功能进行精确查询。</p>
                    </div>
                    
                    <!-- 高级筛选面板 -->
                    <div class="advanced-filter-panel">
                        <div class="collapse-toggle" onclick="toggleAdvancedFilter()">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i>高级筛选
                                <small class="text-muted ms-2">支持多条件组合查询</small>
                            </h6>
                            <i class="fas fa-chevron-down collapse-icon" id="collapseIcon"></i>
                        </div>
                        
                        <div id="advancedFilterContent" class="collapse show">
                            <!-- 当前筛选条件标签 -->
                            <div class="quick-filter-tags" id="filterTags"></div>
                            
                            <!-- 筛选条件列表 -->
                            <div id="filterConditions">
                                <!-- 默认的第一个筛选条件 -->
                                <div class="filter-row" data-index="0">
                                    <div class="filter-field">
                                        <label class="form-label form-label-sm">字段</label>
                                        <select class="form-select form-select-sm" name="field" id="defaultFieldSelect">
                                            <option value="">请选择字段</option>
                                            <!-- 字段选项将通过JavaScript动态生成 -->
                                        </select>
                                    </div>
                                    <div class="filter-operator">
                                        <label class="form-label form-label-sm">操作符</label>
                                        <select class="form-select form-select-sm" name="operator">
                                            <option value="contains">包含</option>
                                            <option value="equals">等于</option>
                                            <option value="starts_with">开始于</option>
                                            <option value="ends_with">结束于</option>
                                            <option value="not_equals">不等于</option>
                                            <option value="is_empty">为空</option>
                                            <option value="is_not_empty">不为空</option>
                                        </select>
                                    </div>
                                    <div class="filter-value">
                                        <label class="form-label form-label-sm">值</label>
                                        <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                    </div>
                                    <div class="filter-actions">
                                        <label class="form-label form-label-sm">&nbsp;</label>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 筛选操作按钮 -->
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="applyAdvancedFilter()">
                                        <i class="fas fa-search me-1"></i>应用筛选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearAllFilters()">
                                        <i class="fas fa-times me-1"></i>清除筛选
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm ms-2" onclick="saveFilterPreset()">
                                        <i class="fas fa-save me-1"></i>保存预设
                                    </button>
                                </div>
                                <div class="d-flex gap-2">
                                    <select class="form-select form-select-sm" id="filterPresets" onchange="loadFilterPreset()" style="width: 180px;">
                                        <option value="">选择筛选预设...</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteFilterPreset()" title="删除选中的预设">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">WIP批次数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="wipTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将通过JavaScript动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加统一的通知容器 -->
<div id="notification-container"></div>
{% endblock %}

{% block extra_js %}
<!-- jQuery -->
<script src="{{ url_for('static', filename='jquery/jquery.min.js') }}">
    // 🚀 推荐使用新的API v2客户端 (支持自动降级)
    // 示例: 
    // const result = await apsAPI.resources.getTableData('tableName', {page: 1, per_page: 50});
    // if (result.success) { console.log(result.data); }
    
    // 原有fetch代码保持不变，确保向后兼容
    </script>
<!-- DataTables -->
<script src="{{ url_for('static', filename='jquery/jquery.min.js') }}"></script>
         <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>

<script>
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let currentFilters = {};
let advancedFilters = [];
let tableData = [];
let filteredData = [];
let currentSortColumn = '';
let currentSortDirection = '';
let filterConditionIndex = 1;
let availableFields = []; // 存储可用的字段列表
const currentUser = '{{ current_user.username }}'; // 当前用户名

// 加载已保存的用户预设
function loadSavedPresets() {
    const select = document.getElementById('filterPresets');
    
    // 通过API加载当前用户的预设，传递页面类型参数
    fetch('/api/user-filter-presets?page_type=wip_lot', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.presets) {
            data.presets.forEach(preset => {
                const option = document.createElement('option');
                option.value = preset.name;
                option.textContent = preset.name;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('加载用户预设失败:', error);
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('WIP批次跟踪页面加载完成，开始初始化数据...');
    loadData();
    initAdvancedFilter();
    loadSavedPresets();
});

// 初始化高级筛选功能
function initAdvancedFilter() {
    // 绑定回车键事件
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.target.matches('.filter-row input[name="value"]')) {
            applyAdvancedFilter();
        }
    });
}

// 切换高级筛选面板
function toggleAdvancedFilter() {
    const content = document.getElementById('advancedFilterContent');
    const icon = document.getElementById('collapseIcon');
    
    if (content.classList.contains('show')) {
        content.classList.remove('show');
        icon.classList.add('rotated');
    } else {
        content.classList.add('show');
        icon.classList.remove('rotated');
    }
}

// 生成字段选择器选项
function generateFieldOptions() {
    let options = '<option value="">请选择字段</option>';
    availableFields.forEach(field => {
        options += `<option value="${field}">${field}</option>`;
    });
    return options;
}

// 更新字段选项
function updateFieldOptions(columns) {
    availableFields = columns || [];
    const fieldOptions = generateFieldOptions();
    
    // 更新所有字段选择器
    document.querySelectorAll('select[name="field"]').forEach(select => {
        const currentValue = select.value;
        select.innerHTML = fieldOptions;
        if (currentValue && availableFields.includes(currentValue)) {
            select.value = currentValue;
        }
    });
}

// 添加筛选条件
function addFilterCondition() {
    const container = document.getElementById('filterConditions');
    const newIndex = filterConditionIndex++;
    
    const newRow = document.createElement('div');
    newRow.className = 'filter-row';
    newRow.setAttribute('data-index', newIndex);
    
    newRow.innerHTML = `
        <div class="filter-field">
            <label class="form-label form-label-sm">字段</label>
            <select class="form-select form-select-sm" name="field">
                ${generateFieldOptions()}
            </select>
        </div>
        <div class="filter-operator">
            <label class="form-label form-label-sm">操作符</label>
            <select class="form-select form-select-sm" name="operator">
                <option value="contains">包含</option>
                <option value="equals">等于</option>
                <option value="starts_with">开始于</option>
                <option value="ends_with">结束于</option>
                <option value="not_equals">不等于</option>
                <option value="is_empty">为空</option>
                <option value="is_not_empty">不为空</option>
            </select>
        </div>
        <div class="filter-value">
            <label class="form-label form-label-sm">值</label>
            <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
        </div>
        <div class="filter-actions">
            <label class="form-label form-label-sm">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(${newIndex})" title="删除条件">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newRow);
}

// 删除筛选条件
function removeFilterCondition(index) {
    const container = document.getElementById('filterConditions');
    const rows = container.querySelectorAll('.filter-row');
    
    if (rows.length <= 1) {
        alert('至少需要保留一个筛选条件');
        return;
    }
    
    const rowToRemove = container.querySelector(`[data-index="${index}"]`);
    if (rowToRemove) {
        rowToRemove.remove();
    }
}

// 应用高级筛选
function applyAdvancedFilter() {
    // 收集筛选条件
    advancedFilters = [];
    const filterRows = document.querySelectorAll('.filter-row');
    
    filterRows.forEach(row => {
        const field = row.querySelector('select[name="field"]').value;
        const operator = row.querySelector('select[name="operator"]').value;
        const value = row.querySelector('input[name="value"]').value;
        
        if (field && operator) {
            advancedFilters.push({ field, operator, value });
        }
    });
    
    // 更新筛选标签显示
    updateFilterTags();
    
    // 重新加载数据
    loadData(1);
}

// 更新筛选标签显示
function updateFilterTags() {
    const container = document.getElementById('filterTags');
    container.innerHTML = '';
    
    advancedFilters.forEach((filter, index) => {
        if (filter.field) {
            const tag = document.createElement('div');
            tag.className = 'filter-tag';
            
            let displayText = `${filter.field} ${getOperatorText(filter.operator)}`;
            if (filter.value && !['is_empty', 'is_not_empty'].includes(filter.operator)) {
                displayText += ` "${filter.value}"`;
            }
            
            tag.innerHTML = `
                ${displayText}
                <span class="remove-filter" onclick="removeFilterTag(${index})" title="删除此筛选条件">×</span>
            `;
            
            container.appendChild(tag);
        }
    });
}

// 获取操作符的中文显示文本
function getOperatorText(operator) {
    const operatorMap = {
        'contains': '包含',
        'equals': '等于',
        'starts_with': '开始于',
        'ends_with': '结束于',
        'not_equals': '不等于',
        'is_empty': '为空',
        'is_not_empty': '不为空'
    };
    return operatorMap[operator] || operator;
}

// 删除筛选标签
function removeFilterTag(index) {
    advancedFilters.splice(index, 1);
    updateFilterTags();
    loadData(1);
}

// 清除所有筛选
function clearAllFilters() {
    // 清空高级筛选
    advancedFilters = [];
    
    // 重置筛选条件表单
    const container = document.getElementById('filterConditions');
    container.innerHTML = '';
    filterConditionIndex = 1;
    
    // 添加默认的第一个筛选条件
    const defaultRow = document.createElement('div');
    defaultRow.className = 'filter-row';
    defaultRow.setAttribute('data-index', '0');
    defaultRow.innerHTML = `
        <div class="filter-field">
            <label class="form-label form-label-sm">字段</label>
            <select class="form-select form-select-sm" name="field" id="defaultFieldSelect">
                ${generateFieldOptions()}
            </select>
        </div>
        <div class="filter-operator">
            <label class="form-label form-label-sm">操作符</label>
            <select class="form-select form-select-sm" name="operator">
                <option value="contains">包含</option>
                <option value="equals">等于</option>
                <option value="starts_with">开始于</option>
                <option value="ends_with">结束于</option>
                <option value="not_equals">不等于</option>
                <option value="is_empty">为空</option>
                <option value="is_not_empty">不为空</option>
            </select>
        </div>
        <div class="filter-value">
            <label class="form-label form-label-sm">值</label>
            <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
        </div>
        <div class="filter-actions">
            <label class="form-label form-label-sm">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(defaultRow);
    
    // 清空筛选标签
    updateFilterTags();
    
    // 重新加载数据
    loadData(1);
}

// 显示或隐藏加载指示器
function showLoading(show = true) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

// 显示通知的函数
function showNotification(message, type = 'success') {
    // 创建通知元素
    const notificationId = 'notification-' + Date.now();
    const notificationHtml = `
        <div class="notification-message" id="${notificationId}" style="background-color: ${type === 'success' ? '#5cb85c' : type === 'info' ? '#5bc0de' : type === 'warning' ? '#f0ad4e' : '#d9534f'}">
            <span class="notification-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'info' ? 'fa-info-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle'}"></i>
            </span>
            <span class="notification-text">${message}</span>
            <button type="button" class="notification-close" onclick="document.getElementById('${notificationId}').remove()">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    // 添加到通知容器
    document.getElementById('notification-container').insertAdjacentHTML('beforeend', notificationHtml);
    
    // 5秒后自动移除
    setTimeout(() => {
        const element = document.getElementById(notificationId);
        if (element) {
            element.style.opacity = '0';
            setTimeout(() => element.remove(), 300);
        }
    }, 5000);
}
        
// 加载数据（修改以支持高级筛选）
function loadData(page = 1) {
    currentPage = page;
    showLoading(true);
    
    // 构建筛选参数
    const params = new URLSearchParams({
        page: page,
        per_page: pageSize
    });
    
    // 使用WIP_LOT数据API
    const apiUrl = '/api/resources/data/wip_lot';
    
    // 添加高级筛选参数
    if (advancedFilters.length > 0) {
        params.append('advanced_filters', JSON.stringify(advancedFilters));
    }
    
    const url = `${apiUrl}?${params}`;
    console.log('正在加载数据，URL:', url);
    
    fetch(url)
        .then(response => {
            console.log('API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('API返回数据:', data);
            if (data.error) {
                throw new Error(data.error);
            }
            
            tableData = data.data || [];
            filteredData = [...tableData];
            
            renderTable(data);
            renderPagination(data);
            updateStats(data);
            updateFieldOptions(data.columns || []);
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            showError('加载数据失败: ' + error.message);
        })
        .finally(() => {
            showLoading(false);
        });
}

// 渲染表格
function renderTable(data) {
    const columns = data.columns || [];
    const rows = data.data || [];
    
    // 渲染表头
    const headerRow = document.getElementById('tableHeaders');
    headerRow.innerHTML = '';
    
    columns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column;
        th.style.cursor = 'pointer';
        th.onclick = () => sortTable(column);
        
        const sortIcon = document.createElement('i');
        sortIcon.className = 'fas fa-sort sort-icon';
        sortIcon.style.marginLeft = '5px';
        th.appendChild(sortIcon);
        
        headerRow.appendChild(th);
    });
    
    // 渲染数据行
    const tableBody = document.getElementById('tableBody');
    tableBody.innerHTML = '';
    
    if (rows.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td colspan="${columns.length}" class="text-center">没有找到匹配的数据</td>`;
        tableBody.appendChild(tr);
        return;
    }
    
    rows.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.className = 'selectable-row';
        
        columns.forEach(column => {
            const td = document.createElement('td');
            const value = row[column];
            
            // 直接显示原始数据，不进行格式化转换
            if (column.includes('DATE') || column.includes('TIME')) {
                td.textContent = formatDateTime(value);
            } else {
                td.textContent = value || '';
            }
            
            tr.appendChild(td);
        });
        
        tableBody.appendChild(tr);
    });
}

// 格式化日期时间
function formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return dateTime;
    }
}

// 渲染分页
function renderPagination(data) {
    totalPages = data.pages || 1;
    const pagination = document.getElementById('pagination');
    
    let paginationHtml = '';
    
    // 上一页
    paginationHtml += `
        <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadData(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadData(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHtml += `
        <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadData(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = paginationHtml;
}

// 更新统计信息
function updateStats(data) {
    document.getElementById('recordCount').textContent = `${data.total || 0} 条记录`;
}

// 改变页面大小
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    loadData(1);
}

// 刷新数据
function refreshData() {
    loadData(currentPage);
}

// 导出数据
function exportData() {
    if (filteredData.length === 0) {
        alert('没有数据可以导出');
        return;
    }
    
    const columns = Array.from(document.querySelectorAll('#tableHeaders th')).map(th => 
        th.textContent.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_]/g, '').replace('_', ' ')
    );
    
    let csvContent = columns.join(',') + '\n';
    
    filteredData.forEach(row => {
        const values = columns.map(col => {
            const key = col.replace(/\s+/g, '_').toUpperCase();
            let value = row[key] || '';
            value = String(value).replace(/<[^>]*>/g, '');
            if (value.includes(',') || value.includes('"')) {
                value = `"${value.replace(/"/g, '""')}"`;
            }
            return value;
        });
        csvContent += values.join(',') + '\n';
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `wip_lot_${new Date().toISOString().slice(0,10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 排序表格
function sortTable(column) {
    // 排序逻辑实现
}

// 显示错误消息
function showError(message) {
    showNotification(message, 'error');
}

// 保存筛选预设
function saveFilterPreset() {
    if (advancedFilters.length === 0) {
        alert('请先设置筛选条件');
        return;
    }
    
    const presetName = prompt('请输入预设名称:');
    if (presetName && presetName.trim()) {
        const trimmedName = presetName.trim();
        
        // 通过API保存预设
        fetch('/api/user-filter-presets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: trimmedName,
                filters: advancedFilters,
                page_type: 'wip_lot'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加到预设选择器（如果不存在的话）
                const select = document.getElementById('filterPresets');
                const existingOption = select.querySelector(`option[value="${trimmedName}"]`);
                if (!existingOption) {
                    const option = document.createElement('option');
                    option.value = trimmedName;
                    option.textContent = trimmedName;
                    select.appendChild(option);
                }
                alert('筛选预设已保存');
            } else {
                if (data.exists) {
                    if (confirm(`预设 "${trimmedName}" 已存在，是否覆盖？`)) {
                        // 覆盖保存
                        fetch('/api/user-filter-presets', {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                name: trimmedName,
                                filters: advancedFilters,
                                page_type: 'wip_lot'
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert('筛选预设已更新');
                            } else {
                                alert('保存预设失败: ' + data.error);
                            }
                        });
                    }
                } else {
                    alert('保存预设失败: ' + data.error);
                }
            }
        })
        .catch(error => {
            console.error('保存预设失败:', error);
            alert('保存预设失败: ' + error.message);
        });
    }
}

// 删除筛选预设
function deleteFilterPreset() {
    const select = document.getElementById('filterPresets');
    const selectedPreset = select.value;
    
    if (!selectedPreset) {
        alert('请先选择要删除的预设');
        return;
    }
    
    if (confirm(`确定要删除预设 "${selectedPreset}" 吗？`)) {
        // 通过API删除预设
        fetch('/api/user-filter-presets', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: selectedPreset,
                page_type: 'wip_lot'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 从下拉列表中删除
                const optionToRemove = select.querySelector(`option[value="${selectedPreset}"]`);
                if (optionToRemove) {
                    optionToRemove.remove();
                }
                
                // 清空选择
                select.value = '';
                
                alert('预设已删除');
            } else {
                alert('删除预设失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('删除预设失败:', error);
            alert('删除预设失败: ' + error.message);
        });
    }
}

// 加载筛选预设  
function loadFilterPreset() {
    const select = document.getElementById('filterPresets');
    const presetName = select.value;
    
    if (!presetName) return;
    
    // 通过API加载用户预设
    fetch(`/api/user-filter-presets/${presetName}?page_type=wip_lot`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.preset) {
            const preset = data.preset.filters;
            
            // 清空现有条件
            const container = document.getElementById('filterConditions');
            container.innerHTML = '';
            filterConditionIndex = 0;
            
            // 添加预设条件
            preset.forEach((filter, index) => {
                addFilterCondition();
                const row = container.children[index];
                row.querySelector('select[name="field"]').value = filter.field;
                row.querySelector('select[name="operator"]').value = filter.operator;
                row.querySelector('input[name="value"]').value = filter.value;
            });
            
            // 应用筛选
            applyAdvancedFilter();
        } else {
            alert('加载预设失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('加载预设失败:', error);
        alert('加载预设失败: ' + error.message);
    });
}

// 页面初始化完成后自动加载数据
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面初始化完成，开始加载WIP数据...');
    loadData();
});

// 智能API调用 - 优先使用API v2，失败时自动降级到API v1
function loadData(page = 1) {
    showLoading(true);
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        per_page: pageSize
    });
    
    // 添加高级筛选参数
    if (advancedFilters.length > 0) {
        params.append('advanced_filters', JSON.stringify(advancedFilters));
    }
    
    // 尝试使用API v2
    if (typeof window.apsAPI !== 'undefined' && window.apsAPI.resources) {
        console.log('🔄 尝试使用API v2加载数据');
        
        window.apsAPI.resources.getTableData('wip_lot', {
            page: page,
            per_page: pageSize,
            filters: advancedFilters.length > 0 ? advancedFilters : []
        })
        .then(response => {
            if (response.success && response.data) {
                // API v2成功响应
                const data = {
                    data: response.data,
                    columns: response.columns || [],
                    total: response.pagination?.total || 0,
                    total_pages: response.pagination?.pages || 1,
                    page: response.pagination?.page || 1,
                    total_records: response.pagination?.total || 0
                };
                
                tableData = data.data || [];
                filteredData = [...tableData];
                
                renderTable(data);
                renderPagination(data);
                updateStats(data);
                updateFieldOptions(data.columns || []);
                
                console.log('✅ 使用API v2成功加载数据:', data.total, '条记录');
            } else {
                throw new Error(response.error || 'API v2响应无效');
            }
        })
        .catch(error => {
            console.warn('API v2失败，降级到API v1:', error);
            fallbackToV1();
        })
        .finally(() => {
            showLoading(false);
        });
    } else {
        console.warn('API v2客户端未加载，使用API v1');
        fallbackToV1();
    }
    
    // API v1降级函数
    function fallbackToV1() {
        const url = `/api/resources/data/wip_lot?${params}`;
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                tableData = data.data || [];
                filteredData = [...tableData];
                
                renderTable(data);
                renderPagination(data);
                updateStats(data);
                updateFieldOptions(data.columns || []);
                
                console.log('📊 使用API v1加载数据');
            })
            .catch(error => {
                console.error('加载数据失败:', error);
                showError('加载数据失败: ' + error.message);
            })
            .finally(() => {
                showLoading(false);
            });
    }
}

// 导出数据 - 优先使用API v2
function exportData() {
    // 显示导出提示
    const exportBtn = document.querySelector('button[onclick="exportData()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
    exportBtn.disabled = true;
    
    // 尝试使用API v2导出
    if (typeof APSResourceAPI !== 'undefined') {
        APSResourceAPI.exportTableData('wip_lot', {
            format: 'excel',
            advanced_filters: advancedFilters.length > 0 ? advancedFilters : undefined
        })
        .then(response => {
            if (response.export_url) {
                // 创建隐藏的iframe来下载文件
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = response.export_url;
                document.body.appendChild(iframe);
                
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 3000);
                
                console.log('✅ 使用API v2成功导出数据');
            } else {
                throw new Error('导出URL未返回');
            }
        })
        .catch(error => {
            console.error('API v2导出失败，使用API v1:', error);
            // 降级到API v1导出
            exportDataV1();
        })
        .finally(() => {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        });
    } else {
        console.warn('API v2客户端未加载，使用API v1导出');
        exportDataV1();
    }
    
    // API v1导出降级函数
    function exportDataV1() {
        const params = new URLSearchParams();
        
        // 添加高级筛选参数
        if (advancedFilters.length > 0) {
            params.append('advanced_filters', JSON.stringify(advancedFilters));
        }
        
        // 标记为导出请求，返回所有筛选后的数据
        params.append('export', 'true');
        
        const url = `/api/resources/data/wip_lot?${params}`;
        
        // 创建隐藏的iframe来下载文件
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = url;
        document.body.appendChild(iframe);
        
        // 恢复按钮状态
        setTimeout(() => {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
            document.body.removeChild(iframe);
        }, 3000);
        
        console.log('📊 使用API v1导出数据');
    }
}

</script>
{% endblock %} 