# -*- coding: utf-8 -*-
"""
数据库连接助手 - 100% MySQL模式
统一管理MySQL数据库连接

Author: AI Assistant
Date: 2025-06-12
"""

import pymysql
from flask import current_app
import os

def get_mysql_connection(database=None):
    """获取MySQL数据库连接
    
    Args:
        database: 数据库名称，None表示使用默认业务数据库(aps)
    """
    config = current_app.config
    
    # 确定数据库名称
    if database is None:
        db_name = getattr(config, 'MYSQL_DATABASE', 'aps')  # 默认业务数据库 aps
    elif database == 'system':
        db_name = getattr(config, 'MYSQL_SYSTEM_DATABASE', 'aps_system')  # 系统数据库 aps_system
    else:
        db_name = database
    
    # 创建MySQL连接
    conn = pymysql.connect(
        host=getattr(config, 'MYSQL_HOST', '127.0.0.1'),
        port=getattr(config, 'MYSQL_PORT', 3306),
        user=getattr(config, 'MYSQL_USER', 'root'),
        password=getattr(config, 'MYSQL_PASSWORD', 'WWWwww123!'),
        database=db_name,
        charset=getattr(config, 'MYSQL_CHARSET', 'utf8mb4'),
        cursorclass=pymysql.cursors.DictCursor,
        autocommit=False
    )
    
    return conn

def get_aps_db():
    """获取APS业务数据库连接 (aps)"""
    return get_mysql_connection()

def get_system_db():
    """获取系统配置数据库连接 (aps_system)"""
    return get_mysql_connection('system')

# 表到数据库的映射
TABLE_DB_MAPPING = {
    # 系统配置表 - aps_system数据库
    'ai_settings': get_system_db,
    'settings': get_system_db,
    'user_filter_presets': get_system_db,
    'scheduling_tasks': get_system_db,
    'database_info': get_system_db,
    'migration_log': get_system_db,
    'system_settings': get_system_db,
    
    # 核心业务表 - aps数据库
    'users': get_aps_db,
    'user_permissions': get_aps_db,
    'user_action_logs': get_aps_db,
    'product_priority_config': get_aps_db,
    'email_configs': get_aps_db,
    'email_attachments': get_aps_db,
    'order_data': get_aps_db,
    'excel_mappings': get_aps_db,
    'resources': get_aps_db,
    'products': get_aps_db,
    'production_orders': get_aps_db,
    'production_schedules': get_aps_db,
    'customer_orders': get_aps_db,
    'order_items': get_aps_db,
    'test_specs': get_aps_db,
    'maintenance_records': get_aps_db,
    'resource_usage_logs': get_aps_db,
    'wip_records': get_aps_db,
    
    # 生产数据表 - aps数据库
    'v_wip_lot_unified': get_aps_db,
    'v_et_wait_lot_unified': get_aps_db,
    'LotPriorityDone': get_aps_db,
    'lot_wip': get_aps_db,
    'v_ct_unified': get_aps_db,
    'v_eqp_status_unified': get_aps_db,
    'v_et_ft_test_spec_unified': get_aps_db,
    'v_et_uph_eqp_unified': get_aps_db,
    'v_et_recipe_file_unified': get_aps_db,
    'tcc_inv': get_aps_db,
    
    # 优先级配置表 - aps数据库
    'lot_priority_config': get_aps_db,
    'device_priority_config': get_aps_db,
    
    # 兼容性映射（大小写变体）
    'v_ct_unified': get_aps_db,
    'v_eqp_status_unified': get_aps_db,
    'v_et_ft_test_spec_unified': get_aps_db,
    'v_et_uph_eqp_unified': get_aps_db,
    'v_et_recipe_file_unified': get_aps_db,
    'TCC_INV': get_aps_db,
    'v_et_wait_lot_unified': get_aps_db,
    'v_wip_lot_unified': get_aps_db,
    'LOT_PRIORITY_CONFIG': get_aps_db,
    'DEVICE_PRIORITY_CONFIG': get_aps_db,
}

def get_db_for_table(table_name):
    """根据表名获取对应的数据库连接函数"""
    return TABLE_DB_MAPPING.get(table_name.lower(), get_aps_db)

def execute_query(table_name, query, params=None):
    """在正确的数据库中执行查询"""
    db_func = get_db_for_table(table_name)
    conn = db_func()
    try:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            return cursor.fetchall()
        else:
            conn.commit()
            return cursor.rowcount
    finally:
        conn.close()

def is_mysql_available():
    """检查MySQL是否可用"""
    try:
        conn = get_mysql_connection()
        conn.close()
        return True
    except Exception:
        return False
