#!/usr/bin/env python3
import pymysql

try:
    conn = pymysql.connect(
        host='127.0.0.1', 
        user='root', 
        password='WWWwww123!', 
        database='aps'
    )
    cursor = conn.cursor()
    
    # 检查表是否存在
    cursor.execute('SHOW TABLES LIKE "test_data"')
    result = cursor.fetchall()
    
    if len(result) > 0:
        print('表 test_data 已存在，正在删除...')
        cursor.execute('DROP TABLE test_data')
        conn.commit()
        print('✅ 已删除旧表 test_data')
    else:
        print('表 test_data 不存在')
    
    conn.close()
    print('✅ 数据库操作完成')
    
except Exception as e:
    print(f'❌ 数据库操作失败: {e}')
