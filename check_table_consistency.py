#!/usr/bin/env python3
"""
检查11个表的字段一致性问题
"""

import pymysql
import pandas as pd
import os
from pathlib import Path

def check_table_consistency():
    """检查表字段一致性"""
    
    try:
        conn = pymysql.connect(
            host='127.0.0.1', 
            user='root', 
            password='WWWwww123!', 
            database='aps'
        )
        cursor = conn.cursor()
        
        # 重点检查的11个表
        key_tables = [
            'ct', 'devicepriorityconfig', 'wip_lot', 'eqp_status', 
            'et_recipe_file', 'et_ft_test_spec', 'et_uph_eqp', 
            'et_wait_lot', 'lotpriorityconfig', 'tcc_inv', 'lotprioritydone'
        ]
        
        print("🔍 检查11个关键表的字段一致性...")
        print("=" * 80)
        
        table_info = {}
        
        for table_name in key_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if cursor.fetchone():
                print(f"\n📋 表: {table_name}")
                print("-" * 40)
                
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                
                table_info[table_name] = {
                    'columns': columns,
                    'count': count,
                    'exists': True
                }
                
                print(f"字段数: {len(columns)}")
                print(f"记录数: {count}")
                print("字段列表:")
                for col in columns:
                    field, type_, null, key, default, extra = col
                    print(f"  - {field}: {type_} {'(PK)' if key == 'PRI' else ''}")
                
                # 检查是否有Excel对应文件
                excel_dir = Path("Excellist2025.06.05")
                if excel_dir.exists():
                    excel_files = list(excel_dir.glob(f"*{table_name}*.xlsx")) + list(excel_dir.glob(f"{table_name}*.xlsx"))
                    if excel_files:
                        print(f"📄 对应Excel文件: {[f.name for f in excel_files]}")
                    else:
                        print("❌ 未找到对应Excel文件")
                
            else:
                print(f"\n❌ 表不存在: {table_name}")
                table_info[table_name] = {'exists': False}
        
        # 分析字段不一致问题
        print("\n" + "=" * 80)
        print("🔍 字段一致性分析:")
        
        # 特别检查devicepriorityconfig
        if 'devicepriorityconfig' in table_info and table_info['devicepriorityconfig']['exists']:
            print(f"\n⚠️  devicepriorityconfig 表详细分析:")
            columns = table_info['devicepriorityconfig']['columns']
            
            # 检查是否有Excel导入的字段vs预期的字段
            excel_fields = []
            expected_fields = ['device', 'priority', 'from_time', 'end_time', 'refresh_time', 'user', 'STAGE', 'HANDLER_CONFIG', 'HANDLER_PRIORITY', 'SETUP_QTY', 'Price']
            
            actual_fields = [col[0] for col in columns if col[0] not in ['id', 'created_at', 'updated_at']]
            
            print(f"  实际字段: {actual_fields}")
            print(f"  预期字段: {expected_fields}")
            
            missing_fields = set(expected_fields) - set(actual_fields)
            extra_fields = set(actual_fields) - set(expected_fields)
            
            if missing_fields:
                print(f"  ❌ 缺少字段: {missing_fields}")
            if extra_fields:
                print(f"  ⚠️  额外字段: {extra_fields}")
            if not missing_fields and not extra_fields:
                print(f"  ✅ 字段完全匹配")
        
        conn.close()
        return table_info
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return {}

def check_excel_files():
    """检查Excel文件结构"""
    
    excel_dir = Path("Excellist2025.06.05")
    if not excel_dir.exists():
        print("❌ Excel目录不存在")
        return
    
    print(f"\n📂 检查Excel文件结构:")
    print("-" * 40)
    
    excel_files = list(excel_dir.glob("*.xlsx"))
    for excel_file in excel_files:
        if excel_file.name.startswith('~$'):
            continue
            
        try:
            print(f"\n📄 {excel_file.name}:")
            excel_data = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
            
            for sheet_name, df in excel_data.items():
                if not df.empty:
                    print(f"  工作表: {sheet_name}")
                    print(f"    行数: {len(df)}")
                    print(f"    列数: {len(df.columns)}")
                    print(f"    列名: {list(df.columns)}")
                    
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")

if __name__ == '__main__':
    table_info = check_table_consistency()
    check_excel_files()
