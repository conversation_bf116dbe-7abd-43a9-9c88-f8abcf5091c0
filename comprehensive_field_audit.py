#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面字段审计脚本
检查所有业务表的id字段和字段一致性问题
"""

import sys
import os
import pymysql
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def comprehensive_field_audit():
    """全面字段审计"""
    print("🔍 全面字段审计 - 检查所有业务表")
    print("=" * 100)
    
    # 定义需要检查的业务表
    business_tables = {
        'aps': [
            'ET_WAIT_LOT',
            'WIP_LOT', 
            'EQP_STATUS',
            'ET_UPH_EQP',
            'ET_FT_TEST_SPEC',
            'TCC_INV',
            'CT',
            'ET_RECIPE_FILE'
        ],
        'aps_system': [
            'devicepriorityconfig',
            'lotpriorityconfig',
            'lotprioritydone'
        ]
    }
    
    # Excel文件路径映射
    excel_files = {
        'ET_WAIT_LOT': 'ET_WAIT_LOT.xlsx',
        'WIP_LOT': 'WIP_LOT.xlsx',
        'EQP_STATUS': 'EQP_STATUS.xlsx', 
        'ET_UPH_EQP': 'ET_UPH_EQP.xlsx',
        'ET_FT_TEST_SPEC': 'ET_FT_TEST_SPEC.xlsx',
        'TCC_INV': 'TCC_INV.xlsx',
        'CT': 'CT.xlsx',
        'ET_RECIPE_FILE': 'ET_RECIPE_FILE.xlsx',
        'devicepriorityconfig': 'devicepriorityconfig.xlsx',
        'lotpriorityconfig': 'lotpriorityconfig.xlsx',
        'lotprioritydone': 'lotprioritydone.xlsx'
    }
    
    excel_base_path = r"D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\Excellist2025.06.05"
    
    results = {}
    
    # 1. 检查MySQL数据库表结构
    print("\n📊 第一步：检查MySQL数据库表结构")
    print("-" * 80)
    
    for database, tables in business_tables.items():
        print(f"\n🗄️ 数据库: {database}")
        
        try:
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            for table_name in tables:
                print(f"\n📋 表: {table_name}")
                
                try:
                    with connection.cursor() as cursor:
                        # 获取表结构
                        cursor.execute(f"DESCRIBE {table_name}")
                        columns_info = cursor.fetchall()
                        
                        # 检查是否有自增id
                        has_auto_id = False
                        id_info = None
                        all_columns = []
                        
                        for col_info in columns_info:
                            col_name, col_type, is_null, key, default, extra = col_info
                            all_columns.append(col_name)
                            
                            if col_name.lower() == 'id':
                                has_auto_id = True
                                id_info = {
                                    'type': col_type,
                                    'key': key,
                                    'auto_increment': 'auto_increment' in extra.lower(),
                                    'extra': extra
                                }
                        
                        # 获取记录数
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        record_count = cursor.fetchone()[0]
                        
                        results[table_name] = {
                            'database': database,
                            'mysql_columns': all_columns,
                            'mysql_column_count': len(all_columns),
                            'has_auto_id': has_auto_id,
                            'id_info': id_info,
                            'record_count': record_count
                        }
                        
                        print(f"  ✅ 字段数: {len(all_columns)}")
                        print(f"  {'✅' if has_auto_id else '❌'} 自增ID: {has_auto_id}")
                        if has_auto_id and id_info:
                            print(f"  📋 ID配置: {id_info['type']}, 主键:{id_info['key']}, 自增:{id_info['auto_increment']}")
                        print(f"  📊 记录数: {record_count}")
                        
                except Exception as e:
                    print(f"  ❌ 表结构检查失败: {e}")
                    results[table_name] = {'error': str(e)}
            
            connection.close()
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
    
    # 2. 检查Excel文件结构
    print(f"\n📂 第二步：检查Excel文件结构")
    print("-" * 80)
    
    for table_name, excel_file in excel_files.items():
        if table_name not in results:
            continue
            
        excel_path = os.path.join(excel_base_path, excel_file)
        print(f"\n📄 检查: {excel_file}")
        
        try:
            if os.path.exists(excel_path):
                # 读取Excel文件
                df = pd.read_excel(excel_path, nrows=0)  # 只读取列名
                excel_columns = list(df.columns)
                
                results[table_name]['excel_columns'] = excel_columns
                results[table_name]['excel_column_count'] = len(excel_columns)
                results[table_name]['excel_has_id'] = 'id' in [col.lower() for col in excel_columns]
                
                print(f"  ✅ 文件存在，字段数: {len(excel_columns)}")
                print(f"  {'✅' if results[table_name]['excel_has_id'] else '❌'} 包含ID字段: {results[table_name]['excel_has_id']}")
                
                # 显示前几个字段
                first_cols = excel_columns[:5] if len(excel_columns) >= 5 else excel_columns
                print(f"  📋 前5个字段: {', '.join(first_cols)}")
                
            else:
                print(f"  ❌ 文件不存在: {excel_path}")
                results[table_name]['excel_error'] = 'File not found'
                
        except Exception as e:
            print(f"  ❌ Excel读取失败: {e}")
            results[table_name]['excel_error'] = str(e)
    
    # 3. 检查前端字段配置
    print(f"\n🖥️ 第三步：检查前端字段配置")
    print("-" * 80)
    
    try:
        # 读取数据源管理器配置
        with open('app/services/data_source_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找字段映射配置
        for table_name in results.keys():
            # 查找多种可能的表名格式
            table_variants = [
                table_name,
                table_name.lower(),
                table_name.replace('_', ''),
                table_name.lower().replace('_', '')
            ]
            
            frontend_columns = None
            for variant in table_variants:
                pattern = f"'{variant}':"
                if pattern in content:
                    # 找到配置行
                    start = content.find(pattern)
                    if start != -1:
                        # 找到列表开始
                        list_start = content.find('[', start)
                        if list_start != -1:
                            # 找到列表结束
                            list_end = content.find(']', list_start)
                            if list_end != -1:
                                # 提取字段列表
                                list_content = content[list_start+1:list_end]
                                # 解析字段名
                                import re
                                fields = re.findall(r"'([^']*)'", list_content)
                                frontend_columns = fields
                                break
            
            if frontend_columns:
                results[table_name]['frontend_columns'] = frontend_columns
                results[table_name]['frontend_column_count'] = len(frontend_columns)
                results[table_name]['frontend_has_id'] = 'id' in frontend_columns
                
                print(f"\n📋 表: {table_name}")
                print(f"  ✅ 前端配置字段数: {len(frontend_columns)}")
                print(f"  {'✅' if results[table_name]['frontend_has_id'] else '❌'} 包含ID字段: {results[table_name]['frontend_has_id']}")
                
                # 显示前几个字段
                first_cols = frontend_columns[:5] if len(frontend_columns) >= 5 else frontend_columns
                print(f"  📋 前5个字段: {', '.join(first_cols)}")
            else:
                print(f"\n📋 表: {table_name}")
                print(f"  ❌ 未找到前端字段配置")
                results[table_name]['frontend_error'] = 'Configuration not found'
        
    except Exception as e:
        print(f"❌ 前端配置读取失败: {e}")
    
    # 4. 生成一致性分析报告
    print(f"\n📈 第四步：一致性分析报告")
    print("=" * 100)
    
    consistency_issues = []
    
    for table_name, data in results.items():
        if 'error' in data:
            continue
            
        print(f"\n🎯 表: {table_name}")
        print("-" * 60)
        
        # 检查ID字段一致性
        mysql_has_id = data.get('has_auto_id', False)
        excel_has_id = data.get('excel_has_id', False)
        frontend_has_id = data.get('frontend_has_id', False)
        
        print(f"ID字段检查:")
        print(f"  MySQL自增ID: {'✅' if mysql_has_id else '❌'} {mysql_has_id}")
        print(f"  Excel ID字段: {'✅' if excel_has_id else '❌'} {excel_has_id}")
        print(f"  前端ID配置: {'✅' if frontend_has_id else '❌'} {frontend_has_id}")
        
        # 检查字段数量一致性
        mysql_count = data.get('mysql_column_count', 0)
        excel_count = data.get('excel_column_count', 0)
        frontend_count = data.get('frontend_column_count', 0)
        
        print(f"字段数量:")
        print(f"  MySQL: {mysql_count}")
        print(f"  Excel: {excel_count}")
        print(f"  前端: {frontend_count}")
        
        # 标识问题
        issues = []
        if not mysql_has_id:
            issues.append("MySQL缺少自增ID")
        if not frontend_has_id:
            issues.append("前端配置缺少ID字段")
        if abs(mysql_count - frontend_count) > 3:  # 允许3个字段的差异（created_at, updated_at等）
            issues.append(f"字段数量差异过大 MySQL({mysql_count}) vs 前端({frontend_count})")
        
        if issues:
            consistency_issues.extend([(table_name, issue) for issue in issues])
            print(f"  ⚠️ 问题: {'; '.join(issues)}")
        else:
            print(f"  ✅ 一致性良好")
    
    # 5. 生成修复建议
    print(f"\n🔧 第五步：修复建议")
    print("=" * 100)
    
    if consistency_issues:
        print("发现以下问题需要修复:")
        
        tables_need_frontend_id = []
        tables_need_mysql_id = []
        
        for table_name, issue in consistency_issues:
            print(f"❌ {table_name}: {issue}")
            
            if "前端配置缺少ID字段" in issue:
                tables_need_frontend_id.append(table_name)
            if "MySQL缺少自增ID" in issue:
                tables_need_mysql_id.append(table_name)
        
        if tables_need_frontend_id:
            print(f"\n需要在前端配置中添加ID字段的表:")
            for table in tables_need_frontend_id:
                print(f"  - {table}")
        
        if tables_need_mysql_id:
            print(f"\n需要在MySQL中添加自增ID的表:")
            for table in tables_need_mysql_id:
                print(f"  - {table}")
                
        return False, results, consistency_issues
    else:
        print("🎉 所有表的字段配置一致性良好！")
        return True, results, []

if __name__ == "__main__":
    success, results, issues = comprehensive_field_audit()
    
    # 生成详细报告文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"field_audit_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("全面字段审计报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for table_name, data in results.items():
            f.write(f"表: {table_name}\n")
            f.write("-" * 40 + "\n")
            f.write(f"数据库: {data.get('database', 'N/A')}\n")
            f.write(f"MySQL字段数: {data.get('mysql_column_count', 'N/A')}\n")
            f.write(f"Excel字段数: {data.get('excel_column_count', 'N/A')}\n")
            f.write(f"前端字段数: {data.get('frontend_column_count', 'N/A')}\n")
            f.write(f"MySQL自增ID: {data.get('has_auto_id', 'N/A')}\n")
            f.write(f"前端ID配置: {data.get('frontend_has_id', 'N/A')}\n")
            f.write(f"记录数: {data.get('record_count', 'N/A')}\n\n")
        
        if issues:
            f.write("发现的问题:\n")
            for table, issue in issues:
                f.write(f"- {table}: {issue}\n")
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    print(f"审计完成，状态: {'✅ 通过' if success else '❌ 发现问题'}") 