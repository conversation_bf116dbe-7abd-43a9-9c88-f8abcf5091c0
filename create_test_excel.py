#!/usr/bin/env python3
"""
创建测试Excel文件
"""

import pandas as pd
import os

def create_test_excel():
    """创建一个简单的测试Excel文件"""
    
    # 创建测试数据
    test_data = {
        'ID': [1, 2, 3, 4, 5],
        'Name': ['Test1', 'Test2', 'Test3', 'Test4', 'Test5'],
        'Value': [100, 200, 300, 400, 500],
        'Date': ['2025-06-26', '2025-06-26', '2025-06-26', '2025-06-26', '2025-06-26'],
        'Status': ['Active', 'Active', 'Inactive', 'Active', 'Pending']
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 确保目录存在
    os.makedirs('test_import', exist_ok=True)
    
    # 保存为Excel文件
    excel_path = 'test_import/test_data.xlsx'
    df.to_excel(excel_path, index=False)
    
    print(f"✅ 测试Excel文件已创建: {excel_path}")
    print(f"📊 数据行数: {len(df)}")
    print(f"📋 列名: {list(df.columns)}")
    
    return excel_path

if __name__ == '__main__':
    create_test_excel()
