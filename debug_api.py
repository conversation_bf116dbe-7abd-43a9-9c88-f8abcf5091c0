#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API问题
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.enhanced_data_source_manager import get_enhanced_manager
from app.config.table_configs import get_table_config

def test_table_structure(table_name):
    """测试表结构获取"""
    print(f"🔍 测试表结构获取: {table_name}")
    
    try:
        manager = get_enhanced_manager()
        
        # 测试columns API
        print("1. 测试 get_table_columns...")
        columns_result = manager.get_table_columns(table_name)
        print(f"   结果: {columns_result}")
        
        # 测试table_info
        print("2. 测试 get_table_info...")
        table_info = manager.field_manager.get_table_info(table_name)
        print(f"   结果类型: {type(table_info)}")
        print(f"   字段类型: {type(table_info.get('fields', []))}")
        print(f"   字段数量: {len(table_info.get('fields', []))}")
        
        # 测试表配置
        print("3. 测试 get_table_config...")
        table_config = get_table_config(table_name)
        print(f"   配置: {table_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    table_name = sys.argv[1] if len(sys.argv) > 1 else "eqp_status"
    test_table_structure(table_name)
