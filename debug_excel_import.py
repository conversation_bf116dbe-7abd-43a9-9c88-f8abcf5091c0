#!/usr/bin/env python3
"""
调试Excel导入功能
检查Excel文件内容和数据库连接
"""

import pandas as pd
import pymysql
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_excel_file(file_path):
    """检查Excel文件内容"""
    print(f"🔍 检查Excel文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"✅ 成功读取Excel文件")
        print(f"📊 文件尺寸: {df.shape[0]} 行 x {df.shape[1]} 列")
        print(f"📋 列名: {list(df.columns)}")
        
        # 显示前几行数据
        print(f"\n📝 前3行数据:")
        for i, row in df.head(3).iterrows():
            print(f"  第{i+1}行: {row.to_dict()}")
        
        # 检查必填字段
        required_fields = ['DEVICE', 'PRIORITY']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            print(f"⚠️  缺少必填字段: {missing_fields}")
        else:
            print(f"✅ 必填字段完整")
        
        # 检查数据完整性
        empty_device_count = df['DEVICE'].isna().sum()
        empty_priority_count = df['PRIORITY'].isna().sum()
        
        print(f"\n📈 数据完整性:")
        print(f"  DEVICE字段空值: {empty_device_count} 个")
        print(f"  PRIORITY字段空值: {empty_priority_count} 个")
        print(f"  有效数据行数: {len(df) - max(empty_device_count, empty_priority_count)} 行")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print(f"\n🔗 检查数据库连接...")
    
    # 数据库配置
    configs = {
        'aps': {
            'host': 'localhost',
            'user': 'root',
            'password': 'Flzx3000',
            'database': 'aps',
            'charset': 'utf8mb4'
        },
        'aps_system': {
            'host': 'localhost',
            'user': 'root',
            'password': 'Flzx3000',
            'database': 'aps_system',
            'charset': 'utf8mb4'
        }
    }
    
    for db_name, config in configs.items():
        try:
            conn = pymysql.connect(**config)
            print(f"✅ 成功连接到 {db_name} 数据库")
            
            # 检查优先级配置表
            with conn.cursor() as cursor:
                # 检查devicepriorityconfig表
                cursor.execute("SHOW TABLES LIKE 'devicepriorityconfig'")
                device_table_exists = cursor.fetchone() is not None
                
                # 检查lotpriorityconfig表
                cursor.execute("SHOW TABLES LIKE 'lotpriorityconfig'")
                lot_table_exists = cursor.fetchone() is not None
                
                print(f"  📋 {db_name}.devicepriorityconfig: {'存在' if device_table_exists else '不存在'}")
                print(f"  📋 {db_name}.lotpriorityconfig: {'存在' if lot_table_exists else '不存在'}")
                
                # 如果表存在，检查记录数
                if device_table_exists:
                    cursor.execute("SELECT COUNT(*) FROM devicepriorityconfig")
                    device_count = cursor.fetchone()[0]
                    print(f"    📊 devicepriorityconfig记录数: {device_count}")
                
                if lot_table_exists:
                    cursor.execute("SELECT COUNT(*) FROM lotpriorityconfig")
                    lot_count = cursor.fetchone()[0]
                    print(f"    📊 lotpriorityconfig记录数: {lot_count}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 连接 {db_name} 数据库失败: {e}")

def simulate_api_processing(file_path):
    """模拟API处理过程"""
    print(f"\n🔄 模拟API处理过程...")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"✅ 读取Excel文件成功，共 {len(df)} 行")
        
        # 检查文件名
        filename = os.path.basename(file_path).lower()
        if 'device' in filename:
            table_type = 'device'
            print(f"✅ 识别为产品优先级配置文件")
        elif 'lot' in filename:
            table_type = 'lot'
            print(f"✅ 识别为批次优先级配置文件")
        else:
            print(f"❌ 无法识别文件类型，文件名: {filename}")
            return
        
        # 模拟数据处理
        valid_count = 0
        errors = []
        
        for index, row in df.iterrows():
            device_val = row.get('DEVICE')
            priority_val = row.get('PRIORITY')
            
            print(f"  处理第{index+1}行: DEVICE={device_val}, PRIORITY={priority_val}")
            
            if pd.isna(device_val) or pd.isna(priority_val):
                error_msg = f'第{index+2}行: DEVICE和PRIORITY字段不能为空'
                errors.append(error_msg)
                print(f"    ❌ {error_msg}")
                continue
            
            # 模拟创建记录
            if table_type == 'device':
                record_data = {
                    'device': str(device_val),
                    'priority': str(priority_val),
                    'from_time': row.get('FROM_TIME'),
                    'end_time': row.get('END_TIME'),
                    'refresh_time': row.get('REFRESH_TIME'),
                    'user': row.get('USER', 'admin')
                }
            else:  # lot
                record_data = {
                    'device': str(device_val),
                    'stage': row.get('STAGE'),
                    'priority': str(priority_val),
                    'refresh_time': row.get('REFRESH_TIME'),
                    'user': row.get('USER', 'admin')
                }
            
            print(f"    ✅ 创建记录: {record_data}")
            valid_count += 1
        
        print(f"\n📊 处理结果:")
        print(f"  有效记录: {valid_count} 条")
        print(f"  错误记录: {len(errors)} 条")
        
        if errors:
            print(f"  错误详情:")
            for error in errors[:5]:  # 只显示前5个错误
                print(f"    - {error}")
        
    except Exception as e:
        print(f"❌ 模拟处理失败: {e}")

def main():
    """主函数"""
    print("🔧 Excel导入功能调试工具")
    print(f"⏰ 调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试文件路径
    test_file = r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\devicepriorityconfig.xlsx"
    
    # 1. 检查Excel文件
    if check_excel_file(test_file):
        # 2. 检查数据库连接
        check_database_connection()
        
        # 3. 模拟API处理
        simulate_api_processing(test_file)
    
    print("\n" + "=" * 60)
    print("🎯 调试建议:")
    print("1. 确认Excel文件格式正确，包含DEVICE和PRIORITY字段")
    print("2. 确认数据库表在aps_system数据库中存在")
    print("3. 检查API日志，查看详细错误信息")
    print("4. 验证数据类型匹配（priority字段应为字符串）")

if __name__ == "__main__":
    main()
