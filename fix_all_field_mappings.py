#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复所有字段映射问题的脚本
"""

import sys
import os
import pymysql
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def fix_all_field_mappings():
    """修复所有字段映射问题"""
    print("🔧 开始修复所有字段映射问题")
    print("=" * 80)
    
    # 获取MySQL中的实际字段
    tables_to_fix = {}
    
    # 连接aps数据库
    try:
        aps_conn = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        # 获取需要修复的表
        tables_need_fixing = ['EQP_STATUS', 'ET_UPH_EQP', 'TCC_INV', 'CT']
        
        for table_name in tables_need_fixing:
            with aps_conn.cursor() as cursor:
                cursor.execute(f"DESCRIBE {table_name}")
                columns_info = cursor.fetchall()
                all_columns = [col_info[0] for col_info in columns_info]
                tables_to_fix[table_name] = all_columns
                print(f"✅ 获取{table_name}字段: {len(all_columns)}个")
        
        aps_conn.close()
        
    except Exception as e:
        print(f"❌ 获取aps数据库字段失败: {e}")
        return False
    
    # 连接aps_system数据库
    try:
        system_conn = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps_system',
            charset='utf8mb4'
        )
        
        # 获取系统表字段
        system_tables = ['devicepriorityconfig', 'lotpriorityconfig']
        
        for table_name in system_tables:
            with system_conn.cursor() as cursor:
                cursor.execute(f"DESCRIBE {table_name}")
                columns_info = cursor.fetchall()
                all_columns = [col_info[0] for col_info in columns_info]
                tables_to_fix[table_name] = all_columns
                print(f"✅ 获取{table_name}字段: {len(all_columns)}个")
        
        system_conn.close()
        
    except Exception as e:
        print(f"❌ 获取aps_system数据库字段失败: {e}")
    
    # 读取当前配置文件
    config_file = 'app/services/data_source_manager.py'
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n📝 开始修复字段映射配置")
        print("-" * 60)
        
        # 生成修复后的字段映射
        fixes = []
        
        # 修复EQP_STATUS
        if 'EQP_STATUS' in tables_to_fix:
            eqp_columns = tables_to_fix['EQP_STATUS']
            eqp_str = "', '".join(eqp_columns)
            fixes.append({
                'old_pattern': r"'eqp_status': \[.*?\],\s*'EQP_STATUS': \[.*?\],",
                'new_text': f"'eqp_status': ['{eqp_str}'],\n                'EQP_STATUS': ['{eqp_str}'],"
            })
            print(f"✅ 准备修复EQP_STATUS: {len(eqp_columns)}个字段")
        
        # 修复ET_UPH_EQP  
        if 'ET_UPH_EQP' in tables_to_fix:
            uph_columns = tables_to_fix['ET_UPH_EQP']
            uph_str = "', '".join(uph_columns)
            fixes.append({
                'old_pattern': r"'ET_UPH_EQP': \[.*?\],\s*'et_uph_eqp': \[.*?\],",
                'new_text': f"'ET_UPH_EQP': ['{uph_str}'],\n                'et_uph_eqp': ['{uph_str}'],"
            })
            print(f"✅ 准备修复ET_UPH_EQP: {len(uph_columns)}个字段")
        
        # 修复CT表
        if 'CT' in tables_to_fix:
            ct_columns = tables_to_fix['CT']
            ct_str = "', '".join(ct_columns)
            fixes.append({
                'old_pattern': r"'CT': \[.*?\],\s*'ct': \[.*?\],",
                'new_text': f"'CT': ['{ct_str}'],\n                'ct': ['{ct_str}'],"
            })
            print(f"✅ 准备修复CT: {len(ct_columns)}个字段")
        
        # 修复devicepriorityconfig
        if 'devicepriorityconfig' in tables_to_fix:
            device_columns = tables_to_fix['devicepriorityconfig']
            device_str = "', '".join(device_columns)
            fixes.append({
                'old_pattern': r"'devicepriorityconfig': \[.*?\],\s*'device_priority_config': \[.*?\],",
                'new_text': f"'devicepriorityconfig': ['{device_str}'],\n                'device_priority_config': ['{device_str}'],"
            })
            print(f"✅ 准备修复devicepriorityconfig: {len(device_columns)}个字段")
        
        # 修复lotpriorityconfig
        if 'lotpriorityconfig' in tables_to_fix:
            lot_columns = tables_to_fix['lotpriorityconfig']
            lot_str = "', '".join(lot_columns)
            fixes.append({
                'old_pattern': r"'lotpriorityconfig': \[.*?\],\s*'lot_priority_config': \[.*?\],",
                'new_text': f"'lotpriorityconfig': ['{lot_str}'],\n                'lot_priority_config': ['{lot_str}'],"
            })
            print(f"✅ 准备修复lotpriorityconfig: {len(lot_columns)}个字段")
        
        # 应用修复
        modified_content = content
        for fix in fixes:
            modified_content = re.sub(
                fix['old_pattern'], 
                fix['new_text'], 
                modified_content, 
                flags=re.DOTALL
            )
        
        # 保存修复后的文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"\n🎉 字段映射修复完成!")
        print(f"✅ 修复了{len(fixes)}个表的字段映射")
        print(f"📄 配置文件已更新: {config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复配置文件失败: {e}")
        return False

if __name__ == "__main__":
    success = fix_all_field_mappings()
    print(f"\n修复完成，状态: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("⚠️ 重要提示: 请重启Flask应用以加载新的配置!")
        print("命令: python run.py") 