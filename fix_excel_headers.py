#!/usr/bin/env python3
"""
修复Excel文件的字段名以匹配数据库
"""

import pandas as pd
import os
from pathlib import Path

def fix_excel_headers():
    """修复Excel文件的字段名"""
    
    excel_dir = Path("Excellist2025.06.05")
    if not excel_dir.exists():
        print("❌ Excel目录不存在")
        return
    
    print("🔧 修复Excel文件字段名...")
    print("=" * 60)
    
    # devicepriorityconfig字段映射
    device_field_mapping = {
        'DEVICE': 'device',
        'STAGE': 'stage', 
        'HANDLER_CONFIG': 'handler_config',
        'HANDLER_PRIORITY': 'handler_priority',
        'SETUP_QTY': 'setup_qty',
        'PRIORITY': 'priority',
        'Price': 'price',
        'FROM_TIME': 'from_time',
        'END_TIME': 'end_time',
        'REFRESH_TIME': 'refresh_time',
        'USER': 'user'
    }
    
    # 修复devicepriorityconfig.xlsx
    device_excel = excel_dir / "devicepriorityconfig.xlsx"
    if device_excel.exists():
        try:
            print(f"\n📄 修复 {device_excel.name}...")
            
            # 读取Excel文件
            df = pd.read_excel(device_excel, engine='openpyxl')
            print(f"  原始字段: {list(df.columns)}")
            
            # 重命名字段
            df = df.rename(columns=device_field_mapping)
            print(f"  修复后字段: {list(df.columns)}")
            
            # 保存修复后的文件
            backup_file = excel_dir / f"devicepriorityconfig_backup_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            device_excel.rename(backup_file)
            print(f"  📦 备份原文件: {backup_file.name}")
            
            # 保存修复后的文件
            df.to_excel(device_excel, index=False, engine='openpyxl')
            print(f"  ✅ 保存修复后文件: {device_excel.name}")
            
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")
    
    # 检查其他Excel文件是否需要修复
    print(f"\n📂 检查其他Excel文件...")
    
    excel_files = [f for f in excel_dir.glob("*.xlsx") if not f.name.startswith('~$') and not f.name.startswith('devicepriorityconfig_backup')]
    
    for excel_file in excel_files:
        try:
            print(f"\n📄 检查 {excel_file.name}...")
            df = pd.read_excel(excel_file, engine='openpyxl')
            columns = list(df.columns)
            
            # 检查是否有需要修复的字段
            needs_fix = False
            mixed_case_columns = []
            
            for col in columns:
                # 检查是否有混合大小写或特殊字符
                if col != col.upper() and col != col.lower():
                    mixed_case_columns.append(col)
                    needs_fix = True
                elif '(' in col or ')' in col or '.' in col:
                    mixed_case_columns.append(col)
                    needs_fix = True
            
            if needs_fix:
                print(f"  ⚠️  发现需要修复的字段: {mixed_case_columns}")
                
                # 对于tcc_inv表的特殊处理
                if 'TCC_INV' in excel_file.name.upper():
                    tcc_field_mapping = {
                        'Unnamed: 0': 'unnamed_0',
                        '硬件编码': '硬件编码',
                        '关键硬件': '关键硬件', 
                        '图片': '图片',
                        '寿命状态': '寿命状态',
                        '仓库': '仓库',
                        '初始库位': '初始库位',
                        '当前储位1': '当前储位1',
                        '当前储位2': '当前储位2',
                        '责任人': '责任人',
                        '周期消耗数': '周期消耗数',
                        '当前库位': '当前库位',
                        '封装形式': '封装形式',
                        '状态': '状态',
                        '类别': '类别',
                        '设备机型': '设备机型',
                        '寄放方': '寄放方',
                        '备注(状态 shipout信息)': '备注_状态_shipout信息_',
                        '类型': '类型',
                        '状态.1': '状态_1',
                        '操作': '操作'
                    }
                    
                    # 重命名字段
                    df_fixed = df.rename(columns=tcc_field_mapping)
                    
                    # 备份并保存
                    backup_file = excel_dir / f"{excel_file.stem}_backup_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                    excel_file.rename(backup_file)
                    print(f"    📦 备份原文件: {backup_file.name}")
                    
                    df_fixed.to_excel(excel_file, index=False, engine='openpyxl')
                    print(f"    ✅ 修复完成: {excel_file.name}")
                    print(f"    修复后字段: {list(df_fixed.columns)}")
                
            else:
                print(f"  ✅ 字段格式正确，无需修复")
                
        except Exception as e:
            print(f"  ❌ 检查失败: {e}")
    
    print(f"\n✅ Excel文件字段修复完成！")

if __name__ == '__main__':
    fix_excel_headers()
