#!/usr/bin/env python3
"""
修复Excel导入功能
1. 检查并创建数据库表
2. 验证Excel文件格式
3. 测试数据插入
"""

import sys
import os
import pymysql
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_tables_if_not_exist():
    """创建数据库表（如果不存在）"""
    print("🔧 检查并创建数据库表...")
    
    # 连接aps_system数据库
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='Flzx3000',
            database='aps_system',
            charset='utf8mb4'
        )
        
        with conn.cursor() as cursor:
            # 创建devicepriorityconfig表
            create_device_table_sql = """
            CREATE TABLE IF NOT EXISTS devicepriorityconfig (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                device VARCHAR(200) NOT NULL COMMENT 'DEVICE - 产品名称',
                priority VARCHAR(10) COMMENT 'PRIORITY - 优先级',
                from_time DATETIME DEFAULT NULL COMMENT 'FROM_TIME - 开始时间',
                end_time DATETIME DEFAULT NULL COMMENT 'END_TIME - 结束时间',
                refresh_time DATETIME DEFAULT NULL COMMENT 'REFRESH_TIME - 刷新时间',
                user VARCHAR(100) DEFAULT NULL COMMENT 'USER - 用户',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_device (device),
                INDEX idx_priority (priority)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品优先级配置表'
            """
            
            cursor.execute(create_device_table_sql)
            print("✅ devicepriorityconfig表检查/创建完成")
            
            # 创建lotpriorityconfig表
            create_lot_table_sql = """
            CREATE TABLE IF NOT EXISTS lotpriorityconfig (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                device VARCHAR(200) NOT NULL COMMENT 'DEVICE - 产品名称',
                stage VARCHAR(100) DEFAULT NULL COMMENT 'STAGE - 工序',
                priority VARCHAR(10) COMMENT 'PRIORITY - 优先级',
                refresh_time DATETIME DEFAULT NULL COMMENT 'REFRESH_TIME - 刷新时间',
                user VARCHAR(100) DEFAULT NULL COMMENT 'USER - 用户',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_device (device),
                INDEX idx_stage (stage),
                INDEX idx_priority (priority)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次优先级配置表'
            """
            
            cursor.execute(create_lot_table_sql)
            print("✅ lotpriorityconfig表检查/创建完成")
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'devicepriorityconfig'")
            device_exists = cursor.fetchone() is not None
            
            cursor.execute("SHOW TABLES LIKE 'lotpriorityconfig'")
            lot_exists = cursor.fetchone() is not None
            
            print(f"📊 表状态:")
            print(f"  devicepriorityconfig: {'存在' if device_exists else '不存在'}")
            print(f"  lotpriorityconfig: {'存在' if lot_exists else '不存在'}")
            
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def test_excel_files():
    """测试Excel文件"""
    print("\n📋 测试Excel文件...")
    
    excel_files = [
        {
            'path': r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\devicepriorityconfig.xlsx",
            'type': 'device'
        },
        {
            'path': r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\lotpriorityconfig.xlsx",
            'type': 'lot'
        }
    ]
    
    for file_info in excel_files:
        file_path = file_info['path']
        file_type = file_info['type']
        
        print(f"\n--- 检查 {file_type} 文件 ---")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
            
        try:
            df = pd.read_excel(file_path)
            print(f"✅ 成功读取文件，{len(df)} 行 x {len(df.columns)} 列")
            print(f"📋 列名: {list(df.columns)}")
            
            # 检查必填字段
            required_fields = ['DEVICE', 'PRIORITY']
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                print(f"❌ 缺少必填字段: {missing_fields}")
            else:
                print(f"✅ 必填字段完整")
                
                # 检查数据完整性
                valid_rows = 0
                for index, row in df.iterrows():
                    device_val = row.get('DEVICE')
                    priority_val = row.get('PRIORITY')
                    
                    if pd.notna(device_val) and pd.notna(priority_val):
                        valid_rows += 1
                
                print(f"📊 有效数据行: {valid_rows}/{len(df)}")
                
                if valid_rows > 0:
                    print(f"✅ 文件格式正确，可以导入")
                else:
                    print(f"❌ 没有有效数据行")
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")

def test_direct_insert():
    """直接测试数据库插入"""
    print("\n🔄 测试直接数据库插入...")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='Flzx3000',
            database='aps_system',
            charset='utf8mb4'
        )
        
        with conn.cursor() as cursor:
            # 测试插入devicepriorityconfig
            print("--- 测试 devicepriorityconfig 插入 ---")
            
            # 检查现有记录数
            cursor.execute("SELECT COUNT(*) FROM devicepriorityconfig")
            before_count = cursor.fetchone()[0]
            print(f"插入前记录数: {before_count}")
            
            # 插入测试数据
            insert_sql = """
            INSERT INTO devicepriorityconfig (device, priority, from_time, end_time, refresh_time, user)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            test_data = (
                'TEST_DEVICE_FIX',
                '1',
                datetime.now(),
                datetime.now(),
                datetime.now(),
                'test_user'
            )
            
            cursor.execute(insert_sql, test_data)
            conn.commit()
            
            # 检查插入后记录数
            cursor.execute("SELECT COUNT(*) FROM devicepriorityconfig")
            after_count = cursor.fetchone()[0]
            print(f"插入后记录数: {after_count}")
            
            if after_count > before_count:
                print("✅ 直接插入成功！")
                
                # 删除测试数据
                cursor.execute("DELETE FROM devicepriorityconfig WHERE device = %s", ('TEST_DEVICE_FIX',))
                conn.commit()
                print("🗑️  删除测试数据")
            else:
                print("❌ 直接插入失败")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 直接插入测试失败: {e}")

def main():
    """主函数"""
    print("🔧 Excel导入功能修复工具")
    print(f"⏰ 修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 创建数据库表
    if create_tables_if_not_exist():
        # 2. 测试Excel文件
        test_excel_files()
        
        # 3. 测试直接插入
        test_direct_insert()
        
        print("\n" + "=" * 60)
        print("🎯 修复建议:")
        print("1. 数据库表已确保存在于aps_system数据库")
        print("2. 检查Excel文件格式和数据完整性")
        print("3. 验证API代码中的数据类型转换")
        print("4. 检查Flask应用的数据库绑定配置")
        print("5. 查看应用日志获取详细错误信息")
        
        print("\n🚀 下一步操作:")
        print("1. 重启Flask应用")
        print("2. 重新尝试Excel导入")
        print("3. 查看浏览器开发者工具的网络请求")
        print("4. 检查Flask应用日志")
    else:
        print("❌ 数据库表创建失败，请检查数据库连接")

if __name__ == "__main__":
    main()
