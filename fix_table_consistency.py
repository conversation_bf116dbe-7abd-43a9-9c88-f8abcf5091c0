#!/usr/bin/env python3
"""
修复表字段一致性问题
"""

import pymysql
import pandas as pd
import os
from pathlib import Path

def fix_table_consistency():
    """修复表字段一致性问题"""
    
    try:
        conn = pymysql.connect(
            host='127.0.0.1', 
            user='root', 
            password='WWWwww123!', 
            database='aps'
        )
        cursor = conn.cursor()
        
        print("🔧 开始修复表字段一致性问题...")
        print("=" * 80)
        
        # 1. 修复devicepriorityconfig表字段大小写问题
        print("\n📋 修复devicepriorityconfig表字段...")
        
        # 检查当前表结构
        cursor.execute("DESCRIBE devicepriorityconfig")
        current_columns = cursor.fetchall()
        print("当前字段:")
        for col in current_columns:
            print(f"  - {col[0]}: {col[1]}")
        
        # 字段映射：当前字段名 -> 标准字段名
        field_mapping = {
            'DEVICE': 'device',
            'STAGE': 'stage', 
            'HANDLER_CONFIG': 'handler_config',
            'HANDLER_PRIORITY': 'handler_priority',
            'SETUP_QTY': 'setup_qty',
            'PRIORITY': 'priority',
            'Price': 'price',
            'FROM_TIME': 'from_time',
            'END_TIME': 'end_time',
            'REFRESH_TIME': 'refresh_time',
            'USER': 'user'
        }
        
        # 重命名字段为标准格式
        print("\n🔄 重命名字段为标准格式...")
        for old_name, new_name in field_mapping.items():
            try:
                alter_sql = f"ALTER TABLE devicepriorityconfig CHANGE `{old_name}` `{new_name}` TEXT"
                cursor.execute(alter_sql)
                print(f"  ✅ {old_name} -> {new_name}")
            except Exception as e:
                print(f"  ❌ 重命名 {old_name} 失败: {e}")
        
        # 2. 检查其他表的字段一致性
        print(f"\n📋 检查其他表的字段一致性...")
        
        # 重点检查的表
        tables_to_check = [
            'ct', 'wip_lot', 'eqp_status', 'et_recipe_file', 
            'et_ft_test_spec', 'et_uph_eqp', 'et_wait_lot', 
            'lotpriorityconfig', 'tcc_inv', 'lotprioritydone'
        ]
        
        for table_name in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if cursor.fetchone():
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                # 检查是否有混合大小写的字段
                mixed_case_fields = []
                for col in columns:
                    field_name = col[0]
                    if field_name not in ['id', 'created_at', 'updated_at']:
                        # 检查是否有小写字母和大写字母混合
                        if field_name != field_name.upper() and field_name != field_name.lower():
                            mixed_case_fields.append(field_name)
                
                if mixed_case_fields:
                    print(f"  ⚠️  {table_name} 有混合大小写字段: {mixed_case_fields}")
                else:
                    print(f"  ✅ {table_name} 字段格式一致")
        
        # 3. 验证Excel文件字段
        print(f"\n📄 验证Excel文件字段...")
        excel_dir = Path("Excellist2025.06.05")
        if excel_dir.exists():
            # 检查devicepriorityconfig.xlsx
            device_excel = excel_dir / "devicepriorityconfig.xlsx"
            if device_excel.exists():
                try:
                    df = pd.read_excel(device_excel, engine='openpyxl')
                    excel_columns = list(df.columns)
                    print(f"  📄 devicepriorityconfig.xlsx 字段: {excel_columns}")
                    
                    # 检查是否与修复后的数据库字段匹配
                    expected_fields = list(field_mapping.values())
                    missing_in_excel = set(expected_fields) - set(excel_columns)
                    extra_in_excel = set(excel_columns) - set(expected_fields)
                    
                    if missing_in_excel:
                        print(f"    ❌ Excel缺少字段: {missing_in_excel}")
                    if extra_in_excel:
                        print(f"    ⚠️  Excel额外字段: {extra_in_excel}")
                    if not missing_in_excel and not extra_in_excel:
                        print(f"    ✅ Excel字段与数据库完全匹配")
                        
                except Exception as e:
                    print(f"    ❌ 读取Excel失败: {e}")
        
        # 4. 更新记录条数统计
        print(f"\n📊 更新记录条数统计...")
        
        key_tables = [
            'ct', 'devicepriorityconfig', 'wip_lot', 'eqp_status', 
            'et_recipe_file', 'et_ft_test_spec', 'et_uph_eqp', 
            'et_wait_lot', 'lotpriorityconfig', 'tcc_inv', 'lotprioritydone'
        ]
        
        record_counts = {}
        for table_name in key_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if cursor.fetchone():
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                record_counts[table_name] = count
                print(f"  📋 {table_name}: {count} 条记录")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ 表字段一致性修复完成！")
        print(f"📊 记录条数统计: {record_counts}")
        
        return record_counts
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return {}

if __name__ == '__main__':
    fix_table_consistency()
