#!/usr/bin/env python3
"""
Excel数据导入到MySQL数据库工具
100% MySQL模式，完全替代SQLite版本

Author: AI Assistant  
Date: 2025-06-12
"""

import os
import sys
import pandas as pd
import pymysql
import json
import time
from datetime import datetime
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_mysql.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        # 优先从环境变量获取MySQL配置，如果没有则使用默认配置
        mysql_config = {
            'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', 'WWWwww123!'),
            'database': os.getenv('MYSQL_DATABASE', 'aps'),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }

        # 如果环境变量中没有密码，尝试使用默认密码
        if not mysql_config['password']:
            mysql_config['password'] = 'WWWwww123!'

        conn = pymysql.connect(**mysql_config)
        logger.info(f"成功连接到MySQL数据库: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        return conn

    except Exception as e:
        logger.error(f"连接MySQL数据库失败: {e}")
        # 记录详细的连接配置（不包含密码）
        safe_config = {k: v for k, v in mysql_config.items() if k != 'password'}
        safe_config['password'] = '***' if mysql_config.get('password') else 'None'
        logger.error(f"连接配置: {safe_config}")
        raise

def update_progress(percent, message, current_file=None, files_processed=0, total_files=0, error=False):
    """更新导入进度到文件"""
    try:
        # 获取Flask应用的instance路径
        instance_path = os.path.join(os.path.dirname(__file__), 'instance')
        if not os.path.exists(instance_path):
            os.makedirs(instance_path)
        
        progress_file = os.path.join(instance_path, 'import_progress.json')
        
        progress_data = {
            'percent': percent,
            'message': message,
            'files_processed': files_processed,
            'total_files': total_files,
            'timestamp': time.time()
        }
        
        if current_file:
            progress_data['current_file'] = current_file
            
        if error:
            progress_data['error'] = True
        
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"进度更新: {percent}% - {message}")
        
    except Exception as e:
        logger.error(f"更新进度失败: {e}")

def create_table_from_dataframe(conn, table_name, df):
    """根据DataFrame创建MySQL表"""
    cursor = conn.cursor()

    # 删除已存在的表
    cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")

    # 生成列定义
    columns = []
    existing_columns = set()

    for col in df.columns:
        col_name = str(col).replace(' ', '_').replace('-', '_')
        # 检查是否与系统列冲突
        if col_name.lower() in ['id', 'created_at', 'updated_at']:
            col_name = f"data_{col_name}"

        # 确保列名唯一
        original_col_name = col_name
        counter = 1
        while col_name.lower() in existing_columns:
            col_name = f"{original_col_name}_{counter}"
            counter += 1

        existing_columns.add(col_name.lower())
        columns.append(f"`{col_name}` TEXT")

    # 创建表SQL
    create_sql = f"""
    CREATE TABLE `{table_name}` (
        id INT AUTO_INCREMENT PRIMARY KEY,
        {', '.join(columns)},
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    """

    cursor.execute(create_sql)
    logger.info(f"创建表 {table_name} 成功")

def insert_dataframe_to_mysql(conn, table_name, df, processed_records_before, total_records_count):
    """将DataFrame数据插入MySQL表"""
    cursor = conn.cursor()

    if df.empty:
        logger.warning(f"DataFrame为空，跳过表 {table_name}")
        return 0

    # 处理列名（与创建表时保持一致）
    columns = []
    existing_columns = set()

    for col in df.columns:
        col_name = str(col).replace(' ', '_').replace('-', '_')
        # 检查是否与系统列冲突
        if col_name.lower() in ['id', 'created_at', 'updated_at']:
            col_name = f"data_{col_name}"

        # 确保列名唯一
        original_col_name = col_name
        counter = 1
        while col_name.lower() in existing_columns:
            col_name = f"{original_col_name}_{counter}"
            counter += 1

        existing_columns.add(col_name.lower())
        columns.append(col_name)

    placeholders = ', '.join(['%s'] * len(columns))
    column_names = ', '.join([f"`{col}`" for col in columns])

    insert_sql = f"INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})"
    
    # 批量插入数据
    inserted_count = 0
    batch_size = 1000
    total_rows = len(df)
    
    for i in range(0, total_rows, batch_size):
        batch_df = df.iloc[i:i+batch_size]
        
        # 准备数据
        values = []
        for _, row in batch_df.iterrows():
            row_values = []
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    row_values.append(None)
                else:
                    row_values.append(str(value))
            values.append(tuple(row_values))
        
        # 执行批量插入
        cursor.executemany(insert_sql, values)
        inserted_count += len(values)
        
        # 基于总记录数计算进度（15%是前期准备，85%是实际导入）
        current_processed = processed_records_before + inserted_count
        overall_progress = 15 + (current_processed / total_records_count) * 85
        
        update_progress(
            percent=min(overall_progress, 99),
            message=f"正在导入表 {table_name}: {inserted_count}/{total_rows} 条记录",
            current_file=table_name,
            files_processed=current_processed,
            total_files=total_records_count
        )
        
        logger.info(f"表 {table_name}: 已插入 {inserted_count}/{total_rows} 条记录")
    
    return inserted_count

def process_excel_file(file_path, conn, file_index, total_files, processed_records_before, total_records_count):
    """处理单个Excel文件"""
    try:
        logger.info(f"开始处理文件: {file_path}")
        
        # 读取Excel文件的所有sheet
        excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
        
        processed_sheets = []
        total_records = 0
        current_processed = processed_records_before
        
        for sheet_index, (sheet_name, df) in enumerate(excel_data.items()):
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空，跳过")
                continue
            
            # 使用正确的表名映射，避免重复表名
            TABLE_MAPPING = {
                'CT': 'ct',
                'WIP_LOT': 'wip_lot',
                'ET_FT_TEST_SPEC': 'et_ft_test_spec',
                'ET_UPH_EQP': 'et_uph_eqp',
                'ET_RECIPE_FILE': 'et_recipe_file',
                'ET_WAIT_LOT': 'et_wait_lot',
                'EQP_STATUS': 'eqp_status'
            }

            # 根据文件名推断表名的映射
            FILE_TO_TABLE_MAPPING = {
                'devicepriorityconfig.xlsx': 'devicepriorityconfig',
                'lotpriorityconfig.xlsx': 'lotpriorityconfig',
                'TCC_INV.xlsx': 'tcc_inv',
                'lotprioritydone.xlsx': 'lotprioritydone'
            }
            
            # 优先使用工作表名称映射
            file_name = os.path.basename(file_path)
            logger.info(f"处理文件: {file_name}, 工作表: {sheet_name}")

            if sheet_name in TABLE_MAPPING:
                table_name = TABLE_MAPPING[sheet_name]
                logger.info(f"使用工作表映射: {sheet_name} -> {table_name}")
            else:
                # 检查文件名映射
                if file_name in FILE_TO_TABLE_MAPPING:
                    table_name = FILE_TO_TABLE_MAPPING[file_name]
                    logger.info(f"使用文件名映射: {file_name} -> {table_name}")
                else:
                    # 如果都不在映射中，使用工作表名称但避免重复
                    if sheet_name.lower() == 'sheet1':
                        # 对于默认的Sheet1，使用文件名作为表名
                        base_name = os.path.splitext(file_name)[0].lower()
                        table_name = base_name
                        logger.info(f"使用文件名作为表名: {file_name} -> {table_name}")
                    else:
                        table_name = sheet_name.lower()
                        logger.info(f"使用工作表名: {sheet_name} -> {table_name}")

            table_name = table_name.replace(' ', '_').replace('-', '_')
            logger.info(f"最终表名: {table_name}")
            
            # 创建表并插入数据
            create_table_from_dataframe(conn, table_name, df)
            record_count = insert_dataframe_to_mysql(conn, table_name, df, current_processed, total_records_count)
            
            processed_sheets.append({
                'sheet_name': sheet_name,
                'table_name': table_name,
                'record_count': record_count
            })
            total_records += record_count
            current_processed += record_count
        
        conn.commit()
        logger.info(f"文件 {file_path} 处理完成，共处理 {total_records} 条记录")
        
        return {
            'success': True,
            'file': Path(file_path).name,
            'table': f"{Path(file_path).stem}",
            'records': total_records,
            'file_path': file_path,
            'sheets': processed_sheets,
            'total_records': total_records
        }
        
    except Exception as e:
        logger.error(f"处理文件 {file_path} 失败: {e}")
        conn.rollback()
        return {
            'success': False,
            'file': Path(file_path).name,
            'file_path': file_path,
            'error': str(e)
        }

def import_excel_files(directory_path, mysql_config=None):
    """
    导入目录中的所有Excel文件到MySQL数据库
    
    Args:
        directory_path: Excel文件目录路径
        mysql_config: MySQL配置字典（可选，使用环境变量作为默认值）
    
    Returns:
        tuple: (success, result)
    """
    start_time = time.time()
    
    try:
        # 初始化进度
        update_progress(0, "正在扫描Excel文件...")
        
        # 检查目录是否存在
        if not os.path.exists(directory_path):
            return False, {'error': f'目录不存在: {directory_path}'}
        
        # 获取所有Excel文件
        excel_files = []
        for ext in ['*.xlsx', '*.xls']:
            excel_files.extend(Path(directory_path).glob(ext))
        
        if not excel_files:
            return False, {'error': f'目录中没有找到Excel文件: {directory_path}'}
        
        logger.info(f"找到 {len(excel_files)} 个Excel文件")
        
        # 更新进度
        update_progress(5, f"找到 {len(excel_files)} 个Excel文件，正在分析文件大小...")
        
        # 第一步：预扫描所有文件，计算总记录数
        file_info_list = []
        total_records_count = 0
        
        for file_path in excel_files:
            try:
                # 快速读取文件获取行数
                excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
                file_records = 0
                for sheet_name, df in excel_data.items():
                    if not df.empty:
                        file_records += len(df)
                
                file_info_list.append({
                    'path': file_path,
                    'records': file_records,
                    'name': Path(file_path).name
                })
                total_records_count += file_records
                logger.info(f"文件 {Path(file_path).name}: {file_records} 条记录")
            except Exception as e:
                logger.error(f"预扫描文件 {file_path} 失败: {e}")
                file_info_list.append({
                    'path': file_path,
                    'records': 0,
                    'name': Path(file_path).name
                })
        
        logger.info(f"总计需要导入 {total_records_count} 条记录")
        
        # 更新进度
        update_progress(10, f"分析完成，总计 {total_records_count} 条记录，正在连接数据库...")
        
        # 连接MySQL数据库
        conn = get_mysql_connection()
        
        # 更新进度
        update_progress(15, "数据库连接成功，开始处理文件...")
        
        # 处理所有文件
        processed_files = []
        failed_files = []
        processed_records = 0
        
        for file_index, file_info in enumerate(file_info_list, 1):
            result = process_excel_file(
                str(file_info['path']), 
                conn, 
                file_index, 
                len(file_info_list),
                processed_records,
                total_records_count
            )
            
            if result['success']:
                processed_files.append(result)
                processed_records += result['total_records']
            else:
                failed_files.append(result)
        
        conn.close()
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 最终进度更新
        update_progress(100, "导入完成！")
        
        # 准备返回结果
        result = {
            'message': f'导入完成，成功处理 {len(processed_files)} 个文件，失败 {len(failed_files)} 个',
            'processed_files': processed_files,
            'failed_files': failed_files,
            'total_files': len(file_info_list),
            'failed_count': len(failed_files),
            'total_records': processed_records,
            'processing_time': round(processing_time, 2)
        }
        
        if failed_files:
            result['details'] = f'部分文件处理失败，请检查日志'
            return False, result
        else:
            result['details'] = f'所有文件处理成功，共导入 {processed_records} 条记录'
            return True, result
        
    except Exception as e:
        logger.error(f"导入过程失败: {e}")
        update_progress(0, f"导入失败: {str(e)}", error=True)
        return False, {'error': str(e), 'message': '导入过程失败'}

def import_from_directory(directory_path, db_path=None):
    """
    兼容性函数，保持与原SQLite版本相同的接口
    
    Args:
        directory_path: Excel文件目录路径
        db_path: 数据库路径（忽略，MySQL模式下不使用）
    
    Returns:
        tuple: (success, result)
    """
    return import_excel_files(directory_path)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("用法: python import_excel_to_mysql.py <Excel目录路径>")
        sys.exit(1)
    
    directory_path = sys.argv[1]
    success, result = import_excel_files(directory_path)
    
    if success:
        print(f"✅ 导入成功: {result['message']}")
        print(f"   处理时间: {result['processing_time']} 秒")
        print(f"   总记录数: {result['total_records']}")
    else:
        print(f"❌ 导入失败: {result.get('error', '未知错误')}")
        sys.exit(1) 