#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建lotprioritydone表，使其完全匹配Excel文件结构
"""
import pymysql
import logging
import json
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MySQL连接配置
MYSQL_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'WWWwww123!',
    'database': 'aps',
    'port': 3306,
    'charset': 'utf8mb4'
}

def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("✓ 成功连接到MySQL数据库")
        return conn
    except pymysql.Error as e:
        logger.error(f"✗ 连接MySQL数据库失败: {e}")
        return None

def backup_existing_data(conn):
    """备份现有数据"""
    logger.info("\n备份现有数据...")
    logger.info("=" * 60)
    
    cursor = conn.cursor()
    backup_data = []
    
    try:
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'lotprioritydone'")
        if cursor.fetchone():
            # 获取现有数据
            cursor.execute("SELECT * FROM lotprioritydone")
            backup_data = cursor.fetchall()
            
            # 获取字段名
            cursor.execute("DESCRIBE lotprioritydone")
            columns = cursor.fetchall()
            field_names = [col[0] for col in columns]
            
            logger.info(f"✓ 备份了 {len(backup_data)} 条记录")
            logger.info(f"✓ 原表有 {len(field_names)} 个字段")
            
            return backup_data, field_names
        else:
            logger.info("表不存在，无需备份")
            return [], []
            
    except pymysql.Error as e:
        logger.error(f"备份失败: {e}")
        return [], []
    finally:
        cursor.close()

def create_new_table_structure(conn):
    """创建新的表结构"""
    logger.info("\n创建新的表结构...")
    logger.info("=" * 60)
    
    # Excel字段顺序（严格按照Excel文件的顺序）
    excel_fields = [
        ("PRIORITY", "VARCHAR(50)", "优先级"),
        ("HANDLER_ID", "VARCHAR(100)", "操作员ID"),
        ("LOT_ID", "VARCHAR(100)", "批次ID"),
        ("LOT_TYPE", "VARCHAR(50)", "批次类型"),
        ("GOOD_QTY", "INT", "良品数量"),
        ("PROD_ID", "VARCHAR(100)", "产品ID"),
        ("DEVICE", "VARCHAR(100)", "设备"),
        ("CHIP_ID", "VARCHAR(100)", "芯片ID"),
        ("PKG_PN", "VARCHAR(100)", "封装料号"),
        ("PO_ID", "VARCHAR(100)", "PO_ID"),
        ("STAGE", "VARCHAR(50)", "阶段"),
        ("WIP_STATE", "VARCHAR(50)", "WIP_STATE"),
        ("PROC_STATE", "VARCHAR(50)", "PROC_STATE"),
        ("HOLD_STATE", "VARCHAR(50)", "HOLD_STATE"),
        ("FLOW_ID", "VARCHAR(100)", "FLOW_ID"),
        ("FLOW_VER", "VARCHAR(50)", "流程版本"),
        ("RELEASE_TIME", "DATETIME", "发布时间"),
        ("FAC_ID", "VARCHAR(50)", "工厂ID"),
        ("CREATE_TIME", "DATETIME", "创建时间"),
        ("SCHDULED_TIME", "DATETIME", "计划时间")  # 注意：Excel中是"SCHDULED TIME"，转换为合法字段名
    ]
    
    cursor = conn.cursor()
    
    try:
        # 删除旧表
        cursor.execute("DROP TABLE IF EXISTS lotprioritydone")
        logger.info("✓ 删除旧表")
        
        # 创建新表的SQL
        create_sql = """
        CREATE TABLE lotprioritydone (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
        """
        
        # 添加Excel字段
        field_definitions = []
        for field_name, field_type, comment in excel_fields:
            null_option = "NULL" if field_type == "DATETIME" else "NULL"
            field_definitions.append(f"    {field_name} {field_type} {null_option} COMMENT '{comment}'")
        
        create_sql += ",\n".join(field_definitions)
        
        # 添加系统字段
        create_sql += """,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批次优先级完成表';
        """
        
        # 执行创建表SQL
        cursor.execute(create_sql)
        logger.info("✓ 创建新表结构")
        
        # 添加索引
        indexes = [
            "CREATE INDEX idx_lot_id ON lotprioritydone(LOT_ID)",
            "CREATE INDEX idx_device ON lotprioritydone(DEVICE)",
            "CREATE INDEX idx_stage ON lotprioritydone(STAGE)",
            "CREATE INDEX idx_priority ON lotprioritydone(PRIORITY)",
            "CREATE INDEX idx_release_time ON lotprioritydone(RELEASE_TIME)",
            "CREATE INDEX idx_create_time ON lotprioritydone(CREATE_TIME)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        logger.info(f"✓ 创建了 {len(indexes)} 个索引")
        
        conn.commit()
        
        return excel_fields
        
    except pymysql.Error as e:
        logger.error(f"✗ 创建表结构失败: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()

def migrate_data(conn, backup_data, old_field_names, new_fields):
    """迁移数据到新表"""
    if not backup_data:
        logger.info("无数据需要迁移")
        return
    
    logger.info(f"\n迁移数据到新表...")
    logger.info("=" * 60)
    
    cursor = conn.cursor()
    
    try:
        # 创建字段映射
        field_mapping = {}
        new_field_names = [field[0] for field in new_fields]
        
        for old_field in old_field_names:
            if old_field.upper() in new_field_names:
                field_mapping[old_field] = old_field.upper()
            elif old_field == 'lot_id':
                field_mapping[old_field] = 'LOT_ID'
            elif old_field == 'device':
                field_mapping[old_field] = 'DEVICE'
            elif old_field == 'stage':
                field_mapping[old_field] = 'STAGE'
            elif old_field == 'priority':
                field_mapping[old_field] = 'PRIORITY'
            elif old_field == 'quantity':
                field_mapping[old_field] = 'GOOD_QTY'
            elif old_field == 'scheduled_time':
                field_mapping[old_field] = 'SCHDULED_TIME'
        
        logger.info(f"字段映射: {field_mapping}")
        
        # 迁移数据
        migrated_count = 0
        for row in backup_data:
            # 构建插入数据
            insert_fields = []
            insert_values = []
            
            for i, old_field in enumerate(old_field_names):
                if old_field in field_mapping:
                    new_field = field_mapping[old_field]
                    insert_fields.append(new_field)
                    insert_values.append(row[i])
            
            if insert_fields:
                placeholders = ', '.join(['%s'] * len(insert_values))
                insert_sql = f"""
                INSERT INTO lotprioritydone ({', '.join(insert_fields)}) 
                VALUES ({placeholders})
                """
                cursor.execute(insert_sql, insert_values)
                migrated_count += 1
        
        conn.commit()
        logger.info(f"✓ 成功迁移 {migrated_count} 条记录")
        
    except pymysql.Error as e:
        logger.error(f"✗ 数据迁移失败: {e}")
        conn.rollback()
    finally:
        cursor.close()

def verify_new_structure(conn):
    """验证新表结构"""
    logger.info("\n验证新表结构...")
    logger.info("=" * 60)
    
    cursor = conn.cursor()
    
    try:
        # 检查表结构
        cursor.execute("DESCRIBE lotprioritydone")
        columns = cursor.fetchall()
        
        logger.info(f"✓ 新表字段数量: {len(columns)}")
        
        excel_order = [
            "id", "PRIORITY", "HANDLER_ID", "LOT_ID", "LOT_TYPE", "GOOD_QTY",
            "PROD_ID", "DEVICE", "CHIP_ID", "PKG_PN", "PO_ID", "STAGE",
            "WIP_STATE", "PROC_STATE", "HOLD_STATE", "FLOW_ID", "FLOW_VER",
            "RELEASE_TIME", "FAC_ID", "CREATE_TIME", "SCHDULED_TIME",
            "created_at", "updated_at"
        ]
        
        actual_fields = [col[0] for col in columns]
        
        logger.info("\n字段顺序验证:")
        for i, (expected, actual) in enumerate(zip(excel_order, actual_fields), 1):
            status = "✓" if expected == actual else "✗"
            logger.info(f"{status} {i:2d}. {actual:<20} (期望: {expected})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM lotprioritydone")
        count = cursor.fetchone()[0]
        logger.info(f"\n📊 数据行数: {count}")
        
        return actual_fields
        
    except pymysql.Error as e:
        logger.error(f"验证失败: {e}")
        return []
    finally:
        cursor.close()

def update_frontend_config(new_fields):
    """更新前端配置"""
    logger.info("\n更新前端配置...")
    logger.info("=" * 60)
    
    try:
        # 创建前端表头配置
        frontend_headers = []
        
        # 添加ID字段（通常不显示）
        frontend_headers.append({
            "field": "id",
            "title": "ID",
            "visible": False,
            "sortable": True,
            "width": 80
        })
        
        # 按Excel顺序添加字段
        excel_field_titles = {
            "PRIORITY": "优先级",
            "HANDLER_ID": "操作员ID",
            "LOT_ID": "批次ID",
            "LOT_TYPE": "批次类型",
            "GOOD_QTY": "良品数量",
            "PROD_ID": "产品ID",
            "DEVICE": "设备",
            "CHIP_ID": "芯片ID",
            "PKG_PN": "封装料号",
            "PO_ID": "PO_ID",
            "STAGE": "阶段",
            "WIP_STATE": "WIP状态",
            "PROC_STATE": "工艺状态",
            "HOLD_STATE": "暂停状态",
            "FLOW_ID": "流程ID",
            "FLOW_VER": "流程版本",
            "RELEASE_TIME": "发布时间",
            "FAC_ID": "工厂ID",
            "CREATE_TIME": "创建时间",
            "SCHDULED_TIME": "计划时间"
        }
        
        for field_name in excel_field_titles.keys():
            frontend_headers.append({
                "field": field_name,
                "title": excel_field_titles[field_name],
                "visible": True,
                "sortable": True,
                "width": 120 if "TIME" in field_name else 100
            })
        
        # 添加系统字段
        frontend_headers.extend([
            {
                "field": "created_at",
                "title": "记录创建时间",
                "visible": False,
                "sortable": True,
                "width": 150
            },
            {
                "field": "updated_at",
                "title": "记录更新时间",
                "visible": False,
                "sortable": True,
                "width": 150
            }
        ])
        
        # 更新JSON配置
        config_file = Path('table_headers_config.json')
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        config['lotprioritydone'] = frontend_headers
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 更新JavaScript配置
        js_content = f"""/**
 * 表头配置文件
 * 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 */

window.tableHeaders = {{
    lotprioritydone: {json.dumps(frontend_headers, ensure_ascii=False, indent=4)}
}};
"""
        
        js_file = Path('app/static/js/table_headers_config.js')
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        logger.info("✓ 前端配置已更新")
        logger.info(f"✓ 配置了 {len(frontend_headers)} 个字段")
        
    except Exception as e:
        logger.error(f"✗ 更新前端配置失败: {e}")

def main():
    """主函数"""
    logger.info("开始重新创建lotprioritydone表...")
    logger.info("=" * 80)
    
    # 连接数据库
    conn = get_mysql_connection()
    if not conn:
        logger.error("无法连接到MySQL数据库，程序退出")
        return
    
    try:
        # 1. 备份现有数据
        backup_data, old_field_names = backup_existing_data(conn)
        
        # 2. 创建新表结构
        new_fields = create_new_table_structure(conn)
        
        # 3. 迁移数据
        migrate_data(conn, backup_data, old_field_names, new_fields)
        
        # 4. 验证新结构
        actual_fields = verify_new_structure(conn)
        
        # 5. 更新前端配置
        update_frontend_config(actual_fields)
        
        logger.info("\n" + "=" * 80)
        logger.info("✅ lotprioritydone表重建完成")
        logger.info("✅ 表结构完全匹配Excel文件")
        logger.info("✅ 字段顺序与Excel完全一致")
        logger.info("✅ 前端配置已更新")
        
    except Exception as e:
        logger.error(f"重建过程中发生错误: {e}")
        conn.rollback()
    finally:
        conn.close()
        logger.info("✓ 数据库连接已关闭")

if __name__ == "__main__":
    main() 