import requests
import sys

def test_simple():
    """简单测试API v3页面是否正常工作"""
    try:
        # 测试基础API
        response = requests.get('http://localhost:5000/api/v3/tables', timeout=5)
        print(f"API基础测试: HTTP {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"支持表数量: {len(data.get('tables', []))}")
        
        # 测试页面访问
        response = requests.get('http://localhost:5000/api/v3/page/eqp_status', timeout=5)
        print(f"页面访问测试: HTTP {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            if 'API v3' in content:
                print("✅ 页面包含API v3标识")
            if 'base_resource.html' in content or 'bootstrap' in content:
                print("✅ 页面使用了正确的模板")
            if '设备状态管理' in content:
                print("✅ 页面标题正确")
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            if '500' in str(response.status_code):
                print("服务器内部错误，可能是模板渲染问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_simple() 