import requests
import json

def test_all_11_tables():
    """测试所有11个表的API v3页面"""
    session = requests.Session()
    
    # 登录admin账号
    print("🔐 正在登录admin账号...")
    login_data = {'username': 'admin', 'password': 'admin'}
    login_response = session.post('http://localhost:5000/auth/login', data=login_data, timeout=10)
    
    if login_response.status_code != 200:
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功!")
    
    # 根据图片显示的11个Excel文件对应的表
    all_tables = [
        {'name': 'ct', 'title': '产品周期管理', 'file': 'CT.xlsx'},
        {'name': 'devicepriorityconfig', 'title': '设备优先级配置', 'file': 'devicepriorityconfig.xlsx'},
        {'name': 'eqp_status', 'title': '设备状态管理', 'file': 'EQP_STATUS.xlsx'},
        {'name': 'et_ft_test_spec', 'title': '测试规格管理', 'file': 'ET_FT_TEST_SPEC.xlsx'},
        {'name': 'et_recipe_file', 'title': '配方文件管理', 'file': 'ET_RECIPE_FILE.xlsx'},
        {'name': 'et_uph_eqp', 'title': 'UPH设备管理', 'file': 'ET_UPH_EQP.xlsx'},
        {'name': 'et_wait_lot', 'title': '等待批次管理', 'file': 'ET_WAIT_LOT.xlsx'},
        {'name': 'lotpriorityconfig', 'title': '批次优先级配置', 'file': 'lotpriorityconfig.xlsx'},
        {'name': 'lotprioritydone', 'title': '已完成批次优先级', 'file': 'lotprioritydone.xlsx'},
        {'name': 'tcc_inv', 'title': 'TCC库存管理', 'file': 'TCC_INV.xlsx'},
        {'name': 'wip_lot', 'title': 'WIP批次管理', 'file': 'WIP_LOT.xlsx'}
    ]
    
    print(f"\n📊 开始测试所有 {len(all_tables)} 个表的API v3页面...")
    
    # 首先获取支持的表列表
    print("\n🔍 获取API v3支持的表列表...")
    tables_response = session.get('http://localhost:5000/api/v3/tables', timeout=10)
    
    supported_tables = []
    if tables_response.status_code == 200:
        tables_data = tables_response.json()
        if tables_data.get('success'):
            supported_table_names = [t['table_name'] for t in tables_data.get('tables', [])]
            print(f"✅ API v3支持 {len(supported_table_names)} 个表")
        else:
            print(f"❌ 获取支持表列表失败: {tables_data.get('error')}")
            return
    else:
        print(f"❌ 获取支持表列表请求失败: {tables_response.status_code}")
        return
    
    results = []
    successful_count = 0
    failed_count = 0
    
    for i, table in enumerate(all_tables, 1):
        table_name = table['name']
        expected_title = table['title']
        excel_file = table['file']
        
        print(f"\n🔍 [{i}/{len(all_tables)}] 测试 {expected_title} ({table_name}) - 来源: {excel_file}")
        
        # 检查表是否被API v3支持
        if table_name not in supported_table_names:
            print(f"   ⚠️  表 {table_name} 不在API v3支持列表中")
            result = {
                'table_name': table_name,
                'title': expected_title,
                'excel_file': excel_file,
                'supported': False,
                'reason': '不在API v3支持列表'
            }
            results.append(result)
            failed_count += 1
            continue
        
        try:
            # 测试页面访问
            page_url = f'http://localhost:5000/api/v3/page/{table_name}'
            page_response = session.get(page_url, timeout=10)
            
            page_success = page_response.status_code == 200
            has_v3_badge = 'API v3' in page_response.text if page_success else False
            
            # 测试API数据获取
            api_url = f'http://localhost:5000/api/v3/tables/{table_name}/data?page=1&per_page=3'
            api_response = session.get(api_url, timeout=10)
            
            api_success = False
            record_count = 0
            column_count = 0
            total_records = 0
            
            if api_response.status_code == 200:
                api_data = api_response.json()
                if api_data.get('success'):
                    api_success = True
                    record_count = len(api_data.get('data', []))
                    column_count = len(api_data.get('columns', []))
                    total_records = api_data.get('total', 0)
            
            # 测试字段验证
            validate_url = f'http://localhost:5000/api/v3/tables/{table_name}/validate'
            validate_response = session.get(validate_url, timeout=10)
            
            validate_success = False
            match_rate = 0
            
            if validate_response.status_code == 200:
                validate_data = validate_response.json()
                if validate_data.get('success'):
                    validate_success = True
                    match_rate = validate_data.get('match_rate', 0)
            
            # 判断整体成功
            overall_success = all([page_success, has_v3_badge, api_success, validate_success])
            
            # 记录结果
            result = {
                'table_name': table_name,
                'title': expected_title,
                'excel_file': excel_file,
                'supported': True,
                'page_success': page_success,
                'has_v3_badge': has_v3_badge,
                'api_success': api_success,
                'record_count': record_count,
                'column_count': column_count,
                'total_records': total_records,
                'validate_success': validate_success,
                'match_rate': match_rate,
                'overall_success': overall_success
            }
            
            results.append(result)
            
            # 输出测试结果
            status = "✅" if overall_success else "❌"
            print(f"   {status} 页面: {page_success} | API: {api_success} | 验证: {validate_success}")
            print(f"   📊 数据: {record_count}/{total_records}条记录, {column_count}个字段, {match_rate}%匹配率")
            
            if overall_success:
                successful_count += 1
            else:
                failed_count += 1
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            results.append({
                'table_name': table_name,
                'title': expected_title,
                'excel_file': excel_file,
                'error': str(e)
            })
            failed_count += 1
    
    # 输出详细总结
    print(f"\n📈 完整测试总结:")
    print("=" * 100)
    print(f"📊 总计: {len(all_tables)} 个表")
    print(f"✅ 成功: {successful_count} 个表")
    print(f"❌ 失败: {failed_count} 个表")
    print(f"📈 成功率: {successful_count/len(all_tables)*100:.1f}%")
    print()
    
    # 按类别分组显示结果
    successful_tables = [r for r in results if r.get('overall_success')]
    failed_tables = [r for r in results if not r.get('overall_success') and not r.get('error')]
    error_tables = [r for r in results if r.get('error')]
    unsupported_tables = [r for r in results if not r.get('supported')]
    
    if successful_tables:
        print("✅ 成功的表:")
        for result in successful_tables:
            print(f"   ✅ {result['title']}: {result['total_records']}条记录, {result['column_count']}字段, {result['match_rate']}%匹配")
    
    if failed_tables:
        print(f"\n❌ 部分功能失败的表:")
        for result in failed_tables:
            issues = []
            if not result.get('page_success'): issues.append("页面访问失败")
            if not result.get('api_success'): issues.append("API获取失败")
            if not result.get('validate_success'): issues.append("字段验证失败")
            print(f"   ❌ {result['title']}: {', '.join(issues)}")
    
    if unsupported_tables:
        print(f"\n⚠️  不支持的表:")
        for result in unsupported_tables:
            print(f"   ⚠️  {result['title']} ({result['table_name']}) - {result.get('reason', '未知原因')}")
    
    if error_tables:
        print(f"\n💥 出现异常的表:")
        for result in error_tables:
            print(f"   💥 {result['title']}: {result['error']}")
    
    # 最终结论
    if successful_count == len(all_tables):
        print(f"\n🎉 恭喜！所有 {len(all_tables)} 个表的API v3功能完全正常！")
        print(f"🚀 API v3迁移第15步圆满完成，可以进入第16步实现CRUD功能！")
    elif successful_count >= len(all_tables) * 0.8:  # 80%以上成功
        print(f"\n🎊 很好！{successful_count}/{len(all_tables)} 个表成功，成功率达到 {successful_count/len(all_tables)*100:.1f}%")
        print(f"🔧 API v3迁移基本成功，可以开始第16步，同时修复剩余问题")
    else:
        print(f"\n⚠️  需要进一步调试剩余 {failed_count} 个表的问题")

if __name__ == "__main__":
    test_all_11_tables() 