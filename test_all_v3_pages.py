import requests
import json

def test_all_v3_pages():
    """测试所有主要的API v3页面"""
    session = requests.Session()
    
    # 登录admin账号
    print("🔐 正在登录admin账号...")
    login_data = {'username': 'admin', 'password': 'admin'}
    login_response = session.post('http://localhost:5000/auth/login', data=login_data, timeout=10)
    
    if login_response.status_code != 200:
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功!")
    
    # 测试的表列表
    test_tables = [
        {'name': 'eqp_status', 'title': '设备状态管理'},
        {'name': 'et_uph_eqp', 'title': 'UPH设备管理'},
        {'name': 'et_ft_test_spec', 'title': '测试规格管理'},
        {'name': 'ct', 'title': '产品周期管理'}
    ]
    
    print(f"\n📊 开始测试 {len(test_tables)} 个API v3页面...")
    
    results = []
    
    for table in test_tables:
        table_name = table['name']
        expected_title = table['title']
        
        print(f"\n🔍 测试 {expected_title} ({table_name})")
        
        try:
            # 测试页面访问
            page_url = f'http://localhost:5000/api/v3/page/{table_name}'
            page_response = session.get(page_url, timeout=10)
            
            page_success = page_response.status_code == 200
            has_v3_badge = 'API v3' in page_response.text if page_success else False
            has_title = expected_title in page_response.text if page_success else False
            
            # 测试API数据获取
            api_url = f'http://localhost:5000/api/v3/tables/{table_name}/data?page=1&per_page=5'
            api_response = session.get(api_url, timeout=10)
            
            api_success = False
            record_count = 0
            column_count = 0
            
            if api_response.status_code == 200:
                api_data = api_response.json()
                if api_data.get('success'):
                    api_success = True
                    record_count = len(api_data.get('data', []))
                    column_count = len(api_data.get('columns', []))
            
            # 测试字段验证
            validate_url = f'http://localhost:5000/api/v3/tables/{table_name}/validate'
            validate_response = session.get(validate_url, timeout=10)
            
            validate_success = False
            match_rate = 0
            
            if validate_response.status_code == 200:
                validate_data = validate_response.json()
                if validate_data.get('success'):
                    validate_success = True
                    match_rate = validate_data.get('match_rate', 0)
            
            # 记录结果
            result = {
                'table_name': table_name,
                'title': expected_title,
                'page_success': page_success,
                'has_v3_badge': has_v3_badge,
                'has_title': has_title,
                'api_success': api_success,
                'record_count': record_count,
                'column_count': column_count,
                'validate_success': validate_success,
                'match_rate': match_rate
            }
            
            results.append(result)
            
            # 输出测试结果
            status = "✅" if all([page_success, has_v3_badge, has_title, api_success, validate_success]) else "⚠️"
            print(f"   {status} 页面: {page_success}, API: {api_success}, 验证: {validate_success}")
            print(f"   📊 数据: {record_count}条记录, {column_count}个字段, {match_rate}%匹配率")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            results.append({
                'table_name': table_name,
                'title': expected_title,
                'error': str(e)
            })
    
    # 输出总结
    print(f"\n📈 测试总结:")
    print("=" * 80)
    
    successful_tables = [r for r in results if r.get('page_success') and r.get('api_success')]
    
    print(f"✅ 成功的表: {len(successful_tables)}/{len(test_tables)}")
    
    for result in results:
        if not result.get('error'):
            status_icon = "✅" if result.get('page_success') and result.get('api_success') else "❌"
            print(f"   {status_icon} {result['title']}: 页面({result['page_success']}) | API({result['api_success']}) | 验证({result['validate_success']})")
            print(f"      数据: {result['record_count']}条记录, {result['column_count']}字段, {result['match_rate']}%匹配")
        else:
            print(f"   ❌ {result['title']}: {result['error']}")
    
    if len(successful_tables) == len(test_tables):
        print(f"\n🎉 所有API v3页面测试成功！")
        print(f"🚀 API v3迁移第15步完全成功，可以进入第16步实现CRUD功能！")
    else:
        print(f"\n⚠️  部分页面需要进一步调试")

if __name__ == "__main__":
    test_all_v3_pages() 