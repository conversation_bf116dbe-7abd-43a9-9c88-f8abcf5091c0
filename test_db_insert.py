#!/usr/bin/env python3
"""
测试数据库插入功能
直接测试DevicePriorityConfig和LotPriorityConfig模型的插入
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_insert():
    """测试数据库插入"""
    try:
        from app import create_app, db
        from app.models import DevicePriorityConfig, LotPriorityConfig
        
        # 创建应用上下文
        app = create_app()
        
        with app.app_context():
            print("🔗 测试数据库连接和插入...")
            
            # 测试DevicePriorityConfig
            print("\n=== 测试 DevicePriorityConfig ===")
            
            # 检查现有记录数
            existing_count = DevicePriorityConfig.query.count()
            print(f"现有记录数: {existing_count}")
            
            # 创建测试记录
            test_device = DevicePriorityConfig(
                device='TEST_DEVICE_001',
                priority='1',
                from_time=datetime.now(),
                end_time=datetime.now(),
                refresh_time=datetime.now(),
                user='test_user'
            )
            
            print(f"创建测试记录: {test_device}")
            print(f"模型绑定: {DevicePriorityConfig.__bind_key__}")
            print(f"表名: {DevicePriorityConfig.__tablename__}")
            
            # 添加到会话
            db.session.add(test_device)
            print("✅ 添加到会话成功")
            
            # 提交事务
            try:
                db.session.commit()
                print("✅ 提交事务成功")
                
                # 验证插入
                new_count = DevicePriorityConfig.query.count()
                print(f"新记录数: {new_count}")
                
                if new_count > existing_count:
                    print("✅ 数据插入成功！")
                    
                    # 查询刚插入的记录
                    inserted_record = DevicePriorityConfig.query.filter_by(device='TEST_DEVICE_001').first()
                    if inserted_record:
                        print(f"✅ 查询到插入的记录: {inserted_record}")
                        print(f"   ID: {inserted_record.id}")
                        print(f"   DEVICE: {inserted_record.device}")
                        print(f"   PRIORITY: {inserted_record.priority}")
                        
                        # 删除测试记录
                        db.session.delete(inserted_record)
                        db.session.commit()
                        print("🗑️  删除测试记录")
                    else:
                        print("❌ 无法查询到插入的记录")
                else:
                    print("❌ 数据插入失败，记录数未增加")
                    
            except Exception as e:
                db.session.rollback()
                print(f"❌ 提交事务失败: {e}")
                
            # 测试LotPriorityConfig
            print("\n=== 测试 LotPriorityConfig ===")
            
            existing_count = LotPriorityConfig.query.count()
            print(f"现有记录数: {existing_count}")
            
            test_lot = LotPriorityConfig(
                device='TEST_DEVICE_002',
                stage='TEST_STAGE',
                priority='2',
                refresh_time=datetime.now(),
                user='test_user'
            )
            
            print(f"创建测试记录: {test_lot}")
            print(f"模型绑定: {LotPriorityConfig.__bind_key__}")
            print(f"表名: {LotPriorityConfig.__tablename__}")
            
            db.session.add(test_lot)
            
            try:
                db.session.commit()
                print("✅ 提交事务成功")
                
                new_count = LotPriorityConfig.query.count()
                print(f"新记录数: {new_count}")
                
                if new_count > existing_count:
                    print("✅ 数据插入成功！")
                    
                    # 查询并删除测试记录
                    inserted_record = LotPriorityConfig.query.filter_by(device='TEST_DEVICE_002').first()
                    if inserted_record:
                        print(f"✅ 查询到插入的记录: {inserted_record}")
                        db.session.delete(inserted_record)
                        db.session.commit()
                        print("🗑️  删除测试记录")
                else:
                    print("❌ 数据插入失败，记录数未增加")
                    
            except Exception as e:
                db.session.rollback()
                print(f"❌ 提交事务失败: {e}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_connection():
    """测试数据库连接配置"""
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            print("🔧 检查数据库配置...")
            
            # 检查配置
            print(f"主数据库URI: {app.config.get('SQLALCHEMY_DATABASE_URI')}")
            print(f"数据库绑定: {app.config.get('SQLALCHEMY_BINDS')}")
            
            # 检查数据库引擎
            from app import db
            
            try:
                # 主数据库引擎
                main_engine = db.get_engine()
                print(f"主数据库引擎: {main_engine.url}")
                
                # system数据库引擎
                system_engine = db.get_engine(bind='system')
                print(f"system数据库引擎: {system_engine.url}")
                
                # 测试连接
                with main_engine.connect() as conn:
                    result = conn.execute("SELECT 1")
                    print("✅ 主数据库连接正常")
                
                with system_engine.connect() as conn:
                    result = conn.execute("SELECT 1")
                    print("✅ system数据库连接正常")
                    
            except Exception as e:
                print(f"❌ 数据库连接测试失败: {e}")
                
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")

if __name__ == "__main__":
    print("🧪 数据库插入测试工具")
    print("=" * 50)
    
    # 1. 测试数据库连接配置
    test_database_connection()
    
    print("\n" + "=" * 50)
    
    # 2. 测试数据库插入
    test_database_insert()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
