#!/usr/bin/env python3
"""
测试Excel导入功能
验证产品优先级配置和批次优先级配置的Excel导入是否正常工作
"""

import os
import sys
import pandas as pd
from datetime import datetime

def create_test_excel_files():
    """创建测试用的Excel文件"""
    print("📝 创建测试Excel文件...")
    
    # 创建测试目录
    test_dir = "test_excel_files"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 1. 创建产品优先级配置测试文件
    device_data = {
        'DEVICE': ['IWO521DFND-M001_TR1', 'IWO130-EWDGR8#TR', 'IWO321DFND-M001_LR1'],
        'PRIORITY': [1, 2, 3],
        'FROM_TIME': ['2025-06-23 12:00:00', '2025-06-23 13:00:00', '2025-06-23 14:00:00'],
        'END_TIME': ['2025-06-24 12:00:00', '2025-06-24 13:00:00', '2025-06-24 14:00:00'],
        'REFRESH_TIME': ['2025-06-23 10:00:00', '2025-06-23 11:00:00', '2025-06-23 12:00:00'],
        'USER': ['admin', 'admin', 'admin']
    }
    
    device_df = pd.DataFrame(device_data)
    device_file = os.path.join(test_dir, 'test_device_priority_config.xlsx')
    device_df.to_excel(device_file, index=False)
    print(f"✅ 创建产品优先级配置测试文件: {device_file}")
    
    # 2. 创建批次优先级配置测试文件
    lot_data = {
        'DEVICE': ['IWO521DFND-M001_TR1', 'IWO130-EWDGR8#TR', 'IWO321DFND-M001_LR1'],
        'STAGE': ['FT', 'CP', 'FT'],
        'PRIORITY': [1, 2, 3],
        'REFRESH_TIME': ['2025-06-23 10:00:00', '2025-06-23 11:00:00', '2025-06-23 12:00:00'],
        'USER': ['admin', 'admin', 'admin']
    }
    
    lot_df = pd.DataFrame(lot_data)
    lot_file = os.path.join(test_dir, 'test_lot_priority_config.xlsx')
    lot_df.to_excel(lot_file, index=False)
    print(f"✅ 创建批次优先级配置测试文件: {lot_file}")
    
    return device_file, lot_file

def create_invalid_test_files():
    """创建无效的测试文件用于验证错误处理"""
    print("📝 创建无效测试文件...")
    
    test_dir = "test_excel_files"
    
    # 1. 错误的文件名（不包含关键字）
    invalid_data = {
        'DEVICE': ['TEST_DEVICE'],
        'PRIORITY': [1]
    }
    
    invalid_df = pd.DataFrame(invalid_data)
    invalid_file = os.path.join(test_dir, 'invalid_filename.xlsx')
    invalid_df.to_excel(invalid_file, index=False)
    print(f"⚠️  创建无效文件名测试文件: {invalid_file}")
    
    # 2. 缺少必填字段的文件
    missing_fields_data = {
        'DEVICE': ['TEST_DEVICE'],
        'DESCRIPTION': ['Missing priority field']
    }
    
    missing_df = pd.DataFrame(missing_fields_data)
    missing_file = os.path.join(test_dir, 'device_missing_fields.xlsx')
    missing_df.to_excel(missing_file, index=False)
    print(f"⚠️  创建缺少字段测试文件: {missing_file}")
    
    return invalid_file, missing_file

def print_test_instructions():
    """打印测试说明"""
    print("\n" + "="*60)
    print("🧪 Excel导入功能测试说明")
    print("="*60)
    
    print("\n📋 测试步骤:")
    print("1. 启动APS系统: python app.py")
    print("2. 访问产品优先级配置页面:")
    print("   http://127.0.0.1:5000/api/v3/universal/devicepriorityconfig")
    print("3. 访问批次优先级配置页面:")
    print("   http://127.0.0.1:5000/api/v3/universal/lotpriorityconfig")
    
    print("\n✅ 正确的测试文件:")
    print("- test_excel_files/test_device_priority_config.xlsx")
    print("- test_excel_files/test_lot_priority_config.xlsx")
    
    print("\n❌ 错误的测试文件（用于验证错误处理）:")
    print("- test_excel_files/invalid_filename.xlsx")
    print("- test_excel_files/device_missing_fields.xlsx")
    
    print("\n🔍 验证要点:")
    print("1. Excel导入按钮只在优先级配置页面显示")
    print("2. 单文件上传限制生效")
    print("3. 文件名关键字验证正常")
    print("4. 必填字段验证正常")
    print("5. 数据导入到正确的数据库表 (aps_system)")
    
    print("\n📊 数据库验证:")
    print("- 检查 aps_system.devicepriorityconfig 表")
    print("- 检查 aps_system.lotpriorityconfig 表")
    
    print("\n🎯 预期结果:")
    print("✅ 正确文件导入成功，显示导入记录数")
    print("❌ 错误文件被拒绝，显示相应错误信息")
    print("🔄 导入成功后页面自动刷新显示新数据")

def main():
    """主函数"""
    print("🚀 Excel导入功能测试工具")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 创建测试文件
        device_file, lot_file = create_test_excel_files()
        invalid_file, missing_file = create_invalid_test_files()
        
        print(f"\n📁 测试文件创建完成，共4个文件:")
        print(f"   ✅ {device_file}")
        print(f"   ✅ {lot_file}")
        print(f"   ⚠️  {invalid_file}")
        print(f"   ⚠️  {missing_file}")
        
        # 打印测试说明
        print_test_instructions()
        
        print("\n🎉 测试准备完成！请按照上述说明进行手动测试。")
        
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
