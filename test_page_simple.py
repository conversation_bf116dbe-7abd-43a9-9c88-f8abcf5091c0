#!/usr/bin/env python
"""
简单的页面测试脚本
"""

import requests

def test_page():
    url = "http://localhost:5000/api/v3/page/eqp_status"
    try:
        response = requests.get(url, timeout=10)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"响应内容: {response.text[:500]}...")
        else:
            print("页面加载成功！")
            # 检查关键内容
            content = response.text
            if "API v3" in content:
                print("✅ 包含API v3标识")
            if "v3-badge" in content:
                print("✅ 包含v3样式")
            if "业务键" in content:
                print("✅ 包含字段标识")
                
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_page() 