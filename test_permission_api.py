#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_permission_api():
    """测试权限设置API"""
    base_url = "http://127.0.0.1:5000"
    
    # 首先登录获取session
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    session = requests.Session()
    
    print("1. 测试登录...")
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code == 200:
        print("✅ 登录成功")
    else:
        print("❌ 登录失败")
        return
    
    # 测试获取用户权限
    print("\n2. 测试获取bonnie用户权限...")
    get_response = session.get(f"{base_url}/api/v2/auth/users/bonnie/permissions")
    print(f"获取权限状态码: {get_response.status_code}")
    
    if get_response.status_code == 200:
        permissions = get_response.json()
        print(f"✅ 获取权限成功: {permissions}")
        
        # 测试更新用户权限
        print("\n3. 测试更新bonnie用户权限...")
        new_permissions = [1, 2, 7, 8, 10, 11]  # 给bonnie一些基本权限
        update_data = {
            "permissions": new_permissions
        }
        
        update_response = session.put(
            f"{base_url}/api/v2/auth/users/bonnie/permissions",
            json=update_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"更新权限状态码: {update_response.status_code}")
        
        if update_response.status_code == 200:
            result = update_response.json()
            print(f"✅ 更新权限成功: {result}")
            
            # 再次获取验证
            print("\n4. 验证权限更新...")
            verify_response = session.get(f"{base_url}/api/v2/auth/users/bonnie/permissions")
            if verify_response.status_code == 200:
                updated_permissions = verify_response.json()
                print(f"✅ 验证成功，更新后的权限: {updated_permissions}")
                
                if set(updated_permissions) == set(new_permissions):
                    print("✅ 权限更新验证通过！")
                else:
                    print(f"❌ 权限更新验证失败！期望: {new_permissions}, 实际: {updated_permissions}")
            else:
                print(f"❌ 验证权限失败: {verify_response.status_code}")
        else:
            try:
                error_info = update_response.json()
                print(f"❌ 更新权限失败: {error_info}")
            except:
                print(f"❌ 更新权限失败: {update_response.text}")
    else:
        try:
            error_info = get_response.json()
            print(f"❌ 获取权限失败: {error_info}")
        except:
            print(f"❌ 获取权限失败: {get_response.text}")

if __name__ == '__main__':
    test_permission_api() 