#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_permission_fix():
    """测试权限修复效果"""
    base_url = "http://127.0.0.1:5000"
    
    # 测试数据
    test_permissions = [1, 2, 4, 5, 6, 7, 8, 10, 11, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 40]  # 包含权限ID 40
    
    # 首先登录获取session
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    session = requests.Session()
    
    print("=== 权限修复测试 ===\n")
    
    print("1. 测试管理员登录...")
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ 管理员登录失败")
        return
    print("✅ 管理员登录成功")
    
    print("\n2. 测试获取当前用户信息...")
    user_info_response = session.get(f"{base_url}/api/v2/auth/user/info")
    print(f"获取用户信息状态码: {user_info_response.status_code}")
    
    if user_info_response.status_code == 200:
        user_info = user_info_response.json()
        print(f"✅ 当前用户: {user_info['username']}, 角色: {user_info['role']}")
    else:
        print("❌ 获取用户信息失败")
        return
    
    print("\n3. 测试包含权限ID 40的权限设置...")
    update_data = {
        "permissions": test_permissions
    }
    
    update_response = session.put(
        f"{base_url}/api/v2/auth/users/boss/permissions",
        json=update_data,
        headers={'Content-Type': 'application/json'}
    )
    print(f"更新权限状态码: {update_response.status_code}")
    
    if update_response.status_code == 200:
        result = update_response.json()
        print(f"✅ 权限更新成功: {result}")
        
        # 验证权限更新
        print("\n4. 验证权限更新...")
        verify_response = session.get(f"{base_url}/api/v2/auth/users/boss/permissions")
        if verify_response.status_code == 200:
            updated_permissions = verify_response.json()
            print(f"✅ 验证成功，更新后的权限: {updated_permissions}")
            
            if set(updated_permissions) == set(test_permissions):
                print("✅ 权限ID 40问题已修复！")
            else:
                print(f"❌ 权限验证失败！期望: {test_permissions}, 实际: {updated_permissions}")
        else:
            print(f"❌ 验证权限失败: {verify_response.status_code}")
    else:
        try:
            error_info = update_response.json()
            print(f"❌ 更新权限失败: {error_info}")
        except:
            print(f"❌ 更新权限失败: {update_response.text}")
    
    print("\n5. 测试非管理员用户访问用户管理...")
    # 登录普通用户
    op_login_data = {
        'username': 'op',
        'password': '123'
    }
    
    op_session = requests.Session()
    op_login_response = op_session.post(f"{base_url}/auth/login", data=op_login_data)
    
    if op_login_response.status_code == 200:
        print("✅ 普通用户op登录成功")
        
        # 测试访问用户列表
        users_response = op_session.get(f"{base_url}/api/auth/users")
        print(f"普通用户访问用户列表状态码: {users_response.status_code}")
        
        if users_response.status_code == 403:
            print("✅ 普通用户无法访问用户管理API（权限控制正常）")
        elif users_response.status_code == 200:
            print("❌ 普通用户可以访问用户管理API（权限控制有问题）")
        else:
            print(f"⚠️ 意外的状态码: {users_response.status_code}")
    else:
        print("❌ 普通用户登录失败")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_permission_fix() 