import requests

def test_with_login():
    """使用admin账号登录后访问页面"""
    session = requests.Session()
    
    try:
        # 第1步：登录admin账号
        print("🔐 正在登录admin账号...")
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post('http://localhost:5000/auth/login', data=login_data, timeout=10)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print("✅ 登录成功!")
        else:
            print("❌ 登录失败")
            return
        
        # 第2步：访问API v3页面
        print("\n📊 访问API v3页面...")
        url = 'http://localhost:5000/api/v3/page/eqp_status'
        response = session.get(url, timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 500:
            print("❌ 页面访问失败:")
            print(response.text[:500])
        elif response.status_code == 200:
            print("✅ 页面加载成功!")
            if 'API v3' in response.text:
                print("✅ 包含API v3标识")
            if '设备状态管理' in response.text:
                print("✅ 页面标题正确")
            if 'table_name' in response.text or 'eqp_status' in response.text:
                print("✅ 表名信息正确")
        
        # 第3步：测试API接口
        print("\n🔧 测试API接口...")
        api_response = session.get('http://localhost:5000/api/v3/tables/eqp_status/data?page=1&per_page=5', timeout=10)
        print(f"API状态码: {api_response.status_code}")
        
        if api_response.status_code == 200:
            api_data = api_response.json()
            if api_data.get('success'):
                print(f"✅ API工作正常: {len(api_data.get('data', []))} 条记录")
            else:
                print(f"❌ API返回错误: {api_data.get('error')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_with_login() 