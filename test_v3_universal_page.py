#!/usr/bin/env python
"""
API v3 通用页面功能测试脚本
验证新的universal_v3.html设计是否正常工作
"""

import requests
import json
import time
from datetime import datetime

def test_v3_universal_pages():
    """测试API v3通用页面功能"""
    base_url = "http://localhost:5000"
    
    # 测试页面列表
    test_pages = [
        {'name': 'eqp_status', 'display': '设备状态管理'},
        {'name': 'et_uph_eqp', 'display': 'UPH设备管理'},
        {'name': 'et_ft_test_spec', 'display': '测试规格管理'},
        {'name': 'ct', 'display': '产品周期管理'}
    ]
    
    print(f"🚀 开始测试API v3通用页面功能")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 等待应用启动
    print("⏳ 等待应用启动...")
    time.sleep(3)
    
    # 1. 测试API v3基础功能
    print("\n1️⃣ 测试API v3基础功能")
    try:
        response = requests.get(f"{base_url}/api/v3/tables", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ 获取支持表列表成功: {len(data['tables'])}个表")
            else:
                print(f"   ❌ API返回错误: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 2. 测试每个通用页面
    print("\n2️⃣ 测试通用页面访问")
    for page in test_pages:
        table_name = page['name']
        display_name = page['display']
        
        print(f"\n   🔍 测试 {display_name} ({table_name})")
        
        try:
            # 测试页面访问
            page_url = f"{base_url}/api/v3/page/{table_name}"
            response = requests.get(page_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                # 检查关键内容
                checks = [
                    ('API v3', 'API v3标识'),
                    ('v3-badge', 'v3徽章样式'),
                    ('universal_v3.html', '使用正确模板'),
                    ('动态字段管理', '动态字段功能'),
                    ('刷新', '刷新按钮'),
                    ('导出', '导出按钮'),
                    ('验证字段', '字段验证功能')
                ]
                
                success_count = 0
                for check, desc in checks:
                    if check in content:
                        success_count += 1
                        print(f"      ✅ {desc}")
                    else:
                        print(f"      ❌ 缺少{desc}")
                
                print(f"      📊 页面完整性: {success_count}/{len(checks)} ({success_count/len(checks)*100:.1f}%)")
                
            else:
                print(f"      ❌ 页面访问失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ 请求异常: {e}")
    
    # 3. 测试API数据获取
    print("\n3️⃣ 测试API数据获取")
    for page in test_pages:
        table_name = page['name']
        print(f"\n   📊 测试 {table_name} 数据API")
        
        try:
            # 测试表字段信息
            columns_url = f"{base_url}/api/v3/tables/{table_name}/columns"
            response = requests.get(columns_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    columns = data['columns']
                    print(f"      ✅ 字段信息: {len(columns)}个字段")
                    
                    # 显示表信息
                    if 'table_info' in data:
                        info = data['table_info']
                        print(f"      📝 业务键: {info.get('business_key', '未识别')}")
                        print(f"      🔑 主键: {info.get('primary_key', 'id')}")
                else:
                    print(f"      ❌ 获取字段失败: {data.get('error')}")
            else:
                print(f"      ❌ 字段API失败: HTTP {response.status_code}")
            
            # 测试数据获取
            data_url = f"{base_url}/api/v3/tables/{table_name}/data?page=1&per_page=5"
            response = requests.get(data_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    records = data['data']
                    total = data['total']
                    print(f"      ✅ 数据获取: {len(records)}/{total} 条记录")
                else:
                    print(f"      ❌ 获取数据失败: {data.get('error')}")
            else:
                print(f"      ❌ 数据API失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ API请求异常: {e}")
    
    # 4. 测试字段验证功能
    print("\n4️⃣ 测试字段验证功能")
    for page in test_pages[:2]:  # 只测试前两个，节省时间
        table_name = page['name']
        print(f"\n   🔍 验证 {table_name} 字段映射")
        
        try:
            validate_url = f"{base_url}/api/v3/tables/{table_name}/validate"
            response = requests.get(validate_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    match_rate = data.get('match_rate', 0)
                    status = data.get('status', 'unknown')
                    print(f"      ✅ 验证成功: {match_rate}% 匹配率 ({status})")
                else:
                    print(f"      ❌ 验证失败: {data.get('error')}")
            else:
                print(f"      ❌ 验证API失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ 验证请求异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎉 API v3通用页面测试完成!")
    print("\n✨ 新设计特色:")
    print("   🎨 完全借鉴base_resource.html的专业UI设计")
    print("   🚀 保持API v3动态字段管理架构")
    print("   🔧 统一的操作界面和交互体验")
    print("   📊 智能字段类型识别和高亮显示")
    print("   ⚡ 优化的加载性能和缓存机制")

if __name__ == "__main__":
    test_v3_universal_pages() 