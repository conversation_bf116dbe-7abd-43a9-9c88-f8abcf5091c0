#!/usr/bin/env python3
"""
测试Web API的导入功能
"""

import requests
import json
import os

def test_web_import():
    """测试Web API的导入功能"""
    
    # 获取测试目录的绝对路径
    test_dir = os.path.abspath('test_import')
    print(f"测试目录: {test_dir}")
    
    # 准备请求数据
    data = {
        'path': test_dir
    }
    
    # 发送POST请求
    url = 'http://127.0.0.1:5000/api/production/import-from-directory'
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"🚀 发送请求到: {url}")
        print(f"📦 请求数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 导入成功!")
            print(f"📈 结果详情: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 导入失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == '__main__':
    test_web_import()
