#!/usr/bin/env python3
"""
测试Web API的导入功能（带登录）
"""

import requests
import json
import os

def test_web_import_with_login():
    """测试Web API的导入功能（先登录）"""

    # 创建会话
    session = requests.Session()

    # 1. 先登录
    login_url = 'http://127.0.0.1:5000/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }

    print("🔐 正在登录...")
    login_response = session.post(login_url, data=login_data)
    print(f"登录状态码: {login_response.status_code}")

    if login_response.status_code != 200:
        print("❌ 登录失败")
        return

    print("✅ 登录成功")

    # 2. 测试导入功能
    test_dir = os.path.abspath('test_import')
    print(f"测试目录: {test_dir}")

    # 准备请求数据
    data = {
        'path': test_dir
    }

    # 发送POST请求
    url = 'http://127.0.0.1:5000/api/production/import-from-directory'
    headers = {
        'Content-Type': 'application/json'
    }

    try:
        print(f"🚀 发送导入请求到: {url}")
        print(f"📦 请求数据: {json.dumps(data, indent=2)}")

        response = session.post(url, json=data, headers=headers)

        print(f"📊 响应状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 导入成功!")
                print(f"📈 结果详情: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"📄 响应内容（非JSON）: {response.text[:500]}...")
        else:
            print(f"❌ 导入失败: HTTP {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")

    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == '__main__':
    test_web_import_with_login()
