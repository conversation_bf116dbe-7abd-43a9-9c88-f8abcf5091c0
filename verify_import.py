#!/usr/bin/env python3
import pymysql

try:
    conn = pymysql.connect(
        host='127.0.0.1', 
        user='root', 
        password='WWWwww123!', 
        database='aps'
    )
    cursor = conn.cursor()
    
    # 检查表是否存在
    cursor.execute('SHOW TABLES LIKE "sheet1"')
    result = cursor.fetchall()
    
    if len(result) > 0:
        print('✅ 表 sheet1 存在')
        
        # 查看表结构
        cursor.execute('DESCRIBE sheet1')
        columns = cursor.fetchall()
        print('\n📋 表结构:')
        for col in columns:
            print(f"  - {col[0]}: {col[1]}")
        
        # 查看数据
        cursor.execute('SELECT * FROM sheet1')
        rows = cursor.fetchall()
        print(f'\n📊 数据行数: {len(rows)}')
        print('\n📄 数据内容:')
        for i, row in enumerate(rows, 1):
            print(f"  行{i}: {row}")
    else:
        print('❌ 表 sheet1 不存在')
    
    conn.close()
    print('\n✅ 验证完成')
    
except Exception as e:
    print(f'❌ 验证失败: {e}')
