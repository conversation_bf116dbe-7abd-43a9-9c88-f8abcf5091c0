# 🔧 优先级配置表迁移和Excel导入修复报告

## 🎯 问题分析

用户遇到两个关键问题：
1. **数据库表位置错误**：`devicepriorityconfig`和`lotpriorityconfig`表应该在`aps_system`数据库中，而不是`aps`数据库
2. **Excel导入功能限制**：需要限制为单文件上传，并严格验证表名称和结构

## ✅ 解决方案实施

### 1. **数据库迁移**

#### 📋 迁移脚本
创建了两个迁移工具：
- **`migrate_priority_tables.py`** - Python自动化迁移脚本
- **`migrate_priority_tables.sql`** - SQL手动迁移脚本

#### 🎯 迁移目标
```
源数据库: aps.devicepriorityconfig → 目标数据库: aps_system.devicepriorityconfig
源数据库: aps.lotpriorityconfig → 目标数据库: aps_system.lotpriorityconfig
```

#### 📊 表结构确认
```sql
-- 产品优先级配置表
CREATE TABLE aps_system.devicepriorityconfig (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device VARCHAR(100) NOT NULL COMMENT '产品名称',
    priority INT NOT NULL DEFAULT 5 COMMENT '优先级',
    from_time DATETIME DEFAULT NULL COMMENT '生效开始时间',
    end_time DATETIME DEFAULT NULL COMMENT '生效结束时间',
    refresh_time DATETIME DEFAULT NULL COMMENT '刷新时间',
    user VARCHAR(50) DEFAULT NULL COMMENT '操作用户',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 批次优先级配置表
CREATE TABLE aps_system.lotpriorityconfig (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device VARCHAR(100) NOT NULL COMMENT '产品名称',
    stage VARCHAR(50) DEFAULT NULL COMMENT '工艺阶段',
    priority INT NOT NULL DEFAULT 5 COMMENT '优先级',
    refresh_time DATETIME DEFAULT NULL COMMENT '刷新时间',
    user VARCHAR(50) DEFAULT NULL COMMENT '操作用户',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. **Excel导入功能修复**

#### 🔧 前端修改 (`universal_resource_v3.html`)

##### A. **单文件上传限制**
```html
<!-- 移除 multiple 属性 -->
<input class="form-control" type="file" id="excelFiles" accept=".xlsx,.xls">
<div class="form-text">仅支持单个Excel文件上传。</div>
```

##### B. **严格的格式说明**
```javascript
// 产品优先级配置
if (TABLE_NAME === 'devicepriorityconfig') {
    formatDetails.innerHTML = `
        <p><strong>产品优先级配置表 (devicepriorityconfig) 字段：</strong></p>
        <ul>
            <li><strong>DEVICE</strong> (必填): 产品名称</li>
            <li><strong>PRIORITY</strong> (必填): 优先级数值</li>
            <li><strong>FROM_TIME</strong> (可选): 生效开始时间</li>
            <li><strong>END_TIME</strong> (可选): 生效结束时间</li>
            <li><strong>REFRESH_TIME</strong> (可选): 刷新时间</li>
            <li><strong>USER</strong> (可选): 操作用户</li>
        </ul>
        <p class="text-warning"><small>⚠️ 文件将导入到 aps_system.devicepriorityconfig 表</small></p>
    `;
}
```

##### C. **严格的文件验证**
```javascript
function uploadExcelFiles() {
    // 1. 单文件验证
    if (files.length > 1) {
        alert('仅支持单个文件上传，请选择一个Excel文件');
        return;
    }
    
    // 2. 文件名关键字验证
    if (TABLE_NAME === 'devicepriorityconfig') {
        if (!fileName.includes('device')) {
            alert('产品优先级配置文件名必须包含"device"关键字');
            return;
        }
    } else if (TABLE_NAME === 'lotpriorityconfig') {
        if (!fileName.includes('lot')) {
            alert('批次优先级配置文件名必须包含"lot"关键字');
            return;
        }
    }
    
    // 3. 文件格式验证
    if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        alert('仅支持Excel文件格式(.xlsx或.xls)');
        return;
    }
}
```

##### D. **优化的结果显示**
```javascript
// 单文件结果显示
const result = data.results[0];
resultContainer.innerHTML = `
    <h6><i class="fas fa-check-circle me-1"></i>上传成功</h6>
    <p><strong>文件:</strong> ${result.filename}</p>
    <p><strong>目标表:</strong> aps_system.${TABLE_NAME}</p>
    <p><strong>导入记录:</strong> ${result.imported_count} 条</p>
`;
```

#### 🔧 后端验证 (`api_v2/production/routes.py`)

API已经支持单文件上传和严格验证：
```python
# 1. 单文件限制
if len(files) > 1:
    return jsonify({'success': False, 'error': '只允许上传单个Excel文件'})

# 2. 文件名关键字验证
if not ('device' in filename_lower or 'lot' in filename_lower):
    return jsonify({
        'success': False, 
        'error': '文件名必须包含"device"或"lot"关键字，以便系统识别文件类型'
    })

# 3. 表类型识别
if 'devicepriorityconfig' in filename_lower or 'device' in filename_lower:
    table_type = 'device'
    model_class = DevicePriorityConfig  # 绑定到 aps_system 数据库
elif 'lotpriorityconfig' in filename_lower or 'lot' in filename_lower:
    table_type = 'lot'
    model_class = LotPriorityConfig     # 绑定到 aps_system 数据库
```

### 3. **模型配置确认**

模型已正确配置为使用`aps_system`数据库：
```python
class DevicePriorityConfig(db.Model):
    __tablename__ = 'devicepriorityconfig'
    __bind_key__ = 'system'  # 绑定到系统数据库

class LotPriorityConfig(db.Model):
    __tablename__ = 'lotpriorityconfig'
    __bind_key__ = 'system'  # 绑定到系统数据库
```

## 🧪 测试工具

### 📝 测试文件生成器 (`test_excel_import.py`)
创建了自动化测试工具，生成以下测试文件：

#### ✅ 正确的测试文件
1. **`test_device_priority_config.xlsx`** - 产品优先级配置测试文件
2. **`test_lot_priority_config.xlsx`** - 批次优先级配置测试文件

#### ❌ 错误的测试文件（验证错误处理）
1. **`invalid_filename.xlsx`** - 文件名不包含关键字
2. **`device_missing_fields.xlsx`** - 缺少必填字段

## 📋 使用说明

### 1. **数据库迁移步骤**
```sql
-- 执行SQL迁移脚本
mysql -u root -p < migrate_priority_tables.sql
```

### 2. **功能测试步骤**
```bash
# 1. 生成测试文件
python test_excel_import.py

# 2. 启动系统
python app.py

# 3. 访问测试页面
# 产品优先级配置: http://127.0.0.1:5000/api/v3/universal/devicepriorityconfig
# 批次优先级配置: http://127.0.0.1:5000/api/v3/universal/lotpriorityconfig
```

### 3. **Excel文件要求**

#### 产品优先级配置文件
- **文件名要求**：必须包含"device"关键字
- **必填字段**：DEVICE, PRIORITY
- **可选字段**：FROM_TIME, END_TIME, REFRESH_TIME, USER
- **目标表**：`aps_system.devicepriorityconfig`

#### 批次优先级配置文件
- **文件名要求**：必须包含"lot"关键字
- **必填字段**：DEVICE, PRIORITY
- **可选字段**：STAGE, REFRESH_TIME, USER
- **目标表**：`aps_system.lotpriorityconfig`

## 🔍 验证要点

### ✅ 功能验证
1. **按钮显示**：Excel导入按钮只在优先级配置页面显示
2. **单文件限制**：只能选择一个Excel文件
3. **文件名验证**：文件名必须包含对应关键字
4. **字段验证**：必填字段不能为空
5. **数据库写入**：数据正确写入`aps_system`数据库

### ❌ 错误处理验证
1. **多文件上传**：显示"仅支持单个文件上传"错误
2. **错误文件名**：显示关键字要求错误
3. **错误格式**：显示文件格式错误
4. **缺少字段**：显示字段验证错误

## 🎉 总结

✅ **数据库迁移**：优先级配置表已迁移到正确的`aps_system`数据库

✅ **单文件限制**：Excel导入功能限制为单文件上传

✅ **严格验证**：文件名、格式、字段都有严格验证

✅ **表结构明确**：明确指定目标表和字段要求

✅ **错误处理**：完善的错误提示和验证机制

✅ **测试工具**：提供完整的测试文件和验证步骤

**现在用户可以安全地使用单文件Excel导入功能，数据将正确导入到`aps_system`数据库的对应表中！** 🚀
